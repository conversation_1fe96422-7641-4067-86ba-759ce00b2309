@echo off
"C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86_64 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging14871377185649545946\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46ed95a07aaabdffd864bffdea2e8e32\\transformed\\react-android-0.76.7-release\\prefab" ^
  "F:\\Apps\\Seller\\my-app\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\4p3q1l2i" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d737b571aa14fa99550749c5c6ccc7b2\\transformed\\hermes-android-0.76.7-release\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cc6538d1dfd58568ef78eef8c5ee9673\\transformed\\fbjni-0.6.0\\prefab"
