<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<animated-selector
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        tools:ignore="NewApi">
    <item
            android:id="@+id/checked"
            android:state_checked="true"
            android:drawable="@drawable/btn_checkbox_checked_mtrl" />
    <item
            android:id="@+id/unchecked"
            android:drawable="@drawable/btn_checkbox_unchecked_mtrl" />
    <transition
            android:fromId="@+id/unchecked"
            android:toId="@+id/checked"
            android:drawable="@drawable/btn_checkbox_unchecked_to_checked_mtrl_animation" />
    <transition
            android:fromId="@+id/checked"
            android:toId="@+id/unchecked"
            android:drawable="@drawable/btn_checkbox_checked_to_unchecked_mtrl_animation" />
</animated-selector>
