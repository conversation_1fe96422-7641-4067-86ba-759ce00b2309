# C/C++ build system timings
generate_cxx_metadata
  [gap of 57ms]
  create-invalidation-state 83ms
generate_cxx_metadata completed in 147ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 202ms]
  create-invalidation-state 114ms
  generate-prefab-packages
    exec-prefab 764ms
    [gap of 311ms]
  generate-prefab-packages completed in 1078ms
  execute-generate-process
    exec-configure 1124ms
    [gap of 423ms]
  execute-generate-process completed in 1548ms
  [gap of 12ms]
  remove-unexpected-so-files 108ms
  [gap of 138ms]
generate_cxx_metadata completed in 3231ms

