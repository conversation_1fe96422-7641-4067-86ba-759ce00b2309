import Caloriescomponent from "../../../../component/Caloriescomponent";
import DropDownComponent, { DropDownComponentTags } from "../../../../component/DropDownComponent";
import PIcon from "@/assets/icons/%.svg";
import React, { useContext, useEffect } from "react";
import TextAreaComponent from "../../../../component/TextAreaComponent";
import useGetApiData from "../../../../hooks/useGetApiData";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import { useRoute } from "@react-navigation/native";
import { router, useLocalSearchParams } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Alert, Text, TextInput, TouchableOpacity, View } from "react-native";
import { Dropdown } from "react-native-element-dropdown";
import { GlobalPagenumber } from "./_layout";

const page2 = () => {
  const {page, setpage} = useContext(GlobalPagenumber);
  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors},
  } = useForm();
  const data = useLocalSearchParams();

  const {mutate: AddproductInformation} = useTenStackMutateD({
    endpoint: "auth/productIngredients",
    invalidateQueriesKey: ["productDetails"],
  });
  const {mutate: AddproductIngredients} = useTenStackMutateD({
    endpoint: "auth/productInformation",
    invalidateQueriesKey: ["productDetails"],
  });
  const {data: TagsList, isLoading} = useGetApiData({
    endpoint: "/productTagList ",
  });
  const route = useRoute();
  const tags = [
    {label: "Vegetarian", value: "1"},
    {label: "Non-Vegetarian", value: "2"},
    {label: "Jaivik Bharat Logo", value: "3"},
    {label: "FSSAI Logo", value: "4"},
    {label: "BIS Certification", value: "5"},
    {label: "Cruelty-Free", value: "6"},
    {label: "Vegan", value: "7"},
    {label: "Food Grade Material", value: "8"},
    {label: "Recyclable Packaging", value: "9"},
  ];
  useEffect(() => {
    setpage(route.name);
  }, []);
  return (
    <View className="mt-4">
      <View className="">
        <View className="">
          <Text className="font-[400] text-[18px]">Tag (Optional)</Text>
        </View>

        <View className="mb-4">
          <DropDownComponent
            control={control}
            setvaluefun={setValue}
            name={"Tags"}
            data={tags}
            text={"Select Tags"}
            placeholder={"Select Options"}
            // defaul={"Seller"}
          />
        </View>
      </View>

      <View>
        <Text className="font-[400] text-[18px]">Nutritional Information (Optional)</Text>
      </View>
      <View>
        <Caloriescomponent
          id={data}
          control={control}
          handleSubmit={handleSubmit}
          setValue={setValue}
          errors={errors}
        />
      </View>

      <View className="mt-4">
        <TextAreaComponent
          text={"Ingredients"}
          name={"ingredients"}
          control={control}
          placeholder={"Name the ingredients present in the product separated by “,”."}
        />
      </View>
      <View className="mt-5">
        <TextAreaComponent
          text={"Allergen Information:"}
          control={control}
          name={"Allergeninformation"}
          placeholder={"Name the ingredients present in the product separated by “,”."}
        />
      </View>
      <View className="mt-10">
        <TouchableOpacity
          className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
          onPress={handleSubmit((datas) => {
            const pattern = /^\d+(\.\d{1,2})?$/;
            if (datas?.quantity_0) {
              if (!pattern.test(datas?.quantity_0)) {
                alert("Please enter quantity in format XX.XX (e.g., 25.55)");
                return;
              }
            }
            let success = true;
            AddproductInformation(
              {
                product_id: data.id,
                description: datas.ingredients,
                tag_id: tags.find((tag) => tag.label === datas?.Tags)?.value,
              },
              {
                onError: (val) => {
                  success = false;
                  Alert.alert("Faild", val.message);
                },
              },
            );
            AddproductIngredients(
              {product_id: data.id, description: datas?.Allergeninformation},
              {
                onSuccess: () => {},
                onError: (val) => {
                  success = false;
                  Alert.alert("Faild", val.message);
                },
              },
            );
            if (success) {
              setpage(page + 1);
              router.push({
                pathname: "/home/<USER>/product/page3",
                params: {id: data.id},
              });
            }
          })}
        >
          <Text className="font-[400] text-[#fff] text-[16px]">Next</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export const NutritionalComponent = (props) => {
  const {data, isLoading} = useGetApiData({
    endpoint: "/productNeutritionList",
  });
  useEffect(() => {}, []);
  return (
    <>
      <View className="flex-row space-x-4 items-center">
        {isLoading ? (
          <></>
        ) : (
          <>
            <View className="w-[130px]">
              <Controller
                name={props?.name1}
                control={props?.control}
                render={({field: {onChange, value}}) => {
                  return (
                    <>
                      <Dropdown
                        data={data?.data}
                        placeholder={"nutrient"}
                        containerStyle={{}}
                        placeholderStyle={{
                          color: "#B3B3B3",
                          textAlignVertical: "center",
                        }}
                        style={{
                          height: 40,
                        }}
                        labelField="name"
                        valueField="name"
                        value={value}
                        onChange={(item) => {
                          onChange(item.name);
                        }}
                        className="border-[1px] border-[#bec9e1] rounded-[4px] px-3 mt-2"
                      />
                    </>
                  );
                }}
              />
            </View>
          </>
        )}

        <View className="w-[130px]">
          <Controller
            name={props?.name2}
            control={props?.control}
            rules={
              {
                // required: {value: true, message: "Please Enter Quantity"},
                // pattern: {
                //   value: /^\d+(\.\d{1,2})?$/,
                //   message: "Please enter quantity in format XX.X (e.g., 25.5)",
                // },
              }
            }
            render={({field: {onChange, value}}) => {
              return (
                <>
                  <TextInput
                    onChangeText={onChange}
                    value={value}
                    placeholder="Quantity"
                    keyboardType="numeric"
                    placeholderTextColor={"#B3B3B3"}
                    className="border-[1px] border-[#ACB9D5] flex-1 rounded-[4px] pl-2 mt-2 h-[40px]"
                  />
                </>
              );
            }}
          />
        </View>
        <View>
          <Text className="">
            <PIcon />
          </Text>
        </View>
      </View>
    </>
  );
};

export default page2;
