com.dhass.myapp:xml/standalone_badge_offset = 0x7f15000c
com.dhass.myapp:xml/standalone_badge_gravity_top_start = 0x7f15000b
com.dhass.myapp:xml/standalone_badge_gravity_bottom_start = 0x7f15000a
com.dhass.myapp:xml/standalone_badge = 0x7f150008
com.dhass.myapp:xml/image_share_filepaths = 0x7f150004
com.dhass.myapp:styleable/WalletFragmentStyle = 0x7f14009e
com.dhass.myapp:styleable/WalletFragmentOptions = 0x7f14009d
com.dhass.myapp:styleable/ViewBackgroundHelper = 0x7f14009a
com.dhass.myapp:styleable/Transition = 0x7f140097
com.dhass.myapp:styleable/Transform = 0x7f140096
com.dhass.myapp:styleable/Toolbar = 0x7f140094
com.dhass.myapp:styleable/TextInputLayout = 0x7f140092
com.dhass.myapp:styleable/TextInputEditText = 0x7f140091
com.dhass.myapp:styleable/TextAppearance = 0x7f140090
com.dhass.myapp:styleable/TabLayout = 0x7f14008f
com.dhass.myapp:styleable/SwitchCompat = 0x7f14008c
com.dhass.myapp:styleable/SwipeRefreshLayout = 0x7f14008b
com.dhass.myapp:styleable/StyledPlayerControlView = 0x7f140089
com.dhass.myapp:styleable/StateSet = 0x7f140088
com.dhass.myapp:styleable/StateListDrawableItem = 0x7f140087
com.dhass.myapp:styleable/State = 0x7f140085
com.dhass.myapp:styleable/Snackbar = 0x7f140082
com.dhass.myapp:styleable/Slider = 0x7f140081
com.dhass.myapp:styleable/SimpleDraweeView = 0x7f140080
com.dhass.myapp:styleable/SearchView = 0x7f14007c
com.dhass.myapp:styleable/ScrollingViewBehavior_Layout = 0x7f14007b
com.dhass.myapp:styleable/RecyclerView = 0x7f140079
com.dhass.myapp:styleable/RecycleListView = 0x7f140078
com.dhass.myapp:styleable/NavigationView = 0x7f14006d
com.dhass.myapp:styleable/NavigationRailView = 0x7f14006c
com.dhass.myapp:styleable/MotionScene = 0x7f140068
com.dhass.myapp:styleable/MotionLayout = 0x7f140067
com.dhass.myapp:styleable/MenuView = 0x7f140063
com.dhass.myapp:styleable/MenuGroup = 0x7f140061
com.dhass.myapp:styleable/MaterialToolbar = 0x7f140060
com.dhass.myapp:styleable/MaterialTextAppearance = 0x7f14005d
com.dhass.myapp:styleable/MaterialRadioButton = 0x7f14005b
com.dhass.myapp:styleable/MapAttrs = 0x7f140050
com.dhass.myapp:styleable/ListPopupWindow = 0x7f14004e
com.dhass.myapp:styleable/LinearProgressIndicator = 0x7f14004d
com.dhass.myapp:styleable/LinearLayoutCompat = 0x7f14004b
com.dhass.myapp:styleable/Layout = 0x7f14004a
com.dhass.myapp:styleable/KeyTrigger = 0x7f140049
com.dhass.myapp:styleable/KeyFrame = 0x7f140044
com.dhass.myapp:styleable/KeyCycle = 0x7f140043
com.dhass.myapp:styleable/Insets = 0x7f140041
com.dhass.myapp:styleable/ImageFilterView = 0x7f140040
com.dhass.myapp:styleable/GradientColorItem = 0x7f14003f
com.dhass.myapp:styleable/GradientColor = 0x7f14003e
com.dhass.myapp:styleable/GenericDraweeHierarchy = 0x7f14003d
com.dhass.myapp:styleable/ForegroundLinearLayout = 0x7f14003a
com.dhass.myapp:styleable/FontFamilyFont = 0x7f140039
com.dhass.myapp:styleable/FloatingActionButton = 0x7f140035
com.dhass.myapp:styleable/ExtendedFloatingActionButton = 0x7f140033
com.dhass.myapp:styleable/DrawerArrowToggle = 0x7f140031
com.dhass.myapp:styleable/DefaultTimeBar = 0x7f140030
com.dhass.myapp:styleable/CustomWalletTheme = 0x7f14002f
com.dhass.myapp:styleable/CoordinatorLayout = 0x7f14002b
com.dhass.myapp:styleable/ConstraintLayout_Layout = 0x7f140028
com.dhass.myapp:styleable/ColorStateListItem = 0x7f140025
com.dhass.myapp:styleable/ClockHandView = 0x7f140022
com.dhass.myapp:styleable/CircularProgressIndicator = 0x7f140020
com.dhass.myapp:styleable/ChipGroup = 0x7f14001f
com.dhass.myapp:styleable/Chip = 0x7f14001e
com.dhass.myapp:styleable/Tooltip = 0x7f140095
com.dhass.myapp:styleable/Capability = 0x7f14001b
com.dhass.myapp:styleable/BottomNavigationView = 0x7f140018
com.dhass.myapp:styleable/BottomAppBar = 0x7f140017
com.dhass.myapp:styleable/Badge = 0x7f140015
com.dhass.myapp:styleable/AppCompatTheme = 0x7f140012
com.dhass.myapp:styleable/AppBarLayout_Layout = 0x7f14000c
com.dhass.myapp:styleable/AppBarLayoutStates = 0x7f14000b
com.dhass.myapp:styleable/AppBarLayout = 0x7f14000a
com.dhass.myapp:styleable/AnimatedStateListDrawableCompat = 0x7f140007
com.dhass.myapp:styleable/ActivityChooserView = 0x7f140005
com.dhass.myapp:styleable/ActionMode = 0x7f140004
com.dhass.myapp:styleable/ActionMenuView = 0x7f140003
com.dhass.myapp:style/redboxButton = 0x7f130478
com.dhass.myapp:style/Widget.Support.CoordinatorLayout = 0x7f130474
com.dhass.myapp:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f130472
com.dhass.myapp:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f130470
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f13046d
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f13046c
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13046b
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f130469
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f130467
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.Button = 0x7f130466
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker = 0x7f130465
com.dhass.myapp:style/Widget.MaterialComponents.TextView = 0x7f130464
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f130463
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f130462
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f130461
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f130460
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f13045f
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f13045c
com.dhass.myapp:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f13045b
com.dhass.myapp:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f130457
com.dhass.myapp:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f130454
com.dhass.myapp:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f130453
com.dhass.myapp:style/Widget.MaterialComponents.Slider = 0x7f130451
com.dhass.myapp:style/Widget.MaterialComponents.ShapeableImageView = 0x7f130450
com.dhass.myapp:style/Widget.MaterialComponents.ProgressIndicator = 0x7f13044f
com.dhass.myapp:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13044e
com.dhass.myapp:style/Widget.MaterialComponents.NavigationView = 0x7f13044a
com.dhass.myapp:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f130448
com.dhass.myapp:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f130447
com.dhass.myapp:style/Widget.MaterialComponents.NavigationRailView = 0x7f130445
com.dhass.myapp:style/Widget.MaterialComponents.MaterialDivider = 0x7f130444
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f130442
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f130441
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f13043b
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f13043a
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f130436
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f130435
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130434
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f130432
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f130430
com.dhass.myapp:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f13042b
com.dhass.myapp:style/Widget.MaterialComponents.FloatingActionButton = 0x7f130429
com.dhass.myapp:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f130428
com.dhass.myapp:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f130423
com.dhass.myapp:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f130420
com.dhass.myapp:style/Widget.MaterialComponents.Chip.Filter = 0x7f13041d
com.dhass.myapp:style/Widget.MaterialComponents.Chip.Entry = 0x7f13041c
com.dhass.myapp:style/Widget.MaterialComponents.Chip.Choice = 0x7f13041b
com.dhass.myapp:style/Widget.MaterialComponents.Chip.Action = 0x7f13041a
com.dhass.myapp:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f130417
com.dhass.myapp:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f130416
com.dhass.myapp:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f130415
com.dhass.myapp:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f130412
com.dhass.myapp:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f13040f
com.dhass.myapp:style/Widget.MaterialComponents.Button.Icon = 0x7f13040d
com.dhass.myapp:style/Widget.MaterialComponents.Button = 0x7f13040c
com.dhass.myapp:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f13040b
com.dhass.myapp:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f130409
com.dhass.myapp:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f130406
com.dhass.myapp:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f130405
com.dhass.myapp:style/Widget.MaterialComponents.BottomAppBar = 0x7f130404
com.dhass.myapp:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f130401
com.dhass.myapp:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f130400
com.dhass.myapp:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1303ff
com.dhass.myapp:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1303fe
com.dhass.myapp:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1303f1
com.dhass.myapp:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1303f0
com.dhass.myapp:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1303ef
com.dhass.myapp:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1303ee
com.dhass.myapp:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1303ea
com.dhass.myapp:style/Widget.Material3.TabLayout.Secondary = 0x7f1303e6
com.dhass.myapp:style/Widget.Material3.Snackbar.TextView = 0x7f1303e3
com.dhass.myapp:style/Widget.Material3.Snackbar.FullWidth = 0x7f1303e2
com.dhass.myapp:style/Widget.Material3.PopupMenu.Overflow = 0x7f1303df
com.dhass.myapp:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1303de
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1303d8
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1303d7
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1303d4
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1303d3
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1303d2
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1303d1
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Year = 0x7f1303ca
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1303c8
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1303c4
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1303c3
com.dhass.myapp:styleable/DrawerLayout = 0x7f140032
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1303c2
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1303be
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1303bd
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1303bb
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1303ba
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Day = 0x7f1303b9
com.dhass.myapp:style/Widget.Material3.LinearProgressIndicator = 0x7f1303b7
com.dhass.myapp:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1303b6
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1303b4
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1303b3
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1303b1
com.dhass.myapp:styleable/Motion = 0x7f140065
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1303b0
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1303af
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1303ae
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1303ac
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1303aa
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1303a8
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1303a6
com.dhass.myapp:styleable/TabItem = 0x7f14008e
com.dhass.myapp:style/Widget.Material3.DrawerLayout = 0x7f1303a5
com.dhass.myapp:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1303a3
com.dhass.myapp:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1303a2
com.dhass.myapp:styleable/CropImageView = 0x7f14002d
com.dhass.myapp:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1303a0
com.dhass.myapp:style/Widget.Material3.CollapsingToolbar = 0x7f13039f
com.dhass.myapp:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f13039e
com.dhass.myapp:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f130408
com.dhass.myapp:style/Widget.Material3.CircularProgressIndicator = 0x7f13039b
com.dhass.myapp:style/Widget.Material3.Chip.Input.Icon = 0x7f130396
com.dhass.myapp:style/Widget.Material3.Chip.Input.Elevated = 0x7f130395
com.dhass.myapp:style/Widget.Material3.Chip.Input = 0x7f130394
com.dhass.myapp:style/Widget.Material3.Chip.Filter.Elevated = 0x7f130393
com.dhass.myapp:style/Widget.Material3.CheckedTextView = 0x7f13038f
com.dhass.myapp:style/Widget.Material3.CardView.Outlined = 0x7f13038e
com.dhass.myapp:style/Widget.Material3.CardView.Elevated = 0x7f13038c
com.dhass.myapp:style/Widget.Material3.Button.UnelevatedButton = 0x7f13038b
com.dhass.myapp:style/Widget.Material3.Button.TonalButton = 0x7f130389
com.dhass.myapp:style/Widget.Material3.Button.TextButton.Icon = 0x7f130387
com.dhass.myapp:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f130385
com.dhass.myapp:style/Widget.Material3.Button.TextButton.Dialog = 0x7f130384
com.dhass.myapp:style/Widget.Material3.Button.TextButton = 0x7f130383
com.dhass.myapp:style/Widget.Material3.Button.OutlinedButton = 0x7f130381
com.dhass.myapp:style/Widget.Material3.Button.IconButton = 0x7f130380
com.dhass.myapp:style/Widget.Material3.Button.Icon = 0x7f13037f
com.dhass.myapp:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f13037e
com.dhass.myapp:style/Widget.Material3.Button = 0x7f13037c
com.dhass.myapp:style/Widget.Material3.BottomSheet.Modal = 0x7f13037b
com.dhass.myapp:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f130379
com.dhass.myapp:style/Widget.Material3.BottomNavigationView = 0x7f130378
com.dhass.myapp:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f130373
com.dhass.myapp:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f130372
com.dhass.myapp:style/Widget.Material3.AppBarLayout = 0x7f130371
com.dhass.myapp:style/Widget.Material3.ActionBar.Solid = 0x7f13036f
com.dhass.myapp:style/Widget.Design.TextInputLayout = 0x7f13036e
com.dhass.myapp:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f13036a
com.dhass.myapp:style/Widget.Design.FloatingActionButton = 0x7f130368
com.dhass.myapp:style/Widget.Design.BottomNavigationView = 0x7f130365
com.dhass.myapp:style/Widget.Design.AppBarLayout = 0x7f130364
com.dhass.myapp:style/Widget.Compat.NotificationActionText = 0x7f130363
com.dhass.myapp:style/Widget.Compat.NotificationActionContainer = 0x7f130362
com.dhass.myapp:style/Widget.Autofill.InlineSuggestionEndIconStyle = 0x7f13035e
com.dhass.myapp:style/Widget.Autofill.InlineSuggestionChip = 0x7f13035d
com.dhass.myapp:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f13035b
com.dhass.myapp:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f130359
com.dhass.myapp:style/Widget.AppCompat.Spinner.DropDown = 0x7f130355
com.dhass.myapp:style/Widget.AppCompat.SeekBar.Discrete = 0x7f130353
com.dhass.myapp:style/Widget.AppCompat.SeekBar = 0x7f130352
com.dhass.myapp:style/Widget.AppCompat.SearchView.ActionBar = 0x7f130351
com.dhass.myapp:style/Widget.AppCompat.RatingBar.Small = 0x7f13034f
com.dhass.myapp:style/Widget.AppCompat.RatingBar = 0x7f13034d
com.dhass.myapp:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f13034c
com.dhass.myapp:style/Widget.AppCompat.ListView.Menu = 0x7f130347
com.dhass.myapp:style/Widget.AppCompat.ListView.DropDown = 0x7f130346
com.dhass.myapp:style/Widget.AppCompat.ListMenuView = 0x7f130343
com.dhass.myapp:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f130342
com.dhass.myapp:style/Widget.AppCompat.Light.SearchView = 0x7f130341
com.dhass.myapp:style/Widget.AppCompat.Light.PopupMenu = 0x7f13033f
com.dhass.myapp:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f13033a
com.dhass.myapp:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f130339
com.dhass.myapp:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f130338
com.dhass.myapp:style/Widget.AppCompat.Light.ActionButton = 0x7f130336
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f130335
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f13043f
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f130334
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f130332
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f130331
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar = 0x7f13032d
com.dhass.myapp:style/Widget.AppCompat.DrawerArrowToggle = 0x7f130329
com.dhass.myapp:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f130327
com.dhass.myapp:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f130325
com.dhass.myapp:style/Widget.AppCompat.Button.Colored = 0x7f130322
com.dhass.myapp:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f130321
com.dhass.myapp:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f130320
com.dhass.myapp:style/Widget.AppCompat.ActionMode = 0x7f13031b
com.dhass.myapp:style/Widget.AppCompat.ActionButton.Overflow = 0x7f13031a
com.dhass.myapp:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f130319
com.dhass.myapp:style/Widget.AppCompat.ActionButton = 0x7f130318
com.dhass.myapp:style/Widget.AppCompat.ActionBar.TabView = 0x7f130317
com.dhass.myapp:style/WalletFragmentDefaultDetailsHeaderTextAppearance = 0x7f130310
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f13030d
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13030a
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f130308
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f130307
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f130306
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f130303
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.Display = 0x7f130468
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f130301
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1302fc
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1302fa
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1302f9
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1302f7
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1302f6
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1302f5
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1302f2
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1302f1
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1302ef
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302ee
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1302eb
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1302ea
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1302e8
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1302e7
com.dhass.myapp:style/amu_Bubble.TextAppearance.Dark = 0x7f130475
com.dhass.myapp:style/ThemeOverlay.MaterialComponents = 0x7f1302e6
com.dhass.myapp:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1302e5
com.dhass.myapp:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1302e4
com.dhass.myapp:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1302df
com.dhass.myapp:style/ThemeOverlay.Material3.Snackbar = 0x7f1302de
com.dhass.myapp:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1302d7
com.dhass.myapp:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1302d6
com.dhass.myapp:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1302d5
com.dhass.myapp:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1302d3
com.dhass.myapp:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1302cf
com.dhass.myapp:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1302cd
com.dhass.myapp:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1302cb
com.dhass.myapp:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f130424
com.dhass.myapp:style/ThemeOverlay.Material3.Dialog = 0x7f1302c8
com.dhass.myapp:style/ThemeOverlay.Material3.Dark = 0x7f1302c5
com.dhass.myapp:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1302c4
com.dhass.myapp:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1302c2
com.dhass.myapp:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1302c1
com.dhass.myapp:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1302bf
com.dhass.myapp:style/ThemeOverlay.Material3.Button = 0x7f1302be
com.dhass.myapp:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1302bc
com.dhass.myapp:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302bb
com.dhass.myapp:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1302b7
com.dhass.myapp:style/ThemeOverlay.Material3.ActionBar = 0x7f1302b6
com.dhass.myapp:style/ThemeOverlay.AppCompat.Light = 0x7f1302b3
com.dhass.myapp:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1302b0
com.dhass.myapp:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1302ac
com.dhass.myapp:style/Theme.SplashScreen.Common = 0x7f1302a9
com.dhass.myapp:style/Theme.ReactNative.TextInput.DefaultBackground = 0x7f1302a7
com.dhass.myapp:style/Theme.ReactNative.AppCompat.Light = 0x7f1302a5
com.dhass.myapp:styleable/ShapeAppearance = 0x7f14007d
com.dhass.myapp:style/Theme.MaterialComponents.NoActionBar = 0x7f1302a3
com.dhass.myapp:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f1302a2
com.dhass.myapp:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f1302a1
com.dhass.myapp:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f13029f
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f13029e
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f13029d
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f13029b
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f130298
com.dhass.myapp:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130296
com.dhass.myapp:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f130293
com.dhass.myapp:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f13028d
com.dhass.myapp:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f13028c
com.dhass.myapp:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f13028b
com.dhass.myapp:style/Theme.MaterialComponents.Dialog.Alert = 0x7f130289
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f130284
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f130283
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f130282
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f13027e
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f13027d
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f13027c
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f13027a
com.dhass.myapp:style/Theme.MaterialComponents.DayNight = 0x7f130278
com.dhass.myapp:style/Theme.MaterialComponents.CompactMenu = 0x7f130277
com.dhass.myapp:styleable/AlertDialog = 0x7f140006
com.dhass.myapp:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f130275
com.dhass.myapp:style/Theme.MaterialComponents = 0x7f130274
com.dhass.myapp:style/Theme.Material3.Light.Dialog = 0x7f13026f
com.dhass.myapp:style/Theme.Material3.Light.BottomSheetDialog = 0x7f13026e
com.dhass.myapp:style/Theme.Material3.DynamicColors.DayNight = 0x7f13026b
com.dhass.myapp:style/Theme.Material3.DayNight.NoActionBar = 0x7f130269
com.dhass.myapp:style/Theme.Material3.DayNight.Dialog = 0x7f130265
com.dhass.myapp:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f130264
com.dhass.myapp:style/Theme.Material3.DayNight = 0x7f130263
com.dhass.myapp:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f130261
com.dhass.myapp:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f130260
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f130286
com.dhass.myapp:style/Theme.Material3.Dark.Dialog.Alert = 0x7f13025f
com.dhass.myapp:style/Theme.FullScreenDialogAnimatedSlide = 0x7f13025b
com.dhass.myapp:style/Widget.AppCompat.PopupWindow = 0x7f13034a
com.dhass.myapp:style/Theme.FullScreenDialogAnimatedFade = 0x7f13025a
com.dhass.myapp:style/Theme.Design.Light = 0x7f130255
com.dhass.myapp:style/Widget.AppCompat.AutoCompleteTextView = 0x7f13031d
com.dhass.myapp:style/Theme.Catalyst.LogBox = 0x7f130251
com.dhass.myapp:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1303f9
com.dhass.myapp:style/Theme.AutofillInlineSuggestion = 0x7f13024f
com.dhass.myapp:style/Theme.AppCompat.NoActionBar = 0x7f13024e
com.dhass.myapp:style/Theme.AppCompat.Light.NoActionBar = 0x7f13024d
com.dhass.myapp:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f13024b
com.dhass.myapp:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f13024a
com.dhass.myapp:style/Theme.AppCompat.Light = 0x7f130247
com.dhass.myapp:style/Theme.AppCompat.Empty = 0x7f130246
com.dhass.myapp:style/Theme.AppCompat.Dialog.MinWidth = 0x7f130244
com.dhass.myapp:style/Theme.AppCompat.Dialog.Alert = 0x7f130243
com.dhass.myapp:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f130240
com.dhass.myapp:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f13023f
com.dhass.myapp:style/Theme.AppCompat.DayNight.Dialog = 0x7f13023d
com.dhass.myapp:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f13023c
com.dhass.myapp:style/Theme.AppCompat.CompactMenu = 0x7f13023a
com.dhass.myapp:style/Theme = 0x7f130237
com.dhass.myapp:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f130236
com.dhass.myapp:style/TextAppearance.Test.UsesSP = 0x7f130233
com.dhass.myapp:style/TextAppearance.Test.NoTextSize = 0x7f130231
com.dhass.myapp:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f13022f
com.dhass.myapp:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f13022e
com.dhass.myapp:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f13022d
com.dhass.myapp:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1302ae
com.dhass.myapp:style/TextAppearance.MaterialComponents.Headline6 = 0x7f13022b
com.dhass.myapp:style/TextAppearance.MaterialComponents.Body2 = 0x7f130222
com.dhass.myapp:style/TextAppearance.Material3.TitleLarge = 0x7f13021d
com.dhass.myapp:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f13021c
com.dhass.myapp:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f13042c
com.dhass.myapp:style/TextAppearance.Material3.LabelSmall = 0x7f13021b
com.dhass.myapp:style/TextAppearance.Material3.LabelMedium = 0x7f13021a
com.dhass.myapp:style/TextAppearance.Material3.HeadlineLarge = 0x7f130216
com.dhass.myapp:style/TextAppearance.Material3.DisplaySmall = 0x7f130215
com.dhass.myapp:style/TextAppearance.Material3.BodySmall = 0x7f130212
com.dhass.myapp:style/TextAppearance.Material3.BodyMedium = 0x7f130211
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f13020c
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f13020a
com.dhass.myapp:style/ThemeOverlay.Material3.Chip = 0x7f1302c3
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f130209
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f130205
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f130204
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f130203
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f130200
com.dhass.myapp:style/TextAppearance.Design.Tab = 0x7f1301fe
com.dhass.myapp:style/TextAppearance.Design.Prefix = 0x7f1301fb
com.dhass.myapp:style/TextAppearance.Design.HelperText = 0x7f1301f8
com.dhass.myapp:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1301f4
com.dhass.myapp:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1301f3
com.dhass.myapp:style/Theme.Design.BottomSheetDialog = 0x7f130254
com.dhass.myapp:style/TextAppearance.Compat.Notification.Title = 0x7f1301f2
com.dhass.myapp:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1301f1
com.dhass.myapp:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1301ee
com.dhass.myapp:style/TextAppearance.Compat.Notification.Info = 0x7f1301eb
com.dhass.myapp:style/TextAppearance.Compat.Notification = 0x7f1301ea
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1301e9
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1301e7
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1301e1
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1301df
com.dhass.myapp:style/TextAppearance.MaterialComponents.Overline = 0x7f13022c
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1301de
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1301dd
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f130299
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1301da
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1301d7
com.dhass.myapp:styleable/MaterialCalendarItem = 0x7f140057
com.dhass.myapp:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1301d3
com.dhass.myapp:style/TextAppearance.AppCompat.Subhead = 0x7f1301d2
com.dhass.myapp:style/TextAppearance.AppCompat.Small = 0x7f1301d0
com.dhass.myapp:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1301cf
com.dhass.myapp:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1301ce
com.dhass.myapp:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1301ca
com.dhass.myapp:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1301c7
com.dhass.myapp:style/TextAppearance.AppCompat.Display3 = 0x7f1301c1
com.dhass.myapp:style/TextAppearance.AppCompat.Display1 = 0x7f1301bf
com.dhass.myapp:style/TextAppearance.AppCompat = 0x7f1301ba
com.dhass.myapp:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f130382
com.dhass.myapp:style/TestThemeWithLineHeightDisabled = 0x7f1301b9
com.dhass.myapp:style/TestThemeWithLineHeight = 0x7f1301b8
com.dhass.myapp:style/TestStyleWithLineHeightAppearance = 0x7f1301b5
com.dhass.myapp:style/TestStyleWithLineHeight = 0x7f1301b4
com.dhass.myapp:style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f1301b3
com.dhass.myapp:style/Test.Widget.MaterialComponents.MaterialCalendar.Day = 0x7f1301b2
com.dhass.myapp:style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1301af
com.dhass.myapp:style/SpinnerDatePickerStyle = 0x7f1301ab
com.dhass.myapp:style/SpinnerDatePickerDialogBase = 0x7f1301aa
com.dhass.myapp:style/SpinnerDatePickerDialog = 0x7f1301a9
com.dhass.myapp:style/ShapeAppearanceOverlay.TopRightDifferentCornerSize = 0x7f1301a8
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1301a6
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1301a2
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f13019f
com.dhass.myapp:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f13019b
com.dhass.myapp:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f13019a
com.dhass.myapp:style/ShapeAppearanceOverlay.Material3.Button = 0x7f130199
com.dhass.myapp:style/ShapeAppearanceOverlay.DifferentCornerSize = 0x7f130198
com.dhass.myapp:style/ShapeAppearanceOverlay.BottomRightCut = 0x7f130196
com.dhass.myapp:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f130193
com.dhass.myapp:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f130191
com.dhass.myapp:style/Widget.AppCompat.Button.Borderless = 0x7f13031f
com.dhass.myapp:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f130190
com.dhass.myapp:style/Theme.Material3.Light.DialogWhenLarge = 0x7f130272
com.dhass.myapp:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f13018f
com.dhass.myapp:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f13018b
com.dhass.myapp:style/ShapeAppearance.Material3.MediumComponent = 0x7f13018a
com.dhass.myapp:style/ShapeAppearance.Material3.Corner.Small = 0x7f130188
com.dhass.myapp:style/ShapeAppearance.Material3.Corner.None = 0x7f130187
com.dhass.myapp:style/ShapeAppearance.Material3.Corner.Large = 0x7f130185
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1303c9
com.dhass.myapp:style/ShapeAppearance.Material3.Corner.Full = 0x7f130184
com.dhass.myapp:style/TextAppearance.Material3.HeadlineSmall = 0x7f130218
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f130181
com.dhass.myapp:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f130290
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.Large.Top = 0x7f13017e
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f13017c
com.dhass.myapp:styleable/RadialViewGroup = 0x7f140076
com.dhass.myapp:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f130175
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f130172
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f13016f
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f13016c
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f13016b
com.dhass.myapp:styleable/MaterialDivider = 0x7f14005a
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f130168
com.dhass.myapp:style/Theme.AppCompat.Dialog = 0x7f130242
com.dhass.myapp:style/Platform.Widget.AppCompat.Spinner = 0x7f130164
com.dhass.myapp:style/Theme.SplashScreen = 0x7f1302a8
com.dhass.myapp:style/Platform.V25.AppCompat = 0x7f130162
com.dhass.myapp:style/Platform.V21.AppCompat = 0x7f130160
com.dhass.myapp:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f13015f
com.dhass.myapp:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f13015e
com.dhass.myapp:style/Platform.MaterialComponents.Dialog = 0x7f13015a
com.dhass.myapp:style/Platform.AppCompat.Light = 0x7f130158
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f130155
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f130151
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130150
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f13014e
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents = 0x7f13014c
com.dhass.myapp:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f13014b
com.dhass.myapp:style/MaterialAlertDialog.Material3.Title.Text = 0x7f13014a
com.dhass.myapp:style/Theme.Material3.Light.NoActionBar = 0x7f130273
com.dhass.myapp:style/FloatingDialogWindowTheme = 0x7f130142
com.dhass.myapp:style/FloatingDialogTheme = 0x7f130141
com.dhass.myapp:style/ExoStyledControls.TimeText.Separator = 0x7f130140
com.dhass.myapp:style/Widget.AppCompat.PopupMenu = 0x7f130348
com.dhass.myapp:style/ExoStyledControls.TimeText = 0x7f13013d
com.dhass.myapp:style/ExoStyledControls.TimeBar = 0x7f13013c
com.dhass.myapp:style/ExoStyledControls.Button.Center.PlayPause = 0x7f130139
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f130304
com.dhass.myapp:style/ExoStyledControls.Button.Center.Next = 0x7f130138
com.dhass.myapp:style/ExoStyledControls.Button.Center.FfwdWithAmount = 0x7f130137
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.Settings = 0x7f130133
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.OverflowHide = 0x7f13012f
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.FullScreen = 0x7f13012e
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.CC = 0x7f13012d
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.AudioTrack = 0x7f13012c
com.dhass.myapp:style/ExoStyledControls.Button = 0x7f13012a
com.dhass.myapp:style/ExoMediaButton.VR = 0x7f130128
com.dhass.myapp:xml/library_file_paths = 0x7f150005
com.dhass.myapp:style/ExoMediaButton.Rewind = 0x7f130127
com.dhass.myapp:style/ExoMediaButton.Play = 0x7f130125
com.dhass.myapp:style/ExoMediaButton.Next = 0x7f130123
com.dhass.myapp:style/DialogAnimationSlide = 0x7f13011e
com.dhass.myapp:style/TextAppearance.MaterialComponents.Caption = 0x7f130224
com.dhass.myapp:style/DialogAnimationFade = 0x7f13011d
com.dhass.myapp:style/CheckoutTheme = 0x7f13011c
com.dhass.myapp:style/CardView.Light = 0x7f13011b
com.dhass.myapp:style/CardView.Dark = 0x7f13011a
com.dhass.myapp:style/CalendarDatePickerStyle = 0x7f130118
com.dhass.myapp:style/CalendarDatePickerDialog = 0x7f130117
com.dhass.myapp:style/Base.v21.Theme.SplashScreen = 0x7f130113
com.dhass.myapp:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13010c
com.dhass.myapp:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13010b
com.dhass.myapp:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f130109
com.dhass.myapp:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f130103
com.dhass.myapp:style/Base.Widget.Material3.Snackbar = 0x7f130101
com.dhass.myapp:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f1300ff
com.dhass.myapp:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f1300fe
com.dhass.myapp:style/Base.Widget.Material3.FloatingActionButton = 0x7f1300fd
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f130280
com.dhass.myapp:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f1300fa
com.dhass.myapp:xml/file_system_provider_paths = 0x7f150002
com.dhass.myapp:style/Base.Widget.Material3.CollapsingToolbar = 0x7f1300f7
com.dhass.myapp:style/Base.Widget.Material3.Chip = 0x7f1300f6
com.dhass.myapp:style/Base.Widget.Design.TabLayout = 0x7f1300f2
com.dhass.myapp:style/Widget.AppCompat.ActionBar = 0x7f130313
com.dhass.myapp:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1300f1
com.dhass.myapp:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1300ef
com.dhass.myapp:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1300ed
com.dhass.myapp:style/Base.Widget.AppCompat.SeekBar = 0x7f1300ea
com.dhass.myapp:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1302b8
com.dhass.myapp:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1300e9
com.dhass.myapp:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1300e6
com.dhass.myapp:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1300e4
com.dhass.myapp:style/TextAppearance.Design.Placeholder = 0x7f1301fa
com.dhass.myapp:style/Base.Widget.AppCompat.PopupMenu = 0x7f1300e0
com.dhass.myapp:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1300df
com.dhass.myapp:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1300de
com.dhass.myapp:style/Base.Widget.AppCompat.ListView = 0x7f1300dd
com.dhass.myapp:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1300dc
com.dhass.myapp:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f130108
com.dhass.myapp:style/Base.Widget.AppCompat.ListMenuView = 0x7f1300db
com.dhass.myapp:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1300da
com.dhass.myapp:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1300d9
com.dhass.myapp:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1300d8
com.dhass.myapp:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1300d6
com.dhass.myapp:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1300d3
com.dhass.myapp:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1300d0
com.dhass.myapp:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1300cf
com.dhass.myapp:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1300ce
com.dhass.myapp:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1300cd
com.dhass.myapp:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1300cc
com.dhass.myapp:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1300ca
com.dhass.myapp:style/Base.Widget.AppCompat.ButtonBar = 0x7f1300c9
com.dhass.myapp:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f130340
com.dhass.myapp:style/Base.Widget.AppCompat.Button.Small = 0x7f1300c8
com.dhass.myapp:style/TextAppearance.Compat.Notification.Line2 = 0x7f1301ed
com.dhass.myapp:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1300c2
com.dhass.myapp:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1300c1
com.dhass.myapp:style/Base.Widget.AppCompat.ActionMode = 0x7f1300c0
com.dhass.myapp:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1300bf
com.dhass.myapp:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1300bc
com.dhass.myapp:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1300b9
com.dhass.myapp:style/Base.Widget.AppCompat.ActionBar = 0x7f1300b8
com.dhass.myapp:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1300b7
com.dhass.myapp:style/Base.V7.Widget.AppCompat.EditText = 0x7f1300b6
com.dhass.myapp:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1300b4
com.dhass.myapp:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1300b3
com.dhass.myapp:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1300b1
com.dhass.myapp:style/Base.V7.Theme.AppCompat = 0x7f1300b0
com.dhass.myapp:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1301cc
com.dhass.myapp:style/Base.V28.Theme.AppCompat.Light = 0x7f1300af
com.dhass.myapp:style/Base.V28.Theme.AppCompat = 0x7f1300ae
com.dhass.myapp:style/Theme.MaterialComponents.Light.BarSize = 0x7f130292
com.dhass.myapp:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1300ad
com.dhass.myapp:style/Base.V26.Theme.AppCompat.Light = 0x7f1300ac
com.dhass.myapp:style/Base.V26.Theme.AppCompat = 0x7f1300ab
com.dhass.myapp:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1300aa
com.dhass.myapp:styleable/SwitchMaterial = 0x7f14008d
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f13032e
com.dhass.myapp:style/Base.V22.Theme.AppCompat = 0x7f1300a3
com.dhass.myapp:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1300a1
com.dhass.myapp:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f13009f
com.dhass.myapp:style/Base.V21.Theme.MaterialComponents.Light = 0x7f13009e
com.dhass.myapp:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f13009d
com.dhass.myapp:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f13009b
com.dhass.myapp:style/Base.V21.Theme.AppCompat.Light = 0x7f13009a
com.dhass.myapp:styleable/ActionBarLayout = 0x7f140001
com.dhass.myapp:style/Base.V21.Theme.AppCompat.Dialog = 0x7f130099
com.dhass.myapp:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f130097
com.dhass.myapp:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f130096
com.dhass.myapp:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f130094
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f130091
com.dhass.myapp:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f130411
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f13008f
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Light = 0x7f13008e
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f13008d
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f13008c
com.dhass.myapp:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f13033b
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1302ec
com.dhass.myapp:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f130089
com.dhass.myapp:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f130088
com.dhass.myapp:styleable/CoordinatorLayout_Layout = 0x7f14002c
com.dhass.myapp:style/Base.V14.Theme.Material3.Light = 0x7f130087
com.dhass.myapp:style/Widget.Design.BottomSheet.Modal = 0x7f130366
com.dhass.myapp:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f130085
com.dhass.myapp:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f130081
com.dhass.myapp:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f130080
com.dhass.myapp:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f13007e
com.dhass.myapp:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f13007c
com.dhass.myapp:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f13007b
com.dhass.myapp:style/Base.ThemeOverlay.AppCompat.Light = 0x7f13007a
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f130173
com.dhass.myapp:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f130079
com.dhass.myapp:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f130078
com.dhass.myapp:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f130076
com.dhass.myapp:style/Base.Theme.SplashScreen.Light = 0x7f130073
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f130070
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f13006f
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f13006e
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f13006a
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light = 0x7f130067
com.dhass.myapp:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f130066
com.dhass.myapp:style/Base.Theme.MaterialComponents.Dialog = 0x7f130061
com.dhass.myapp:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f130060
com.dhass.myapp:style/Base.Theme.MaterialComponents.Bridge = 0x7f13005f
com.dhass.myapp:style/Base.Theme.MaterialComponents = 0x7f13005e
com.dhass.myapp:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f13005c
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1303ad
com.dhass.myapp:style/Base.Theme.Material3.Light = 0x7f13005b
com.dhass.myapp:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1302c9
com.dhass.myapp:style/Base.Theme.Material3.Dark.Dialog = 0x7f13005a
com.dhass.myapp:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1300d5
com.dhass.myapp:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f130059
com.dhass.myapp:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f130057
com.dhass.myapp:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f130056
com.dhass.myapp:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f130054
com.dhass.myapp:style/Base.Theme.AppCompat.Light.Dialog = 0x7f130053
com.dhass.myapp:style/Base.Theme.AppCompat.Light = 0x7f130051
com.dhass.myapp:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f13004f
com.dhass.myapp:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f13004e
com.dhass.myapp:style/Base.Theme.AppCompat.Dialog = 0x7f13004c
com.dhass.myapp:style/Base.Theme.AppCompat.CompactMenu = 0x7f13004b
com.dhass.myapp:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f130048
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1303bf
com.dhass.myapp:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f130046
com.dhass.myapp:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f130045
com.dhass.myapp:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f130043
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f130042
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f130040
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f13003f
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f13003d
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f13003c
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f13003b
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker = 0x7f1303d0
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f130039
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f130036
com.dhass.myapp:style/ThemeOverlay.Material3.NavigationView = 0x7f1302dd
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f130031
com.dhass.myapp:style/Widget.Material3.TabLayout.OnSurface = 0x7f1303e5
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f130030
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Title = 0x7f13002f
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Subhead = 0x7f13002d
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f13002c
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Small = 0x7f13002b
com.dhass.myapp:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f130029
com.dhass.myapp:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f130028
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f130026
com.dhass.myapp:style/Theme.Catalyst = 0x7f130250
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f130024
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Inverse = 0x7f130020
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Display4 = 0x7f13001e
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Display3 = 0x7f13001d
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Display2 = 0x7f13001c
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Display1 = 0x7f13001b
com.dhass.myapp:style/Base.TextAppearance.AppCompat = 0x7f130016
com.dhass.myapp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130014
com.dhass.myapp:style/Base.DialogWindowTitle.AppCompat = 0x7f130011
com.dhass.myapp:style/Base.CardView = 0x7f130010
com.dhass.myapp:style/Base.Animation.AppCompat.Tooltip = 0x7f13000f
com.dhass.myapp:style/Base.Animation.AppCompat.DropDownUp = 0x7f13000e
com.dhass.myapp:style/Base.Animation.AppCompat.Dialog = 0x7f13000d
com.dhass.myapp:style/Base.AlertDialog.AppCompat.Light = 0x7f13000c
com.dhass.myapp:style/AppTheme = 0x7f13000a
com.dhass.myapp:style/Animation.Catalyst.LogBox = 0x7f130006
com.dhass.myapp:style/Animation.AppCompat.DropDownUp = 0x7f130004
com.dhass.myapp:style/Animation.AppCompat.Dialog = 0x7f130003
com.dhass.myapp:style/AndroidThemeColorAccentYellow = 0x7f130002
com.dhass.myapp:style/AlertDialog.AppCompat = 0x7f130000
com.dhass.myapp:string/wallet_buy_button_place_holder = 0x7f12012e
com.dhass.myapp:string/tooltip_label = 0x7f12012d
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f13043e
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Body2 = 0x7f130018
com.dhass.myapp:string/tooltip_description = 0x7f12012c
com.dhass.myapp:style/Widget.AppCompat.EditText = 0x7f13032b
com.dhass.myapp:string/timer_description = 0x7f12012a
com.dhass.myapp:style/Widget.MaterialComponents.Badge = 0x7f130403
com.dhass.myapp:string/tablist_description = 0x7f120128
com.dhass.myapp:string/tab = 0x7f120127
com.dhass.myapp:string/switch_role = 0x7f120126
com.dhass.myapp:string/state_unselected_description = 0x7f120123
com.dhass.myapp:style/TextAppearance.Design.Counter = 0x7f1301f5
com.dhass.myapp:string/state_on_description = 0x7f120122
com.dhass.myapp:string/state_off = 0x7f12011f
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f130092
com.dhass.myapp:string/state_mixed_description = 0x7f12011e
com.dhass.myapp:style/Animation.Catalyst.RedBox = 0x7f130007
com.dhass.myapp:string/state_expanded_description = 0x7f12011d
com.dhass.myapp:string/state_empty = 0x7f12011c
com.dhass.myapp:string/state_busy_description = 0x7f12011a
com.dhass.myapp:string/selected = 0x7f120118
com.dhass.myapp:string/rn_tab_description = 0x7f120115
com.dhass.myapp:string/project_id = 0x7f120111
com.dhass.myapp:string/progressbar_description = 0x7f120110
com.dhass.myapp:string/path_password_strike_through = 0x7f12010c
com.dhass.myapp:string/path_password_eye_mask_visible = 0x7f12010b
com.dhass.myapp:string/password_toggle_content_description = 0x7f120108
com.dhass.myapp:styleable/MaterialShape = 0x7f14005c
com.dhass.myapp:string/mtrl_timepicker_confirm = 0x7f120105
com.dhass.myapp:string/mtrl_picker_toggle_to_year_selection = 0x7f120104
com.dhass.myapp:string/mtrl_picker_toggle_to_text_input_mode = 0x7f120103
com.dhass.myapp:string/mtrl_picker_text_input_day_abbr = 0x7f1200fe
com.dhass.myapp:style/Platform.V25.AppCompat.Light = 0x7f130163
com.dhass.myapp:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1200fd
com.dhass.myapp:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1200fc
com.dhass.myapp:string/mtrl_picker_range_header_unselected = 0x7f1200f9
com.dhass.myapp:string/mtrl_picker_range_header_title = 0x7f1200f8
com.dhass.myapp:string/mtrl_picker_range_header_selected = 0x7f1200f7
com.dhass.myapp:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f130075
com.dhass.myapp:string/mtrl_picker_range_header_only_start_selected = 0x7f1200f6
com.dhass.myapp:string/mtrl_picker_range_header_only_end_selected = 0x7f1200f5
com.dhass.myapp:string/mtrl_picker_out_of_range = 0x7f1200f4
com.dhass.myapp:string/mtrl_picker_invalid_range = 0x7f1200f2
com.dhass.myapp:string/mtrl_picker_invalid_format_use = 0x7f1200f1
com.dhass.myapp:string/mtrl_picker_invalid_format = 0x7f1200ef
com.dhass.myapp:styleable/StyledPlayerView = 0x7f14008a
com.dhass.myapp:string/mtrl_picker_day_of_week_column_header = 0x7f1200ee
com.dhass.myapp:string/mtrl_picker_date_header_unselected = 0x7f1200ed
com.dhass.myapp:string/mtrl_picker_date_header_selected = 0x7f1200eb
com.dhass.myapp:string/mtrl_picker_confirm = 0x7f1200ea
com.dhass.myapp:string/mtrl_picker_cancel = 0x7f1200e9
com.dhass.myapp:style/Theme.Material3.Dark = 0x7f13025c
com.dhass.myapp:string/mtrl_picker_announce_current_selection = 0x7f1200e8
com.dhass.myapp:string/mtrl_picker_a11y_next_month = 0x7f1200e6
com.dhass.myapp:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1303eb
com.dhass.myapp:string/mtrl_exceed_max_badge_number_suffix = 0x7f1200e5
com.dhass.myapp:styleable/KeyFramesAcceleration = 0x7f140045
com.dhass.myapp:string/mtrl_chip_close_icon_content_description = 0x7f1200e3
com.dhass.myapp:string/mtrl_badge_numberless_content_description = 0x7f1200e2
com.dhass.myapp:string/menubar_description = 0x7f1200e0
com.dhass.myapp:string/menu_description = 0x7f1200df
com.dhass.myapp:string/material_timepicker_am = 0x7f1200d8
com.dhass.myapp:string/material_motion_easing_linear = 0x7f1200d4
com.dhass.myapp:string/material_motion_easing_emphasized = 0x7f1200d3
com.dhass.myapp:string/material_motion_easing_accelerated = 0x7f1200d1
com.dhass.myapp:string/m3_sys_motion_easing_standard_decelerate = 0x7f1200ca
com.dhass.myapp:string/m3_sys_motion_easing_standard_accelerate = 0x7f1200c9
com.dhass.myapp:string/m3_sys_motion_easing_legacy_decelerate = 0x7f1200c6
com.dhass.myapp:string/m3_sys_motion_easing_emphasized_path_data = 0x7f1200c3
com.dhass.myapp:style/Theme.Material3.DynamicColors.Dark = 0x7f13026a
com.dhass.myapp:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f1200c1
com.dhass.myapp:string/m3_sys_motion_easing_emphasized = 0x7f1200c0
com.dhass.myapp:styleable/FontFamily = 0x7f140038
com.dhass.myapp:string/m3_ref_typeface_plain_regular = 0x7f1200bf
com.dhass.myapp:styleable/CollapsingToolbarLayout_Layout = 0x7f140024
com.dhass.myapp:string/m3_ref_typeface_brand_regular = 0x7f1200bd
com.dhass.myapp:string/link_description = 0x7f1200bb
com.dhass.myapp:string/label = 0x7f1200ba
com.dhass.myapp:string/is_expo_go = 0x7f1200b8
com.dhass.myapp:string/indeterminate = 0x7f1200b7
com.dhass.myapp:string/in_progress = 0x7f1200b6
com.dhass.myapp:string/imagebutton_description = 0x7f1200b5
com.dhass.myapp:string/ic_rotate_left_24 = 0x7f1200b1
com.dhass.myapp:string/ic_flip_24 = 0x7f1200ae
com.dhass.myapp:string/hide_bottom_view_on_scroll_behavior = 0x7f1200ad
com.dhass.myapp:style/CardView = 0x7f130119
com.dhass.myapp:string/header_description = 0x7f1200ac
com.dhass.myapp:string/gcm_defaultSenderId = 0x7f1200a7
com.dhass.myapp:string/fcm_fallback_notification_channel_label = 0x7f1200a6
com.dhass.myapp:string/fallback_menu_item_open_in_browser = 0x7f1200a4
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f130068
com.dhass.myapp:string/exposed_dropdown_menu_content_description = 0x7f1200a0
com.dhass.myapp:string/exo_track_unknown = 0x7f12009b
com.dhass.myapp:styleable/LoadingImageView = 0x7f14004f
com.dhass.myapp:string/exo_track_surround_7_point_1 = 0x7f12009a
com.dhass.myapp:string/exo_track_surround_5_point_1 = 0x7f120099
com.dhass.myapp:string/exo_track_surround = 0x7f120098
com.dhass.myapp:string/exo_track_stereo = 0x7f120097
com.dhass.myapp:string/exo_track_selection_title_video = 0x7f120096
com.dhass.myapp:string/exo_track_selection_title_audio = 0x7f120094
com.dhass.myapp:string/exo_track_selection_auto = 0x7f120092
com.dhass.myapp:string/exo_track_role_supplementary = 0x7f120091
com.dhass.myapp:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1302b9
com.dhass.myapp:string/exo_track_role_commentary = 0x7f120090
com.dhass.myapp:string/exo_track_mono = 0x7f12008c
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f130437
com.dhass.myapp:string/exo_track_bitrate = 0x7f12008b
com.dhass.myapp:string/exo_download_paused_for_wifi = 0x7f120088
com.dhass.myapp:style/Widget.AppCompat.Toolbar = 0x7f13035a
com.dhass.myapp:string/exo_download_paused_for_network = 0x7f120087
com.dhass.myapp:string/exo_download_notification_channel_name = 0x7f120085
com.dhass.myapp:string/exo_download_description = 0x7f120082
com.dhass.myapp:string/exo_controls_vr_description = 0x7f120080
com.dhass.myapp:styleable/PropertySet = 0x7f140075
com.dhass.myapp:string/exo_controls_stop_description = 0x7f12007e
com.dhass.myapp:string/exo_controls_shuffle_off_description = 0x7f12007c
com.dhass.myapp:style/Platform.MaterialComponents.Light = 0x7f13015b
com.dhass.myapp:string/exo_controls_settings_description = 0x7f12007a
com.dhass.myapp:string/exo_controls_seek_bar_description = 0x7f120079
com.dhass.myapp:string/exo_controls_rewind_description = 0x7f120078
com.dhass.myapp:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f130271
com.dhass.myapp:string/exo_controls_repeat_one_description = 0x7f120077
com.dhass.myapp:string/exo_controls_repeat_off_description = 0x7f120076
com.dhass.myapp:string/exo_controls_repeat_all_description = 0x7f120075
com.dhass.myapp:string/exo_controls_previous_description = 0x7f120074
com.dhass.myapp:string/exo_controls_play_description = 0x7f120072
com.dhass.myapp:string/exo_controls_pause_description = 0x7f120071
com.dhass.myapp:string/exo_controls_overflow_show_description = 0x7f120070
com.dhass.myapp:string/exo_controls_overflow_hide_description = 0x7f12006f
com.dhass.myapp:string/toolbar_description = 0x7f12012b
com.dhass.myapp:string/exo_controls_fullscreen_enter_description = 0x7f12006b
com.dhass.myapp:string/exo_controls_fastforward_description = 0x7f12006a
com.dhass.myapp:string/exo_controls_custom_playback_speed = 0x7f120069
com.dhass.myapp:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f130166
com.dhass.myapp:string/error_icon_content_description = 0x7f120065
com.dhass.myapp:string/dropdown_menu = 0x7f120063
com.dhass.myapp:string/default_popup_window_title = 0x7f120062
com.dhass.myapp:string/default_media_controller_time = 0x7f120061
com.dhass.myapp:string/crop_image_activity_title = 0x7f12005e
com.dhass.myapp:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f130471
com.dhass.myapp:string/crop_image_activity_no_permissions = 0x7f12005d
com.dhass.myapp:string/common_signin_button_text_long = 0x7f12005b
com.dhass.myapp:string/common_google_play_services_wear_update_text = 0x7f120058
com.dhass.myapp:string/common_google_play_services_update_text = 0x7f120055
com.dhass.myapp:string/common_google_play_services_update_button = 0x7f120054
com.dhass.myapp:string/common_google_play_services_unknown_issue = 0x7f120052
com.dhass.myapp:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f130356
com.dhass.myapp:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1302e2
com.dhass.myapp:style/ExoStyledControls.Button.Bottom = 0x7f13012b
com.dhass.myapp:string/common_google_play_services_install_title = 0x7f12004f
com.dhass.myapp:string/common_google_play_services_install_button = 0x7f12004d
com.dhass.myapp:string/common_google_play_services_enable_text = 0x7f12004b
com.dhass.myapp:string/common_google_play_services_enable_button = 0x7f12004a
com.dhass.myapp:style/Theme.MaterialComponents.Light.LargeTouch = 0x7f1302a0
com.dhass.myapp:string/close_sheet = 0x7f120048
com.dhass.myapp:string/close_drawer = 0x7f120047
com.dhass.myapp:string/clear_text_end_icon_content_description = 0x7f120046
com.dhass.myapp:string/chip_text = 0x7f120045
com.dhass.myapp:string/character_counter_pattern = 0x7f120044
com.dhass.myapp:string/character_counter_overflowed_content_description = 0x7f120043
com.dhass.myapp:string/catalyst_settings_title = 0x7f120041
com.dhass.myapp:string/catalyst_reload_error = 0x7f12003d
com.dhass.myapp:string/catalyst_reload_button = 0x7f12003c
com.dhass.myapp:string/catalyst_open_debugger_error = 0x7f120038
com.dhass.myapp:string/catalyst_inspector_toggle = 0x7f120036
com.dhass.myapp:string/catalyst_hot_reloading_stop = 0x7f120035
com.dhass.myapp:string/catalyst_hot_reloading = 0x7f120032
com.dhass.myapp:string/catalyst_dev_menu_header = 0x7f12002e
com.dhass.myapp:string/catalyst_debug_open_disabled = 0x7f12002d
com.dhass.myapp:string/catalyst_debug_open = 0x7f12002c
com.dhass.myapp:string/catalyst_change_bundle_location = 0x7f120028
com.dhass.myapp:string/call_notification_decline_action = 0x7f120023
com.dhass.myapp:string/catalyst_perf_monitor = 0x7f120039
com.dhass.myapp:string/call_notification_answer_video_action = 0x7f120022
com.dhass.myapp:string/bottom_sheet_behavior = 0x7f12001f
com.dhass.myapp:string/appbar_scrolling_view_behavior = 0x7f12001e
com.dhass.myapp:string/app_name = 0x7f12001d
com.dhass.myapp:string/androidx_startup = 0x7f12001c
com.dhass.myapp:string/alert_description = 0x7f12001b
com.dhass.myapp:string/abc_shareactionprovider_share_with_application = 0x7f120019
com.dhass.myapp:string/abc_shareactionprovider_share_with = 0x7f120018
com.dhass.myapp:string/abc_searchview_description_voice = 0x7f120017
com.dhass.myapp:string/abc_searchview_description_query = 0x7f120014
com.dhass.myapp:string/abc_search_hint = 0x7f120012
com.dhass.myapp:string/abc_prepend_shortcut_label = 0x7f120011
com.dhass.myapp:string/abc_menu_sym_shortcut_label = 0x7f120010
com.dhass.myapp:style/TextAppearance.MaterialComponents.Body1 = 0x7f130221
com.dhass.myapp:string/abc_menu_space_shortcut_label = 0x7f12000f
com.dhass.myapp:string/abc_menu_shift_shortcut_label = 0x7f12000e
com.dhass.myapp:string/abc_menu_meta_shortcut_label = 0x7f12000d
com.dhass.myapp:string/abc_menu_ctrl_shortcut_label = 0x7f120009
com.dhass.myapp:string/bottomsheet_action_expand_halfway = 0x7f120020
com.dhass.myapp:string/abc_menu_alt_shortcut_label = 0x7f120008
com.dhass.myapp:string/abc_activitychooserview_choose_application = 0x7f120005
com.dhass.myapp:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1300eb
com.dhass.myapp:string/abc_activity_chooser_view_see_all = 0x7f120004
com.dhass.myapp:string/abc_action_mode_done = 0x7f120003
com.dhass.myapp:string/abc_action_bar_home_description = 0x7f120000
com.dhass.myapp:raw/otpelf = 0x7f110015
com.dhass.myapp:string/material_slider_range_end = 0x7f1200d6
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_zocial = 0x7f110014
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f130177
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_simplelineicons = 0x7f110013
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_octicons = 0x7f110012
com.dhass.myapp:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1302cc
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialicons = 0x7f110011
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_foundation = 0x7f11000e
com.dhass.myapp:string/summary_description = 0x7f120125
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontisto = 0x7f11000d
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_solid = 0x7f11000c
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_solid = 0x7f110009
com.dhass.myapp:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f130326
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_brands = 0x7f110007
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome = 0x7f110006
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_antdesign = 0x7f110002
com.dhass.myapp:raw/firebase_common_keep = 0x7f110001
com.dhass.myapp:raw/assets_fonts_spacemonoregular = 0x7f110000
com.dhass.myapp:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f130426
com.dhass.myapp:mipmap/ic_launcher_round = 0x7f0f0002
com.dhass.myapp:mipmap/ic_launcher_foreground = 0x7f0f0001
com.dhass.myapp:mipmap/ic_launcher = 0x7f0f0000
com.dhass.myapp:menu/crop_image_menu = 0x7f0e0000
com.dhass.myapp:layout/wallet_test_layout = 0x7f0d00a1
com.dhass.myapp:layout/text_view_without_line_height = 0x7f0d00a0
com.dhass.myapp:layout/text_view_with_line_height_from_style = 0x7f0d009e
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f13029c
com.dhass.myapp:layout/text_view_with_line_height_from_appearance = 0x7f0d009c
com.dhass.myapp:layout/test_toolbar_elevation = 0x7f0d009a
com.dhass.myapp:layout/test_toolbar_custom_background = 0x7f0d0099
com.dhass.myapp:layout/test_design_radiobutton = 0x7f0d0094
com.dhass.myapp:styleable/MockView = 0x7f140064
com.dhass.myapp:layout/test_chip_zero_corner_radius = 0x7f0d0092
com.dhass.myapp:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f13033c
com.dhass.myapp:layout/test_action_chip = 0x7f0d0091
com.dhass.myapp:layout/single_item = 0x7f0d008e
com.dhass.myapp:layout/select_dialog_singlechoice_material = 0x7f0d008d
com.dhass.myapp:layout/select_dialog_multichoice_material = 0x7f0d008c
com.dhass.myapp:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1300e7
com.dhass.myapp:layout/sdk_integration_status = 0x7f0d008a
com.dhass.myapp:layout/rzp_magic_base = 0x7f0d0089
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1303a7
com.dhass.myapp:layout/rzp_loader = 0x7f0d0088
com.dhass.myapp:layout/redbox_item_title = 0x7f0d0086
com.dhass.myapp:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f130146
com.dhass.myapp:layout/redbox_item_frame = 0x7f0d0085
com.dhass.myapp:layout/paused_in_debugger_view = 0x7f0d0084
com.dhass.myapp:layout/notification_template_part_time = 0x7f0d0083
com.dhass.myapp:layout/notification_template_part_chronometer = 0x7f0d0082
com.dhass.myapp:layout/notification_template_media_custom = 0x7f0d0081
com.dhass.myapp:string/abc_menu_enter_shortcut_label = 0x7f12000b
com.dhass.myapp:layout/notification_template_media = 0x7f0d0080
com.dhass.myapp:layout/notification_template_icon_group = 0x7f0d007e
com.dhass.myapp:layout/notification_template_big_media_narrow_custom = 0x7f0d007c
com.dhass.myapp:layout/notification_template_big_media_narrow = 0x7f0d007b
com.dhass.myapp:layout/notification_template_big_media_custom = 0x7f0d007a
com.dhass.myapp:layout/notification_media_action = 0x7f0d0077
com.dhass.myapp:layout/notification_layout = 0x7f0d0076
com.dhass.myapp:layout/notification_action_tombstone = 0x7f0d0075
com.dhass.myapp:style/ExoMediaButton.Previous = 0x7f130126
com.dhass.myapp:layout/mtrl_picker_text_input_date = 0x7f0d0072
com.dhass.myapp:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1302e0
com.dhass.myapp:layout/mtrl_picker_header_toggle = 0x7f0d0071
com.dhass.myapp:layout/mtrl_picker_header_title_text = 0x7f0d0070
com.dhass.myapp:layout/mtrl_picker_header_fullscreen = 0x7f0d006e
com.dhass.myapp:layout/mtrl_picker_header_dialog = 0x7f0d006d
com.dhass.myapp:layout/mtrl_picker_fullscreen = 0x7f0d006c
com.dhass.myapp:layout/mtrl_picker_dialog = 0x7f0d006b
com.dhass.myapp:style/Platform.MaterialComponents = 0x7f130159
com.dhass.myapp:layout/mtrl_picker_actions = 0x7f0d006a
com.dhass.myapp:layout/mtrl_layout_snackbar = 0x7f0d0067
com.dhass.myapp:layout/mtrl_calendar_year = 0x7f0d0066
com.dhass.myapp:style/Widget.Material3.Chip.Suggestion = 0x7f130398
com.dhass.myapp:style/Theme.AppCompat.Light.DarkActionBar = 0x7f130248
com.dhass.myapp:layout/mtrl_calendar_months = 0x7f0d0064
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f13046e
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f130035
com.dhass.myapp:layout/mtrl_calendar_month_labeled = 0x7f0d0062
com.dhass.myapp:layout/mtrl_calendar_month = 0x7f0d0061
com.dhass.myapp:layout/mtrl_calendar_days_of_week = 0x7f0d005f
com.dhass.myapp:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f130050
com.dhass.myapp:layout/mtrl_alert_select_dialog_multichoice = 0x7f0d005a
com.dhass.myapp:layout/mtrl_alert_select_dialog_item = 0x7f0d0059
com.dhass.myapp:layout/mtrl_alert_dialog_title = 0x7f0d0058
com.dhass.myapp:layout/mtrl_alert_dialog_actions = 0x7f0d0057
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f130201
com.dhass.myapp:layout/material_timepicker_dialog = 0x7f0d0054
com.dhass.myapp:layout/material_time_input = 0x7f0d0052
com.dhass.myapp:layout/material_time_chip = 0x7f0d0051
com.dhass.myapp:string/catalyst_debug_connecting = 0x7f12002a
com.dhass.myapp:layout/material_radial_view_group = 0x7f0d004f
com.dhass.myapp:string/exo_track_selection_none = 0x7f120093
com.dhass.myapp:layout/material_clockface_view = 0x7f0d004e
com.dhass.myapp:layout/material_clock_period_toggle_land = 0x7f0d004c
com.dhass.myapp:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f130427
com.dhass.myapp:layout/material_clock_period_toggle = 0x7f0d004b
com.dhass.myapp:layout/material_clock_display_divider = 0x7f0d004a
com.dhass.myapp:layout/material_clock_display = 0x7f0d0049
com.dhass.myapp:style/Widget.MaterialComponents.Toolbar = 0x7f13046f
com.dhass.myapp:layout/material_chip_input_combo = 0x7f0d0048
com.dhass.myapp:string/m3_sys_motion_easing_legacy = 0x7f1200c4
com.dhass.myapp:layout/m3_alert_dialog_actions = 0x7f0d0045
com.dhass.myapp:layout/m3_alert_dialog = 0x7f0d0044
com.dhass.myapp:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f130268
com.dhass.myapp:layout/expo_media_controller = 0x7f0d0040
com.dhass.myapp:layout/exo_track_selection_dialog = 0x7f0d003f
com.dhass.myapp:layout/exo_styled_sub_settings_list_item = 0x7f0d003e
com.dhass.myapp:layout/exo_styled_settings_list_item = 0x7f0d003d
com.dhass.myapp:layout/exo_styled_player_view = 0x7f0d003b
com.dhass.myapp:layout/exo_styled_player_control_view = 0x7f0d003a
com.dhass.myapp:layout/exo_styled_player_control_rewind_button = 0x7f0d0039
com.dhass.myapp:layout/exo_player_view = 0x7f0d0037
com.dhass.myapp:layout/design_text_input_end_icon = 0x7f0d0032
com.dhass.myapp:layout/design_navigation_menu_item = 0x7f0d0031
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1302f3
com.dhass.myapp:layout/design_navigation_menu = 0x7f0d0030
com.dhass.myapp:style/Theme.MaterialComponents.Light = 0x7f130291
com.dhass.myapp:layout/design_navigation_item_separator = 0x7f0d002e
com.dhass.myapp:layout/design_navigation_item_header = 0x7f0d002d
com.dhass.myapp:layout/design_menu_item_action_area = 0x7f0d002b
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f13027f
com.dhass.myapp:layout/design_layout_tab_icon = 0x7f0d0029
com.dhass.myapp:layout/design_layout_snackbar_include = 0x7f0d0028
com.dhass.myapp:layout/crop_image_view = 0x7f0d0023
com.dhass.myapp:layout/autofill_inline_suggestion = 0x7f0d001f
com.dhass.myapp:layout/amu_webview = 0x7f0d001e
com.dhass.myapp:string/ic_flip_24_vertically = 0x7f1200b0
com.dhass.myapp:layout/abc_search_dropdown_item_icons_2line = 0x7f0d0018
com.dhass.myapp:style/TestStyleWithoutLineHeight = 0x7f1301b7
com.dhass.myapp:layout/abc_screen_simple = 0x7f0d0015
com.dhass.myapp:layout/abc_popup_menu_header_item_layout = 0x7f0d0012
com.dhass.myapp:layout/abc_list_menu_item_radio = 0x7f0d0011
com.dhass.myapp:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1301d1
com.dhass.myapp:layout/abc_list_menu_item_layout = 0x7f0d0010
com.dhass.myapp:layout/abc_list_menu_item_icon = 0x7f0d000f
com.dhass.myapp:layout/abc_list_menu_item_checkbox = 0x7f0d000e
com.dhass.myapp:layout/abc_expanded_menu_layout = 0x7f0d000d
com.dhass.myapp:string/catalyst_settings = 0x7f120040
com.dhass.myapp:layout/abc_cascading_menu_item_layout = 0x7f0d000b
com.dhass.myapp:layout/abc_alert_dialog_title_material = 0x7f0d000a
com.dhass.myapp:layout/abc_activity_chooser_view_list_item = 0x7f0d0007
com.dhass.myapp:layout/abc_action_mode_close_item_material = 0x7f0d0005
com.dhass.myapp:layout/abc_action_menu_layout = 0x7f0d0003
com.dhass.myapp:layout/abc_action_bar_up_container = 0x7f0d0001
com.dhass.myapp:style/ExoStyledControls = 0x7f130129
com.dhass.myapp:layout/abc_action_bar_title_item = 0x7f0d0000
com.dhass.myapp:interpolator/mtrl_linear = 0x7f0c0009
com.dhass.myapp:interpolator/mtrl_fast_out_slow_in = 0x7f0c0008
com.dhass.myapp:interpolator/mtrl_fast_out_linear_in = 0x7f0c0007
com.dhass.myapp:interpolator/fast_out_slow_in = 0x7f0c0006
com.dhass.myapp:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004
com.dhass.myapp:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003
com.dhass.myapp:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001
com.dhass.myapp:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000
com.dhass.myapp:integer/status_bar_notification_info_maxnum = 0x7f0b0039
com.dhass.myapp:integer/show_password_duration = 0x7f0b0038
com.dhass.myapp:integer/react_native_dev_server_port = 0x7f0b0037
com.dhass.myapp:integer/mtrl_view_visible = 0x7f0b0036
com.dhass.myapp:string/exo_controls_fullscreen_exit_description = 0x7f12006c
com.dhass.myapp:integer/mtrl_view_invisible = 0x7f0b0035
com.dhass.myapp:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0b0033
com.dhass.myapp:integer/mtrl_card_anim_duration_ms = 0x7f0b0031
com.dhass.myapp:integer/mtrl_calendar_year_selector_span = 0x7f0b002f
com.dhass.myapp:string/material_timepicker_hour = 0x7f1200da
com.dhass.myapp:integer/mtrl_calendar_selection_text_lines = 0x7f0b002e
com.dhass.myapp:style/TextAppearance.AppCompat.Large = 0x7f1301c5
com.dhass.myapp:integer/mtrl_btn_anim_delay_ms = 0x7f0b002b
com.dhass.myapp:integer/mtrl_badge_max_character_count = 0x7f0b002a
com.dhass.myapp:style/Widget.Design.TextInputEditText = 0x7f13036d
com.dhass.myapp:integer/material_motion_path = 0x7f0b0029
com.dhass.myapp:integer/material_motion_duration_short_2 = 0x7f0b0028
com.dhass.myapp:integer/material_motion_duration_medium_2 = 0x7f0b0026
com.dhass.myapp:integer/material_motion_duration_medium_1 = 0x7f0b0025
com.dhass.myapp:integer/m3_sys_motion_duration_800 = 0x7f0b0020
com.dhass.myapp:integer/m3_sys_motion_duration_550 = 0x7f0b001d
com.dhass.myapp:integer/m3_sys_motion_duration_500 = 0x7f0b001c
com.dhass.myapp:integer/m3_sys_motion_duration_50 = 0x7f0b001b
com.dhass.myapp:integer/m3_sys_motion_duration_450 = 0x7f0b001a
com.dhass.myapp:integer/m3_sys_motion_duration_350 = 0x7f0b0018
com.dhass.myapp:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f130456
com.dhass.myapp:integer/m3_sys_motion_duration_250 = 0x7f0b0016
com.dhass.myapp:integer/m3_sys_motion_duration_150 = 0x7f0b0014
com.dhass.myapp:integer/m3_chip_anim_duration = 0x7f0b0011
com.dhass.myapp:integer/m3_btn_anim_duration_ms = 0x7f0b000e
com.dhass.myapp:integer/hide_password_duration = 0x7f0b000c
com.dhass.myapp:integer/exo_media_button_opacity_percentage_enabled = 0x7f0b000a
com.dhass.myapp:integer/exo_media_button_opacity_percentage_disabled = 0x7f0b0009
com.dhass.myapp:integer/design_snackbar_text_max_lines = 0x7f0b0007
com.dhass.myapp:integer/config_tooltipAnimTime = 0x7f0b0005
com.dhass.myapp:integer/cancel_button_image_alpha = 0x7f0b0004
com.dhass.myapp:integer/bottom_sheet_slide_duration = 0x7f0b0003
com.dhass.myapp:integer/app_bar_elevation_anim_duration = 0x7f0b0002
com.dhass.myapp:integer/abc_config_activityDefaultDur = 0x7f0b0000
com.dhass.myapp:id/zoom = 0x7f0a0280
com.dhass.myapp:id/zero_corner_chip = 0x7f0a027f
com.dhass.myapp:id/wrapped_composition_tag = 0x7f0a027e
com.dhass.myapp:id/wrap_content = 0x7f0a027d
com.dhass.myapp:id/wrap = 0x7f0a027c
com.dhass.myapp:id/withinBounds = 0x7f0a027b
com.dhass.myapp:id/window = 0x7f0a0279
com.dhass.myapp:id/when_playing = 0x7f0a0277
com.dhass.myapp:id/visible = 0x7f0a0274
com.dhass.myapp:id/view_tree_view_model_store_owner = 0x7f0a0273
com.dhass.myapp:id/view_tree_lifecycle_owner = 0x7f0a0270
com.dhass.myapp:id/view_tag_instance_handle = 0x7f0a026e
com.dhass.myapp:id/view_offset_helper = 0x7f0a026d
com.dhass.myapp:id/use_hardware_layer = 0x7f0a026b
com.dhass.myapp:id/useLogo = 0x7f0a026a
com.dhass.myapp:id/uniform = 0x7f0a0267
com.dhass.myapp:id/triangle = 0x7f0a0263
com.dhass.myapp:id/transition_transform = 0x7f0a0262
com.dhass.myapp:id/transition_scene_layoutid_cache = 0x7f0a0261
com.dhass.myapp:id/transition_layout_save = 0x7f0a025f
com.dhass.myapp:id/transition_current_scene = 0x7f0a025e
com.dhass.myapp:id/transitionToStart = 0x7f0a025d
com.dhass.myapp:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f1300fb
com.dhass.myapp:id/transitionToEnd = 0x7f0a025c
com.dhass.myapp:id/transform_origin = 0x7f0a025b
com.dhass.myapp:id/touch_outside = 0x7f0a0259
com.dhass.myapp:layout/browser_actions_context_menu_row = 0x7f0d0021
com.dhass.myapp:id/topPanel = 0x7f0a0258
com.dhass.myapp:id/title_template = 0x7f0a0255
com.dhass.myapp:id/titleDividerNoCustom = 0x7f0a0254
com.dhass.myapp:string/material_motion_easing_standard = 0x7f1200d5
com.dhass.myapp:id/title = 0x7f0a0253
com.dhass.myapp:id/time = 0x7f0a0252
com.dhass.myapp:id/textinput_prefix_text = 0x7f0a024f
com.dhass.myapp:id/textinput_helper_text = 0x7f0a024d
com.dhass.myapp:style/Theme.AppCompat = 0x7f130239
com.dhass.myapp:id/textinput_error = 0x7f0a024c
com.dhass.myapp:id/text_input_start_icon = 0x7f0a024a
com.dhass.myapp:id/text_input_error_icon = 0x7f0a0249
com.dhass.myapp:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f130388
com.dhass.myapp:id/textViewCountdown = 0x7f0a0247
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f13027b
com.dhass.myapp:style/TextAppearance.AppCompat.Body1 = 0x7f1301bb
com.dhass.myapp:id/textTop = 0x7f0a0246
com.dhass.myapp:id/text = 0x7f0a0240
com.dhass.myapp:id/test_radiobutton_app_button_tint = 0x7f0a023f
com.dhass.myapp:id/test_radiobutton_android_button_tint = 0x7f0a023e
com.dhass.myapp:id/test_checkbox_android_button_tint = 0x7f0a023c
com.dhass.myapp:id/test = 0x7f0a023b
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.VR = 0x7f130135
com.dhass.myapp:string/exo_controls_hide = 0x7f12006d
com.dhass.myapp:id/terrain = 0x7f0a023a
com.dhass.myapp:id/tag_screen_reader_focusable = 0x7f0a0234
com.dhass.myapp:id/tag_on_receive_content_mime_types = 0x7f0a0233
com.dhass.myapp:id/tag_on_receive_content_listener = 0x7f0a0232
com.dhass.myapp:style/Base.Theme.SplashScreen.DayNight = 0x7f130072
com.dhass.myapp:id/tag_on_apply_window_listener = 0x7f0a0231
com.dhass.myapp:id/tag_accessibility_actions = 0x7f0a022d
com.dhass.myapp:id/tabMode = 0x7f0a022c
com.dhass.myapp:id/submit_area = 0x7f0a022a
com.dhass.myapp:id/submenuarrow = 0x7f0a0229
com.dhass.myapp:id/stretch = 0x7f0a0227
com.dhass.myapp:string/common_google_play_services_update_title = 0x7f120056
com.dhass.myapp:id/stop = 0x7f0a0226
com.dhass.myapp:id/staticLayout = 0x7f0a0223
com.dhass.myapp:id/startHorizontal = 0x7f0a0220
com.dhass.myapp:styleable/ScrimInsetsFrameLayout = 0x7f14007a
com.dhass.myapp:id/src_over = 0x7f0a021d
com.dhass.myapp:id/src_atop = 0x7f0a021b
com.dhass.myapp:id/square = 0x7f0a021a
com.dhass.myapp:id/spread = 0x7f0a0218
com.dhass.myapp:id/spline = 0x7f0a0216
com.dhass.myapp:id/spherical_gl_surface_view = 0x7f0a0214
com.dhass.myapp:id/special_effects_controller_view_tag = 0x7f0a0213
com.dhass.myapp:id/spacer = 0x7f0a0212
com.dhass.myapp:id/skip_previous_button = 0x7f0a020c
com.dhass.myapp:id/skip_next_button = 0x7f0a020b
com.dhass.myapp:style/Widget.AppCompat.SearchView = 0x7f130350
com.dhass.myapp:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1300c5
com.dhass.myapp:string/catalyst_dev_menu_sub_header = 0x7f12002f
com.dhass.myapp:id/shortcut = 0x7f0a0205
com.dhass.myapp:id/selection_type = 0x7f0a0204
com.dhass.myapp:id/selectionDetails = 0x7f0a0203
com.dhass.myapp:styleable/ActionMenuItemView = 0x7f140002
com.dhass.myapp:id/selected = 0x7f0a0202
com.dhass.myapp:id/select_dialog_listview = 0x7f0a0201
com.dhass.myapp:id/search_plate = 0x7f0a01fd
com.dhass.myapp:style/TextAppearance.AppCompat.Headline = 0x7f1301c3
com.dhass.myapp:id/search_mag_icon = 0x7f0a01fc
com.dhass.myapp:id/search_badge = 0x7f0a01f6
com.dhass.myapp:id/textinput_counter = 0x7f0a024b
com.dhass.myapp:id/scrollIndicatorUp = 0x7f0a01f3
com.dhass.myapp:style/Base.Widget.MaterialComponents.Snackbar = 0x7f13010f
com.dhass.myapp:id/scrollIndicatorDown = 0x7f0a01f2
com.dhass.myapp:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1302c0
com.dhass.myapp:id/screen = 0x7f0a01f0
com.dhass.myapp:id/sawtooth = 0x7f0a01ee
com.dhass.myapp:id/save_non_transition_alpha = 0x7f0a01ec
com.dhass.myapp:id/satellite = 0x7f0a01eb
com.dhass.myapp:id/sandbox = 0x7f0a01ea
com.dhass.myapp:id/rzp_theMainMagicView = 0x7f0a01e9
com.dhass.myapp:style/Base.Widget.Material3.ActionMode = 0x7f1300f4
com.dhass.myapp:id/rzp_outerbox = 0x7f0a01e7
com.dhass.myapp:id/rzp_innerbox = 0x7f0a01e6
com.dhass.myapp:id/row_index_key = 0x7f0a01e5
com.dhass.myapp:id/rn_redbox_report_label = 0x7f0a01e1
com.dhass.myapp:id/rn_redbox_reload_button = 0x7f0a01df
com.dhass.myapp:id/rn_redbox_loading_indicator = 0x7f0a01de
com.dhass.myapp:integer/m3_card_anim_duration_ms = 0x7f0b0010
com.dhass.myapp:id/rn_frame_method = 0x7f0a01db
com.dhass.myapp:id/rn_frame_file = 0x7f0a01da
com.dhass.myapp:id/rightToLeft = 0x7f0a01d7
com.dhass.myapp:id/right = 0x7f0a01d6
com.dhass.myapp:id/report_drawn = 0x7f0a01d3
com.dhass.myapp:id/rectangleVerticalOnly = 0x7f0a01d1
com.dhass.myapp:id/ratio = 0x7f0a01cd
com.dhass.myapp:id/progress_horizontal = 0x7f0a01cb
com.dhass.myapp:id/progressBar = 0x7f0a01c9
com.dhass.myapp:id/snap = 0x7f0a0210
com.dhass.myapp:id/pooling_container_listener_holder_tag = 0x7f0a01c5
com.dhass.myapp:id/pointer_events = 0x7f0a01c4
com.dhass.myapp:id/play_button = 0x7f0a01c3
com.dhass.myapp:id/performance = 0x7f0a01c1
com.dhass.myapp:id/percent = 0x7f0a01c0
com.dhass.myapp:id/peekHeight = 0x7f0a01bf
com.dhass.myapp:id/pathRelative = 0x7f0a01be
com.dhass.myapp:layout/material_textinput_timepicker = 0x7f0d0050
com.dhass.myapp:id/password_toggle = 0x7f0a01bc
com.dhass.myapp:id/parentPanel = 0x7f0a01b9
com.dhass.myapp:id/parent = 0x7f0a01b8
com.dhass.myapp:id/packed = 0x7f0a01b6
com.dhass.myapp:id/outward = 0x7f0a01b4
com.dhass.myapp:id/snackbar_action = 0x7f0a020e
com.dhass.myapp:id/one = 0x7f0a01b2
com.dhass.myapp:string/range_end = 0x7f120113
com.dhass.myapp:id/onTouch = 0x7f0a01b1
com.dhass.myapp:id/notification_main_column = 0x7f0a01ad
com.dhass.myapp:id/notification_background = 0x7f0a01ac
com.dhass.myapp:id/none = 0x7f0a01aa
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f130180
com.dhass.myapp:id/noScroll = 0x7f0a01a9
com.dhass.myapp:layout/mtrl_layout_snackbar_include = 0x7f0d0068
com.dhass.myapp:id/textEnd = 0x7f0a0242
com.dhass.myapp:id/navigation_bar_item_small_label_view = 0x7f0a01a6
com.dhass.myapp:id/navigation_bar_item_labels_group = 0x7f0a01a4
com.dhass.myapp:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f130386
com.dhass.myapp:id/navigation_bar_item_icon_view = 0x7f0a01a3
com.dhass.myapp:id/navigation_bar_item_icon_container = 0x7f0a01a2
com.dhass.myapp:id/navigation_bar_item_active_indicator_view = 0x7f0a01a1
com.dhass.myapp:id/multiply = 0x7f0a01a0
com.dhass.myapp:id/mtrl_view_tag_bottom_padding = 0x7f0a019f
com.dhass.myapp:style/TextAppearance.AppCompat.Title = 0x7f1301d4
com.dhass.myapp:id/mtrl_picker_text_input_range_start = 0x7f0a019d
com.dhass.myapp:id/mtrl_picker_text_input_range_end = 0x7f0a019c
com.dhass.myapp:id/mtrl_picker_header_title_and_selection = 0x7f0a0199
com.dhass.myapp:id/mtrl_picker_header_selection_text = 0x7f0a0198
com.dhass.myapp:string/state_off_description = 0x7f120120
com.dhass.myapp:string/image_description = 0x7f1200b4
com.dhass.myapp:id/mtrl_picker_fullscreen = 0x7f0a0196
com.dhass.myapp:id/mtrl_motion_snapshot_view = 0x7f0a0195
com.dhass.myapp:id/mtrl_internal_children_alpha_tag = 0x7f0a0194
com.dhass.myapp:id/mtrl_child_content_container = 0x7f0a0193
com.dhass.myapp:styleable/MenuItem = 0x7f140062
com.dhass.myapp:id/mtrl_card_checked_layer_id = 0x7f0a0192
com.dhass.myapp:id/mtrl_calendar_year_selector_frame = 0x7f0a0191
com.dhass.myapp:id/mtrl_calendar_selection_frame = 0x7f0a018f
com.dhass.myapp:id/mtrl_calendar_months = 0x7f0a018e
com.dhass.myapp:id/mtrl_calendar_main_pane = 0x7f0a018d
com.dhass.myapp:id/mtrl_calendar_frame = 0x7f0a018c
com.dhass.myapp:id/mtrl_calendar_day_selector_frame = 0x7f0a018a
com.dhass.myapp:id/motion_base = 0x7f0a0188
com.dhass.myapp:string/abc_searchview_description_submit = 0x7f120016
com.dhass.myapp:id/month_title = 0x7f0a0187
com.dhass.myapp:id/month_navigation_previous = 0x7f0a0186
com.dhass.myapp:id/month_navigation_next = 0x7f0a0185
com.dhass.myapp:id/month_navigation_fragment_toggle = 0x7f0a0184
com.dhass.myapp:id/month_grid = 0x7f0a0182
com.dhass.myapp:id/monochrome = 0x7f0a0181
com.dhass.myapp:id/mix_blend_mode = 0x7f0a0180
com.dhass.myapp:id/middle = 0x7f0a017e
com.dhass.myapp:id/message = 0x7f0a017d
com.dhass.myapp:id/matrix = 0x7f0a017a
com.dhass.myapp:id/material_timepicker_ok_button = 0x7f0a0177
com.dhass.myapp:id/material_timepicker_mode_button = 0x7f0a0176
com.dhass.myapp:id/material_textinput_timepicker = 0x7f0a0172
com.dhass.myapp:id/material_minute_tv = 0x7f0a0171
com.dhass.myapp:style/Theme.Material3.Light.Dialog.Alert = 0x7f130270
com.dhass.myapp:id/material_label = 0x7f0a016f
com.dhass.myapp:id/material_hour_tv = 0x7f0a016e
com.dhass.myapp:id/view_tag_native_id = 0x7f0a026f
com.dhass.myapp:id/rzp_securedpayments = 0x7f0a01e8
com.dhass.myapp:id/material_clock_period_pm_button = 0x7f0a016b
com.dhass.myapp:id/match_parent = 0x7f0a0165
com.dhass.myapp:id/masked = 0x7f0a0164
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13006d
com.dhass.myapp:string/common_google_play_services_notification_ticker = 0x7f120051
com.dhass.myapp:id/logo_only = 0x7f0a0163
com.dhass.myapp:id/list_item = 0x7f0a0161
com.dhass.myapp:style/Theme.App.SplashScreen = 0x7f130238
com.dhass.myapp:id/listMode = 0x7f0a0160
com.dhass.myapp:id/linear = 0x7f0a015f
com.dhass.myapp:id/light = 0x7f0a015c
com.dhass.myapp:id/left = 0x7f0a015a
com.dhass.myapp:id/layout = 0x7f0a0159
com.dhass.myapp:style/Base.Widget.AppCompat.EditText = 0x7f1300d1
com.dhass.myapp:id/labelled_by = 0x7f0a0158
com.dhass.myapp:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1303da
com.dhass.myapp:id/jumpToStart = 0x7f0a0156
com.dhass.myapp:id/jumpToEnd = 0x7f0a0155
com.dhass.myapp:layout/amu_info_window = 0x7f0d001c
com.dhass.myapp:id/iv_check_mark = 0x7f0a0154
com.dhass.myapp:id/item_touch_helper_previous_elevation = 0x7f0a0153
com.dhass.myapp:id/invisible = 0x7f0a014f
com.dhass.myapp:id/invalidate_transform = 0x7f0a014e
com.dhass.myapp:style/Platform.MaterialComponents.Light.Dialog = 0x7f13015c
com.dhass.myapp:id/ignoreRequest = 0x7f0a014a
com.dhass.myapp:id/ignore = 0x7f0a0149
com.dhass.myapp:id/ic_rotate_right_24 = 0x7f0a0144
com.dhass.myapp:id/ic_rotate_left_24 = 0x7f0a0143
com.dhass.myapp:id/ic_flip_24_vertically = 0x7f0a0142
com.dhass.myapp:id/ic_flip_24_horizontally = 0x7f0a0141
com.dhass.myapp:id/ic_flip_24 = 0x7f0a0140
com.dhass.myapp:style/Widget.MaterialComponents.CheckedTextView = 0x7f130419
com.dhass.myapp:id/honorRequest = 0x7f0a013e
com.dhass.myapp:id/search_voice_btn = 0x7f0a01ff
com.dhass.myapp:id/homeAsUp = 0x7f0a013d
com.dhass.myapp:id/hideable = 0x7f0a0139
com.dhass.myapp:id/hide_in_inspector_tag = 0x7f0a0138
com.dhass.myapp:id/tv_title = 0x7f0a0265
com.dhass.myapp:id/hide_graphics_layer_in_inspector_tag = 0x7f0a0136
com.dhass.myapp:id/guideline = 0x7f0a0134
com.dhass.myapp:layout/m3_auto_complete_simple_item = 0x7f0d0047
com.dhass.myapp:id/never = 0x7f0a01a8
com.dhass.myapp:id/groups = 0x7f0a0133
com.dhass.myapp:id/group_divider = 0x7f0a0132
com.dhass.myapp:id/graph_wrap = 0x7f0a0130
com.dhass.myapp:id/google_wallet_grayscale = 0x7f0a012d
com.dhass.myapp:layout/support_simple_spinner_dropdown_item = 0x7f0d0090
com.dhass.myapp:id/googleMaterial2 = 0x7f0a012b
com.dhass.myapp:id/ghost_view_holder = 0x7f0a0128
com.dhass.myapp:id/fullscreen_mode_button = 0x7f0a0126
com.dhass.myapp:string/material_hour_selection = 0x7f1200cd
com.dhass.myapp:id/flip = 0x7f0a011f
com.dhass.myapp:styleable/MaterialCheckBox = 0x7f140059
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f13032f
com.dhass.myapp:id/fixed_height = 0x7f0a011d
com.dhass.myapp:id/slide = 0x7f0a020d
com.dhass.myapp:id/fitStart = 0x7f0a0119
com.dhass.myapp:id/fitEnd = 0x7f0a0118
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1302ff
com.dhass.myapp:color/design_default_color_on_primary = 0x7f060056
com.dhass.myapp:drawable/ic_rotate_right_24 = 0x7f0800f7
com.dhass.myapp:id/fitCenter = 0x7f0a0117
com.dhass.myapp:id/home = 0x7f0a013c
com.dhass.myapp:id/filter = 0x7f0a0114
com.dhass.myapp:id/fillCenter = 0x7f0a010e
com.dhass.myapp:attr/flow_verticalStyle = 0x7f0401ec
com.dhass.myapp:id/expanded_menu = 0x7f0a010a
com.dhass.myapp:id/expand_activities_button = 0x7f0a0109
com.dhass.myapp:id/exo_time = 0x7f0a0106
com.dhass.myapp:styleable/MotionHelper = 0x7f140066
com.dhass.myapp:id/exo_rew_with_amount = 0x7f0a00fd
com.dhass.myapp:style/TextAppearance.MaterialComponents.Headline2 = 0x7f130227
com.dhass.myapp:id/exo_repeat_toggle = 0x7f0a00fb
com.dhass.myapp:style/ShapeAppearanceOverlay.Material3.TextField.Filled = 0x7f13019d
com.dhass.myapp:id/exo_prev = 0x7f0a00f8
com.dhass.myapp:id/exo_play_pause = 0x7f0a00f5
com.dhass.myapp:attr/liftOnScrollTargetViewId = 0x7f0402a1
com.dhass.myapp:id/exo_pause = 0x7f0a00f3
com.dhass.myapp:anim/abc_slide_out_top = 0x7f010009
com.dhass.myapp:id/exo_main_text = 0x7f0a00ec
com.dhass.myapp:id/exo_ffwd_with_amount = 0x7f0a00e9
com.dhass.myapp:id/exo_duration = 0x7f0a00e4
com.dhass.myapp:attr/materialAlertDialogBodyTextStyle = 0x7f0402c7
com.dhass.myapp:id/enterAlwaysCollapsed = 0x7f0a00d6
com.dhass.myapp:id/exo_controls_background = 0x7f0a00e3
com.dhass.myapp:attr/listPreferredItemHeightSmall = 0x7f0402b0
com.dhass.myapp:id/exo_controller_placeholder = 0x7f0a00e2
com.dhass.myapp:id/holo_light = 0x7f0a013b
com.dhass.myapp:id/add = 0x7f0a0051
com.dhass.myapp:id/exo_content_frame = 0x7f0a00e0
com.dhass.myapp:id/exitUntilCollapsed = 0x7f0a00d7
com.dhass.myapp:id/endToStart = 0x7f0a00d2
com.dhass.myapp:id/end = 0x7f0a00d1
com.dhass.myapp:id/edittext_dropdown_editable = 0x7f0a00ce
com.dhass.myapp:id/edit_text_id = 0x7f0a00cd
com.dhass.myapp:id/easeInOut = 0x7f0a00ca
com.dhass.myapp:id/dropdown_editable = 0x7f0a00c6
com.dhass.myapp:style/Animation.Design.BottomSheetDialog = 0x7f130008
com.dhass.myapp:id/dragRight = 0x7f0a00c3
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f130431
com.dhass.myapp:attr/motionDurationShort1 = 0x7f040308
com.dhass.myapp:id/dragDown = 0x7f0a00c0
com.dhass.myapp:color/primary_text_default_material_light = 0x7f06025d
com.dhass.myapp:id/donate_with_google = 0x7f0a00bf
com.dhass.myapp:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000d
com.dhass.myapp:id/donate_with = 0x7f0a00be
com.dhass.myapp:id/disjoint = 0x7f0a00bd
com.dhass.myapp:styleable/OnSwipe = 0x7f14006f
com.dhass.myapp:id/direct = 0x7f0a00b9
com.dhass.myapp:id/design_menu_item_action_area = 0x7f0a00b3
com.dhass.myapp:id/design_bottom_sheet = 0x7f0a00b2
com.dhass.myapp:id/decor_content_parent = 0x7f0a00af
com.dhass.myapp:xml/file_provider_paths = 0x7f150001
com.dhass.myapp:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.dhass.myapp:id/cut = 0x7f0a00aa
com.dhass.myapp:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f13024c
com.dhass.myapp:color/switch_thumb_normal_material_dark = 0x7f06026c
com.dhass.myapp:id/customPanel = 0x7f0a00a9
com.dhass.myapp:drawable/notification_bg = 0x7f080125
com.dhass.myapp:id/cropImageView = 0x7f0a00a5
com.dhass.myapp:attr/show_subtitle_button = 0x7f0403a8
com.dhass.myapp:id/counterclockwise = 0x7f0a00a4
com.dhass.myapp:id/cos = 0x7f0a00a3
com.dhass.myapp:id/contiguous = 0x7f0a00a1
com.dhass.myapp:id/content = 0x7f0a009f
com.dhass.myapp:color/cardview_shadow_end_color = 0x7f060031
com.dhass.myapp:id/compatible = 0x7f0a0099
com.dhass.myapp:id/clear_text = 0x7f0a0094
com.dhass.myapp:id/circle_center = 0x7f0a0092
com.dhass.myapp:dimen/mtrl_extended_fab_min_height = 0x7f0701f5
com.dhass.myapp:id/chip2 = 0x7f0a008e
com.dhass.myapp:attr/cropMinCropWindowHeight = 0x7f04014f
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600b4
com.dhass.myapp:id/chip = 0x7f0a008c
com.dhass.myapp:id/chains = 0x7f0a0088
com.dhass.myapp:id/webview = 0x7f0a0276
com.dhass.myapp:id/center_horizontal = 0x7f0a0085
com.dhass.myapp:style/Base.ThemeOverlay.Material3.Dialog = 0x7f13007d
com.dhass.myapp:id/centerInside = 0x7f0a0084
com.dhass.myapp:id/centerCrop = 0x7f0a0083
com.dhass.myapp:id/center = 0x7f0a0082
com.dhass.myapp:style/Widget.Material3.CardView.Filled = 0x7f13038d
com.dhass.myapp:color/m3_sys_color_light_inverse_on_surface = 0x7f06018e
com.dhass.myapp:id/buy_with_google = 0x7f0a007e
com.dhass.myapp:attr/layout_constraintTag = 0x7f040286
com.dhass.myapp:id/buy_with = 0x7f0a007d
com.dhass.myapp:attr/actionModeSplitBackground = 0x7f04001b
com.dhass.myapp:color/abc_tint_switch_track = 0x7f060018
com.dhass.myapp:id/buyButton = 0x7f0a007b
com.dhass.myapp:id/button_text = 0x7f0a007a
com.dhass.myapp:id/exo_subtitle = 0x7f0a0103
com.dhass.myapp:attr/endIconCheckable = 0x7f04019e
com.dhass.myapp:id/browser_actions_menu_items = 0x7f0a0076
com.dhass.myapp:string/exo_track_role_closed_captions = 0x7f12008f
com.dhass.myapp:id/browser_actions_menu_item_icon = 0x7f0a0074
com.dhass.myapp:styleable/Spinner = 0x7f140084
com.dhass.myapp:style/Base.Widget.MaterialComponents.TextView = 0x7f130112
com.dhass.myapp:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.dhass.myapp:color/common_google_signin_btn_text_light_disabled = 0x7f06003f
com.dhass.myapp:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080071
com.dhass.myapp:id/browser_actions_header_text = 0x7f0a0073
com.dhass.myapp:string/exo_controls_show = 0x7f12007b
com.dhass.myapp:id/bottom = 0x7f0a0071
com.dhass.myapp:attr/cropFlipVertically = 0x7f040145
com.dhass.myapp:id/blocking = 0x7f0a006f
com.dhass.myapp:id/beginning = 0x7f0a006e
com.dhass.myapp:id/beginOnFirstDraw = 0x7f0a006d
com.dhass.myapp:id/autofill_inline_suggestion_subtitle = 0x7f0a0069
com.dhass.myapp:attr/materialTimePickerStyle = 0x7f0402e9
com.dhass.myapp:id/autoCompleteToStart = 0x7f0a0066
com.dhass.myapp:id/async = 0x7f0a0062
com.dhass.myapp:color/browser_actions_title_color = 0x7f06002a
com.dhass.myapp:id/animateToEnd = 0x7f0a005e
com.dhass.myapp:id/all = 0x7f0a0056
com.dhass.myapp:styleable/SnackbarLayout = 0x7f140083
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130154
com.dhass.myapp:attr/behavior_saveFlags = 0x7f04006b
com.dhass.myapp:id/alertTitle = 0x7f0a0054
com.dhass.myapp:color/button_material_light = 0x7f06002c
com.dhass.myapp:attr/marginHorizontal = 0x7f0402bc
com.dhass.myapp:dimen/mtrl_calendar_bottom_padding = 0x7f0701ba
com.dhass.myapp:id/actions = 0x7f0a004f
com.dhass.myapp:attr/endIconMode = 0x7f0401a1
com.dhass.myapp:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700cd
com.dhass.myapp:id/action_bar_title = 0x7f0a0044
com.dhass.myapp:id/action_bar_activity_content = 0x7f0a003f
com.dhass.myapp:id/elastic = 0x7f0a00d0
com.dhass.myapp:id/header_title = 0x7f0a0135
com.dhass.myapp:id/action0 = 0x7f0a003d
com.dhass.myapp:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1302d8
com.dhass.myapp:id/accessibility_links = 0x7f0a0038
com.dhass.myapp:id/accessibility_label = 0x7f0a0037
com.dhass.myapp:id/rn_redbox_report_button = 0x7f0a01e0
com.dhass.myapp:dimen/design_bottom_navigation_icon_size = 0x7f070069
com.dhass.myapp:dimen/m3_alert_dialog_icon_margin = 0x7f0700c9
com.dhass.myapp:id/easeOut = 0x7f0a00cb
com.dhass.myapp:id/accessibility_hint = 0x7f0a0036
com.dhass.myapp:string/abc_searchview_description_search = 0x7f120015
com.dhass.myapp:id/accessibility_custom_action_9 = 0x7f0a0035
com.dhass.myapp:id/accessibility_custom_action_8 = 0x7f0a0034
com.dhass.myapp:drawable/abc_switch_thumb_material = 0x7f080047
com.dhass.myapp:id/accessibility_custom_action_7 = 0x7f0a0033
com.dhass.myapp:id/accessibility_custom_action_6 = 0x7f0a0032
com.dhass.myapp:dimen/abc_button_inset_vertical_material = 0x7f070013
com.dhass.myapp:id/accessibility_custom_action_30 = 0x7f0a002e
com.dhass.myapp:id/accessibility_custom_action_26 = 0x7f0a0029
com.dhass.myapp:anim/catalyst_push_up_in = 0x7f01001a
com.dhass.myapp:drawable/exo_ic_pause_circle_filled = 0x7f0800a7
com.dhass.myapp:id/accessibility_custom_action_23 = 0x7f0a0026
com.dhass.myapp:style/Base.Theme.Material3.Dark = 0x7f130058
com.dhass.myapp:id/accessibility_custom_action_22 = 0x7f0a0025
com.dhass.myapp:id/accessibility_custom_action_20 = 0x7f0a0023
com.dhass.myapp:id/action_mode_bar = 0x7f0a004b
com.dhass.myapp:id/action_image = 0x7f0a0048
com.dhass.myapp:style/DialogWindowTheme = 0x7f13011f
com.dhass.myapp:string/call_notification_incoming_text = 0x7f120025
com.dhass.myapp:dimen/cardview_default_radius = 0x7f070058
com.dhass.myapp:id/accessibility_custom_action_2 = 0x7f0a0022
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f070147
com.dhass.myapp:id/accessibility_custom_action_16 = 0x7f0a001e
com.dhass.myapp:id/accessibility_custom_action_15 = 0x7f0a001d
com.dhass.myapp:string/state_collapsed_description = 0x7f12011b
com.dhass.myapp:id/video_decoder_gl_surface_view = 0x7f0a026c
com.dhass.myapp:id/accessibility_custom_action_13 = 0x7f0a001b
com.dhass.myapp:id/accessibility_custom_action_12 = 0x7f0a001a
com.dhass.myapp:id/accessibility_custom_action_1 = 0x7f0a0017
com.dhass.myapp:id/accessibility_collection_item = 0x7f0a0015
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130152
com.dhass.myapp:id/accessibility_collection = 0x7f0a0014
com.dhass.myapp:id/TOP_END = 0x7f0a000f
com.dhass.myapp:style/Widget.Autofill.InlineSuggestionTitle = 0x7f130361
com.dhass.myapp:id/SHOW_ALL = 0x7f0a000b
com.dhass.myapp:id/SHIFT = 0x7f0a000a
com.dhass.myapp:id/ImageView_image = 0x7f0a0007
com.dhass.myapp:id/CropProgressBar = 0x7f0a0005
com.dhass.myapp:id/CTRL = 0x7f0a0003
com.dhass.myapp:color/secondary_text_disabled_material_dark = 0x7f060265
com.dhass.myapp:id/BOTTOM_START = 0x7f0a0002
com.dhass.myapp:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1303ec
com.dhass.myapp:style/Widget.Design.Snackbar = 0x7f13036b
com.dhass.myapp:id/BOTTOM_END = 0x7f0a0001
com.dhass.myapp:drawable/tooltip_frame_light = 0x7f080145
com.dhass.myapp:attr/fabAlignmentMode = 0x7f0401c1
com.dhass.myapp:drawable/tooltip_frame_dark = 0x7f080144
com.dhass.myapp:drawable/test_custom_background = 0x7f080142
com.dhass.myapp:attr/colorOnSecondary = 0x7f0400f2
com.dhass.myapp:drawable/abc_list_longpressed_holo = 0x7f08002d
com.dhass.myapp:id/autofill_inline_suggestion_title = 0x7f0a006a
com.dhass.myapp:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1302dc
com.dhass.myapp:drawable/rzp_poweredby = 0x7f08013e
com.dhass.myapp:id/SYM = 0x7f0a000e
com.dhass.myapp:drawable/rzp_name_logo = 0x7f08013d
com.dhass.myapp:drawable/rzp_green_button = 0x7f08013a
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Menu = 0x7f130027
com.dhass.myapp:drawable/rzp_border = 0x7f080138
com.dhass.myapp:drawable/rn_edit_text_material = 0x7f080136
com.dhass.myapp:drawable/redbox_top_border_background = 0x7f080134
com.dhass.myapp:style/ShapeAppearance.Material3.Tooltip = 0x7f13018d
com.dhass.myapp:style/NoAnimationDialog = 0x7f130156
com.dhass.myapp:dimen/m3_navigation_rail_item_padding_top = 0x7f07012b
com.dhass.myapp:id/fill = 0x7f0a010d
com.dhass.myapp:drawable/progress_bar_custom = 0x7f080133
com.dhass.myapp:attr/indicatorInset = 0x7f04022b
com.dhass.myapp:drawable/paused_in_debugger_dialog_background = 0x7f080132
com.dhass.myapp:layout/test_reflow_chipgroup = 0x7f0d0097
com.dhass.myapp:animator/m3_card_state_list_anim = 0x7f020010
com.dhass.myapp:drawable/notification_tile_bg = 0x7f08012f
com.dhass.myapp:drawable/notification_template_icon_bg = 0x7f08012d
com.dhass.myapp:drawable/notification_icon_background = 0x7f08012b
com.dhass.myapp:drawable/notification_bg_normal = 0x7f080129
com.dhass.myapp:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_clearicon = 0x7f080121
com.dhass.myapp:attr/listPreferredItemPaddingStart = 0x7f0402b4
com.dhass.myapp:attr/flow_verticalBias = 0x7f0401ea
com.dhass.myapp:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_backiconmask = 0x7f080120
com.dhass.myapp:id/dark = 0x7f0a00ab
com.dhass.myapp:id/text_input_end_icon = 0x7f0a0248
com.dhass.myapp:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_backicon = 0x7f08011f
com.dhass.myapp:drawable/node_modules_reactnativeelementdropdown_src_assets_down = 0x7f08011e
com.dhass.myapp:id/src_in = 0x7f0a021c
com.dhass.myapp:dimen/design_snackbar_action_text_color_alpha = 0x7f070084
com.dhass.myapp:drawable/node_modules_reactnativeelementdropdown_src_assets_close = 0x7f08011d
com.dhass.myapp:style/TextAppearance.MaterialComponents.Tooltip = 0x7f130230
com.dhass.myapp:attr/textAppearanceLabelMedium = 0x7f04041a
com.dhass.myapp:drawable/node_modules_exporouter_assets_unmatched = 0x7f08011c
com.dhass.myapp:attr/haloColor = 0x7f040202
com.dhass.myapp:drawable/node_modules_exporouter_assets_forward = 0x7f080119
com.dhass.myapp:attr/crossfade = 0x7f04015d
com.dhass.myapp:drawable/node_modules_exporouter_assets_error = 0x7f080117
com.dhass.myapp:styleable/MaterialCardView = 0x7f140058
com.dhass.myapp:dimen/splashscreen_icon_size_no_background = 0x7f070262
com.dhass.myapp:drawable/mtrl_tabs_default_indicator = 0x7f080115
com.dhass.myapp:drawable/mtrl_popupmenu_background_overlay = 0x7f080114
com.dhass.myapp:drawable/mtrl_navigation_bar_item_background = 0x7f080112
com.dhass.myapp:drawable/mtrl_ic_error = 0x7f080111
com.dhass.myapp:id/month_navigation_bar = 0x7f0a0183
com.dhass.myapp:drawable/mtrl_ic_cancel = 0x7f080110
com.dhass.myapp:string/material_slider_range_start = 0x7f1200d7
com.dhass.myapp:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08004a
com.dhass.myapp:drawable/mtrl_dialog_background = 0x7f08010c
com.dhass.myapp:attr/hide_on_touch = 0x7f04020f
com.dhass.myapp:drawable/material_ic_calendar_black_24dp = 0x7f080103
com.dhass.myapp:attr/arrowShaftLength = 0x7f04003b
com.dhass.myapp:drawable/material_cursor_drawable = 0x7f080102
com.dhass.myapp:attr/colorOnPrimary = 0x7f0400ef
com.dhass.myapp:drawable/m3_tabs_rounded_line_indicator = 0x7f080100
com.dhass.myapp:id/scroll = 0x7f0a01f1
com.dhass.myapp:drawable/m3_tabs_background = 0x7f0800fe
com.dhass.myapp:id/design_navigation_view = 0x7f0a00b6
com.dhass.myapp:style/amu_Bubble.TextAppearance.Light = 0x7f130476
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f070146
com.dhass.myapp:drawable/m3_radiobutton_ripple = 0x7f0800fc
com.dhass.myapp:color/m3_sys_color_dynamic_dark_tertiary = 0x7f060173
com.dhass.myapp:drawable/m3_popupmenu_background_overlay = 0x7f0800fb
com.dhass.myapp:attr/actualImageScaleType = 0x7f040026
com.dhass.myapp:drawable/m3_appbar_background = 0x7f0800fa
com.dhass.myapp:integer/mtrl_view_gone = 0x7f0b0034
com.dhass.myapp:drawable/icon_background = 0x7f0800f9
com.dhass.myapp:attr/behavior_halfExpandedRatio = 0x7f040067
com.dhass.myapp:drawable/ic_mtrl_chip_close_circle = 0x7f0800f4
com.dhass.myapp:attr/closeItemLayout = 0x7f0400d5
com.dhass.myapp:attr/pressedStateOverlayImage = 0x7f040357
com.dhass.myapp:id/exo_progress_placeholder = 0x7f0a00fa
com.dhass.myapp:drawable/ic_flip_24 = 0x7f0800e9
com.dhass.myapp:drawable/ic_call_decline = 0x7f0800e6
com.dhass.myapp:id/buttonPanel = 0x7f0a0079
com.dhass.myapp:style/TextAppearance.MaterialComponents.Headline3 = 0x7f130228
com.dhass.myapp:drawable/ic_call_answer_video_low = 0x7f0800e5
com.dhass.myapp:drawable/ic_call_answer_video = 0x7f0800e4
com.dhass.myapp:drawable/exo_styled_controls_vr = 0x7f0800de
com.dhass.myapp:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f13025d
com.dhass.myapp:attr/endIconTint = 0x7f0401a2
com.dhass.myapp:attr/maskedWalletDetailsTextAppearance = 0x7f0402c6
com.dhass.myapp:drawable/exo_styled_controls_subtitle_on = 0x7f0800dd
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1301a1
com.dhass.myapp:drawable/exo_styled_controls_speed = 0x7f0800db
com.dhass.myapp:drawable/exo_styled_controls_shuffle_on = 0x7f0800da
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600c1
com.dhass.myapp:attr/collapseContentDescription = 0x7f0400d6
com.dhass.myapp:attr/tabRippleColor = 0x7f0403fa
com.dhass.myapp:id/accessibility_custom_action_17 = 0x7f0a001f
com.dhass.myapp:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f08010b
com.dhass.myapp:drawable/exo_styled_controls_settings = 0x7f0800d8
com.dhass.myapp:drawable/exo_styled_controls_rewind = 0x7f0800d7
com.dhass.myapp:string/catalyst_loading_from_url = 0x7f120037
com.dhass.myapp:drawable/exo_styled_controls_repeat_off = 0x7f0800d5
com.dhass.myapp:id/exo_minimal_fullscreen = 0x7f0a00ee
com.dhass.myapp:drawable/exo_styled_controls_overflow_hide = 0x7f0800cf
com.dhass.myapp:dimen/abc_text_size_button_material = 0x7f070041
com.dhass.myapp:drawable/exo_styled_controls_fullscreen_exit = 0x7f0800cd
com.dhass.myapp:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f130055
com.dhass.myapp:drawable/exo_styled_controls_fullscreen_enter = 0x7f0800cc
com.dhass.myapp:string/exo_track_resolution = 0x7f12008d
com.dhass.myapp:attr/show_timeout = 0x7f0403a9
com.dhass.myapp:drawable/exo_styled_controls_fastforward = 0x7f0800cb
com.dhass.myapp:drawable/exo_rounded_rectangle = 0x7f0800c8
com.dhass.myapp:drawable/exo_notification_rewind = 0x7f0800c5
com.dhass.myapp:id/fillEnd = 0x7f0a010f
com.dhass.myapp:drawable/exo_notification_pause = 0x7f0800c2
com.dhass.myapp:drawable/exo_notification_fastforward = 0x7f0800c0
com.dhass.myapp:attr/subheaderInsetEnd = 0x7f0403d2
com.dhass.myapp:drawable/exo_icon_rewind = 0x7f0800bb
com.dhass.myapp:drawable/exo_icon_previous = 0x7f0800b7
com.dhass.myapp:drawable/exo_icon_pause = 0x7f0800b5
com.dhass.myapp:dimen/mtrl_calendar_navigation_height = 0x7f0701d2
com.dhass.myapp:drawable/exo_icon_fullscreen_enter = 0x7f0800b2
com.dhass.myapp:string/scrollbar_description = 0x7f120116
com.dhass.myapp:attr/rangeFillColor = 0x7f040362
com.dhass.myapp:drawable/exo_ic_subtitle_on = 0x7f0800af
com.dhass.myapp:drawable/exo_ic_play_circle_filled = 0x7f0800a8
com.dhass.myapp:attr/shapeAppearanceMediumComponent = 0x7f040396
com.dhass.myapp:drawable/exo_ic_fullscreen_enter = 0x7f0800a5
com.dhass.myapp:drawable/exo_ic_default_album_image = 0x7f0800a3
com.dhass.myapp:drawable/exo_ic_chevron_right = 0x7f0800a2
com.dhass.myapp:string/path_password_eye = 0x7f120109
com.dhass.myapp:id/staticPostLayout = 0x7f0a0224
com.dhass.myapp:id/accessibility_value = 0x7f0a003c
com.dhass.myapp:drawable/ic_m3_chip_close = 0x7f0800f0
com.dhass.myapp:string/abc_capital_on = 0x7f120007
com.dhass.myapp:drawable/exo_ic_chevron_left = 0x7f0800a1
com.dhass.myapp:drawable/exo_edit_mode_logo = 0x7f08009e
com.dhass.myapp:drawable/exo_controls_vr = 0x7f08009d
com.dhass.myapp:drawable/exo_controls_shuffle_on = 0x7f08009c
com.dhass.myapp:attr/endIconContentDescription = 0x7f04019f
com.dhass.myapp:drawable/abc_list_selector_background_transition_holo_light = 0x7f080031
com.dhass.myapp:id/exo_shuffle = 0x7f0a0100
com.dhass.myapp:drawable/exo_controls_repeat_all = 0x7f080097
com.dhass.myapp:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0701cc
com.dhass.myapp:drawable/abc_list_divider_mtrl_alpha = 0x7f08002b
com.dhass.myapp:id/accessibility_actions = 0x7f0a0013
com.dhass.myapp:color/m3_chip_assist_text_color = 0x7f060089
com.dhass.myapp:drawable/exo_controls_play = 0x7f080095
com.dhass.myapp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130015
com.dhass.myapp:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f080073
com.dhass.myapp:drawable/exo_controls_fullscreen_exit = 0x7f080092
com.dhass.myapp:id/classic = 0x7f0a0093
com.dhass.myapp:drawable/design_password_eye = 0x7f08008e
com.dhass.myapp:drawable/design_ic_visibility = 0x7f08008c
com.dhass.myapp:drawable/abc_btn_borderless_material = 0x7f080008
com.dhass.myapp:drawable/exo_icon_circular_play = 0x7f0800b0
com.dhass.myapp:id/start = 0x7f0a021f
com.dhass.myapp:drawable/design_fab_background = 0x7f08008b
com.dhass.myapp:drawable/compat_splash_screen_no_icon_background = 0x7f08008a
com.dhass.myapp:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f13023e
com.dhass.myapp:drawable/common_google_signin_btn_text_light_normal = 0x7f080087
com.dhass.myapp:drawable/exo_styled_controls_subtitle_off = 0x7f0800dc
com.dhass.myapp:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f080083
com.dhass.myapp:attr/materialAlertDialogTheme = 0x7f0402c9
com.dhass.myapp:drawable/common_google_signin_btn_text_dark = 0x7f080080
com.dhass.myapp:style/Base.V14.Theme.Material3.Dark = 0x7f130084
com.dhass.myapp:id/splashscreen_icon_view = 0x7f0a0215
com.dhass.myapp:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f08007f
com.dhass.myapp:layout/design_layout_snackbar = 0x7f0d0027
com.dhass.myapp:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f08007a
com.dhass.myapp:string/exit_fullscreen_mode = 0x7f120066
com.dhass.myapp:drawable/common_google_signin_btn_icon_dark_normal = 0x7f080079
com.dhass.myapp:string/exo_download_removing = 0x7f120089
com.dhass.myapp:drawable/btn_radio_on_mtrl = 0x7f080074
com.dhass.myapp:drawable/notification_oversize_large_icon_bg = 0x7f08012c
com.dhass.myapp:layout/notification_template_custom_big = 0x7f0d007d
com.dhass.myapp:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08006f
com.dhass.myapp:drawable/exo_ic_rewind = 0x7f0800a9
com.dhass.myapp:attr/maskedWalletDetailsButtonTextAppearance = 0x7f0402c2
com.dhass.myapp:drawable/autofill_inline_suggestion_chip_background = 0x7f08006b
com.dhass.myapp:id/dragStart = 0x7f0a00c4
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_qe = 0x7f080065
com.dhass.myapp:color/m3_sys_color_dark_background = 0x7f060145
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_facebookpng = 0x7f080062
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_copy = 0x7f080061
com.dhass.myapp:drawable/assets_profileimg_bootomsheet = 0x7f080060
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1303c6
com.dhass.myapp:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1303a1
com.dhass.myapp:attr/expandedTitleMarginStart = 0x7f0401b6
com.dhass.myapp:drawable/assets_img_bannerima = 0x7f08005c
com.dhass.myapp:color/m3_dark_primary_text_disable_only = 0x7f060092
com.dhass.myapp:drawable/assets_images_profile = 0x7f08005a
com.dhass.myapp:id/mtrl_anchor_parent = 0x7f0a0189
com.dhass.myapp:color/m3_timepicker_display_background_color = 0x7f0601b3
com.dhass.myapp:drawable/assets_images_image = 0x7f080059
com.dhass.myapp:attr/fontProviderFetchTimeout = 0x7f0401f3
com.dhass.myapp:drawable/assets_images_appleicon = 0x7f080057
com.dhass.myapp:drawable/exo_controls_next = 0x7f080093
com.dhass.myapp:drawable/abc_textfield_search_material = 0x7f080053
com.dhass.myapp:drawable/abc_textfield_activated_mtrl_alpha = 0x7f08004f
com.dhass.myapp:drawable/abc_text_select_handle_right_mtrl = 0x7f08004e
com.dhass.myapp:drawable/abc_text_select_handle_middle_mtrl = 0x7f08004d
com.dhass.myapp:string/expo_system_ui_user_interface_style = 0x7f12009f
com.dhass.myapp:animator/fragment_open_enter = 0x7f020007
com.dhass.myapp:drawable/abc_spinner_mtrl_am_alpha = 0x7f080043
com.dhass.myapp:drawable/abc_seekbar_thumb_material = 0x7f080040
com.dhass.myapp:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08003c
com.dhass.myapp:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08003b
com.dhass.myapp:drawable/common_google_signin_btn_text_dark_normal = 0x7f080082
com.dhass.myapp:drawable/abc_ratingbar_small_material = 0x7f08003a
com.dhass.myapp:color/material_dynamic_secondary90 = 0x7f0601f2
com.dhass.myapp:drawable/abc_ratingbar_material = 0x7f080039
com.dhass.myapp:id/FUNCTION = 0x7f0a0006
com.dhass.myapp:attr/statusBarScrim = 0x7f0403cd
com.dhass.myapp:drawable/abc_popup_background_mtrl_mult = 0x7f080037
com.dhass.myapp:id/grayscale = 0x7f0a0131
com.dhass.myapp:id/dialog_button = 0x7f0a00b7
com.dhass.myapp:drawable/abc_list_selector_holo_dark = 0x7f080034
com.dhass.myapp:drawable/abc_list_selector_disabled_holo_dark = 0x7f080032
com.dhass.myapp:attr/homeAsUpIndicator = 0x7f040214
com.dhass.myapp:attr/cornerFamilyTopLeft = 0x7f040127
com.dhass.myapp:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080030
com.dhass.myapp:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1302bd
com.dhass.myapp:drawable/abc_list_pressed_holo_light = 0x7f08002f
com.dhass.myapp:drawable/abc_item_background_holo_light = 0x7f080029
com.dhass.myapp:style/Theme.Design.NoActionBar = 0x7f130258
com.dhass.myapp:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f130100
com.dhass.myapp:drawable/abc_item_background_holo_dark = 0x7f080028
com.dhass.myapp:drawable/abc_ic_search_api_material = 0x7f080026
com.dhass.myapp:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080025
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f13008b
com.dhass.myapp:color/m3_assist_chip_icon_tint_color = 0x7f06007d
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600b3
com.dhass.myapp:dimen/mtrl_calendar_navigation_top_padding = 0x7f0701d3
com.dhass.myapp:drawable/abc_ic_menu_overflow_material = 0x7f080022
com.dhass.myapp:drawable/btn_checkbox_checked_mtrl = 0x7f08006e
com.dhass.myapp:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080021
com.dhass.myapp:attr/colorOnSurface = 0x7f0400f4
com.dhass.myapp:drawable/abc_ic_go_search_api_material = 0x7f08001f
com.dhass.myapp:color/mtrl_chip_text_color = 0x7f060231
com.dhass.myapp:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08001c
com.dhass.myapp:drawable/node_modules_exporouter_assets_sitemap = 0x7f08011b
com.dhass.myapp:attr/useMaterialThemeColors = 0x7f040485
com.dhass.myapp:drawable/abc_edit_text_material = 0x7f08001a
com.dhass.myapp:attr/region_heightMoreThan = 0x7f040368
com.dhass.myapp:color/m3_chip_stroke_color = 0x7f06008c
com.dhass.myapp:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080012
com.dhass.myapp:drawable/abc_btn_colored_material = 0x7f08000d
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f130439
com.dhass.myapp:attr/backgroundOverlayColorAlpha = 0x7f040051
com.dhass.myapp:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08000c
com.dhass.myapp:drawable/exo_icon_repeat_all = 0x7f0800b8
com.dhass.myapp:drawable/abc_btn_check_material_anim = 0x7f08000a
com.dhass.myapp:drawable/abc_action_bar_item_background_material = 0x7f080007
com.dhass.myapp:attr/customStringValue = 0x7f040168
com.dhass.myapp:drawable/$avd_hide_password__0 = 0x7f080000
com.dhass.myapp:attr/gapBetweenBars = 0x7f0401ff
com.dhass.myapp:drawable/abc_cab_background_top_material = 0x7f080016
com.dhass.myapp:style/TextAppearance.Material3.HeadlineMedium = 0x7f130217
com.dhass.myapp:id/contentPanel = 0x7f0a00a0
com.dhass.myapp:dimen/tooltip_y_offset_non_touch = 0x7f070277
com.dhass.myapp:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08000b
com.dhass.myapp:id/showCustom = 0x7f0a0206
com.dhass.myapp:dimen/tooltip_vertical_padding = 0x7f070276
com.dhass.myapp:drawable/mtrl_ic_arrow_drop_up = 0x7f08010f
com.dhass.myapp:drawable/assets_images_start = 0x7f08005b
com.dhass.myapp:id/accessibility_custom_action_19 = 0x7f0a0021
com.dhass.myapp:dimen/tooltip_precise_anchor_threshold = 0x7f070275
com.dhass.myapp:dimen/tooltip_precise_anchor_extra_offset = 0x7f070274
com.dhass.myapp:dimen/tooltip_margin = 0x7f070273
com.dhass.myapp:dimen/test_navigation_bar_shadow_height = 0x7f07026f
com.dhass.myapp:dimen/test_navigation_bar_elevation = 0x7f070269
com.dhass.myapp:dimen/test_navigation_bar_active_text_size = 0x7f070268
com.dhass.myapp:id/exo_rew = 0x7f0a00fc
com.dhass.myapp:drawable/exo_ic_check = 0x7f0800a0
com.dhass.myapp:id/showTitle = 0x7f0a0208
com.dhass.myapp:dimen/test_mtrl_calendar_day_cornerSize = 0x7f070265
com.dhass.myapp:id/mtrl_calendar_text_input_frame = 0x7f0a0190
com.dhass.myapp:dimen/notification_top_pad = 0x7f07025b
com.dhass.myapp:animator/design_fab_hide_motion_spec = 0x7f020001
com.dhass.myapp:dimen/notification_small_icon_background_padding = 0x7f070258
com.dhass.myapp:string/ic_flip_24_horizontally = 0x7f1200af
com.dhass.myapp:dimen/notification_right_side_padding_top = 0x7f070257
com.dhass.myapp:attr/dividerColor = 0x7f04017a
com.dhass.myapp:dimen/notification_media_narrow_margin = 0x7f070255
com.dhass.myapp:dimen/mtrl_navigation_item_horizontal_padding = 0x7f07020e
com.dhass.myapp:dimen/notification_content_margin_start = 0x7f070251
com.dhass.myapp:id/chip3 = 0x7f0a008f
com.dhass.myapp:style/TextAppearance.AppCompat.Caption = 0x7f1301be
com.dhass.myapp:dimen/notification_big_circle_margin = 0x7f070250
com.dhass.myapp:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.dhass.myapp:dimen/notification_action_icon_size = 0x7f07024e
com.dhass.myapp:attr/firstBaselineToTopHeight = 0x7f0401d0
com.dhass.myapp:dimen/mtrl_tooltip_padding = 0x7f07024c
com.dhass.myapp:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f1300f9
com.dhass.myapp:dimen/mtrl_tooltip_minWidth = 0x7f07024b
com.dhass.myapp:styleable/PopupWindowBackgroundState = 0x7f140073
com.dhass.myapp:dimen/mtrl_tooltip_cornerSize = 0x7f070249
com.dhass.myapp:attr/clickAction = 0x7f0400c9
com.dhass.myapp:attr/listPreferredItemPaddingRight = 0x7f0402b3
com.dhass.myapp:dimen/mtrl_toolbar_default_height = 0x7f070247
com.dhass.myapp:dimen/mtrl_textinput_start_icon_margin_end = 0x7f070246
com.dhass.myapp:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f070245
com.dhass.myapp:id/material_clock_period_am_button = 0x7f0a016a
com.dhass.myapp:attr/roundingBorderColor = 0x7f04037e
com.dhass.myapp:dimen/mtrl_textinput_counter_margin_start = 0x7f070243
com.dhass.myapp:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f07017a
com.dhass.myapp:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070240
com.dhass.myapp:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f07023e
com.dhass.myapp:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f130148
com.dhass.myapp:string/character_counter_content_description = 0x7f120042
com.dhass.myapp:dimen/mtrl_calendar_year_vertical_padding = 0x7f0701df
com.dhass.myapp:dimen/mtrl_snackbar_background_corner_radius = 0x7f070238
com.dhass.myapp:attr/deltaPolarAngle = 0x7f040172
com.dhass.myapp:drawable/abc_list_selector_disabled_holo_light = 0x7f080033
com.dhass.myapp:dimen/m3_btn_padding_bottom = 0x7f0700f3
com.dhass.myapp:dimen/mtrl_slider_track_height = 0x7f070233
com.dhass.myapp:string/material_motion_easing_decelerated = 0x7f1200d2
com.dhass.myapp:dimen/m3_chip_corner_size = 0x7f070106
com.dhass.myapp:dimen/mtrl_slider_thumb_radius = 0x7f070232
com.dhass.myapp:string/catalyst_heap_capture = 0x7f120031
com.dhass.myapp:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f060174
com.dhass.myapp:dimen/mtrl_slider_label_radius = 0x7f07022f
com.dhass.myapp:id/action_menu_divider = 0x7f0a0049
com.dhass.myapp:attr/trackTint = 0x7f040470
com.dhass.myapp:dimen/mtrl_shape_corner_size_medium_component = 0x7f07022b
com.dhass.myapp:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f070228
com.dhass.myapp:style/ShapeAppearanceOverlay.Cut = 0x7f130197
com.dhass.myapp:dimen/mtrl_progress_circular_inset = 0x7f07021c
com.dhass.myapp:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f07021a
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1303cd
com.dhass.myapp:attr/iconTintMode = 0x7f040220
com.dhass.myapp:dimen/mtrl_navigation_rail_margin = 0x7f070219
com.dhass.myapp:font/roboto_medium_numbers = 0x7f090000
com.dhass.myapp:dimen/mtrl_navigation_rail_icon_size = 0x7f070218
com.dhass.myapp:attr/materialAlertDialogTitleIconStyle = 0x7f0402ca
com.dhass.myapp:id/compress = 0x7f0a009b
com.dhass.myapp:id/accessibility_state = 0x7f0a003a
com.dhass.myapp:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1302d1
com.dhass.myapp:style/Theme.Material3.DynamicColors.Light = 0x7f13026c
com.dhass.myapp:dimen/mtrl_navigation_rail_elevation = 0x7f070216
com.dhass.myapp:color/exo_white = 0x7f060075
com.dhass.myapp:dimen/mtrl_navigation_rail_active_text_size = 0x7f070213
com.dhass.myapp:dimen/mtrl_navigation_item_icon_size = 0x7f070210
com.dhass.myapp:string/exo_download_failed = 0x7f120084
com.dhass.myapp:dimen/mtrl_navigation_item_icon_padding = 0x7f07020f
com.dhass.myapp:id/fill_vertical = 0x7f0a0112
com.dhass.myapp:attr/switchMinWidth = 0x7f0403e1
com.dhass.myapp:dimen/mtrl_navigation_bar_item_default_margin = 0x7f07020c
com.dhass.myapp:styleable/ConstraintLayout_placeholder = 0x7f140029
com.dhass.myapp:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f13041f
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1302fd
com.dhass.myapp:dimen/mtrl_min_touch_target_size = 0x7f07020a
com.dhass.myapp:dimen/mtrl_low_ripple_focused_alpha = 0x7f070207
com.dhass.myapp:dimen/mtrl_btn_max_width = 0x7f0701aa
com.dhass.myapp:id/end_padder = 0x7f0a00d3
com.dhass.myapp:dimen/mtrl_low_ripple_default_alpha = 0x7f070206
com.dhass.myapp:drawable/abc_vector_test = 0x7f080054
com.dhass.myapp:style/Base.V24.Theme.Material3.Light = 0x7f1300a9
com.dhass.myapp:dimen/mtrl_large_touch_target = 0x7f070205
com.dhass.myapp:dimen/mtrl_high_ripple_pressed_alpha = 0x7f070204
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1301a3
com.dhass.myapp:style/Base.V7.Theme.AppCompat.Light = 0x7f1300b2
com.dhass.myapp:dimen/abc_action_button_min_height_material = 0x7f07000d
com.dhass.myapp:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0701ff
com.dhass.myapp:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0701fc
com.dhass.myapp:dimen/abc_text_size_display_4_material = 0x7f070046
com.dhass.myapp:id/accessibility_custom_action_25 = 0x7f0a0028
com.dhass.myapp:id/rectangleHorizontalOnly = 0x7f0a01d0
com.dhass.myapp:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0701fb
com.dhass.myapp:dimen/mtrl_extended_fab_top_padding = 0x7f0701f9
com.dhass.myapp:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0701f8
com.dhass.myapp:attr/materialCalendarMonth = 0x7f0402da
com.dhass.myapp:drawable/abc_switch_track_mtrl_alpha = 0x7f080048
com.dhass.myapp:drawable/abc_list_selector_holo_light = 0x7f080035
com.dhass.myapp:attr/popupMenuBackground = 0x7f04034e
com.dhass.myapp:attr/triggerReceiver = 0x7f040478
com.dhass.myapp:dimen/mtrl_extended_fab_start_padding = 0x7f0701f7
com.dhass.myapp:id/chain = 0x7f0a0087
com.dhass.myapp:id/accessibility_custom_action_0 = 0x7f0a0016
com.dhass.myapp:dimen/mtrl_extended_fab_icon_size = 0x7f0701f3
com.dhass.myapp:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0701f2
com.dhass.myapp:attr/tabIndicator = 0x7f0403ea
com.dhass.myapp:id/custom = 0x7f0a00a8
com.dhass.myapp:dimen/mtrl_extended_fab_elevation = 0x7f0701f0
com.dhass.myapp:attr/motionEasingLinear = 0x7f04030d
com.dhass.myapp:dimen/mtrl_extended_fab_corner_radius = 0x7f0701ed
com.dhass.myapp:color/background_material_dark = 0x7f06001f
com.dhass.myapp:dimen/mtrl_extended_fab_bottom_padding = 0x7f0701ec
com.dhass.myapp:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0701eb
com.dhass.myapp:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0701ea
com.dhass.myapp:color/material_dynamic_secondary10 = 0x7f0601e9
com.dhass.myapp:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0701e9
com.dhass.myapp:dimen/m3_card_elevated_dragged_z = 0x7f0700ff
com.dhass.myapp:drawable/abc_star_black_48dp = 0x7f080045
com.dhass.myapp:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1300a0
com.dhass.myapp:id/labeled = 0x7f0a0157
com.dhass.myapp:dimen/mtrl_chip_text_size = 0x7f0701e8
com.dhass.myapp:attr/contentPaddingEnd = 0x7f04011a
com.dhass.myapp:attr/clockHandColor = 0x7f0400cb
com.dhass.myapp:dimen/mtrl_card_elevation = 0x7f0701e5
com.dhass.myapp:drawable/mtrl_ic_arrow_drop_down = 0x7f08010e
com.dhass.myapp:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020014
com.dhass.myapp:dimen/mtrl_card_checked_icon_margin = 0x7f0701e1
com.dhass.myapp:color/material_dynamic_primary100 = 0x7f0601dd
com.dhass.myapp:dimen/mtrl_calendar_year_width = 0x7f0701e0
com.dhass.myapp:drawable/exo_controls_pause = 0x7f080094
com.dhass.myapp:dimen/mtrl_calendar_year_height = 0x7f0701dd
com.dhass.myapp:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0701db
com.dhass.myapp:dimen/mtrl_calendar_text_input_padding_top = 0x7f0701d9
com.dhass.myapp:id/fps_text = 0x7f0a0123
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.OverflowShow = 0x7f130130
com.dhass.myapp:color/material_dynamic_secondary30 = 0x7f0601ec
com.dhass.myapp:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0701d8
com.dhass.myapp:color/exo_error_message_background_color = 0x7f060073
com.dhass.myapp:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0701d6
com.dhass.myapp:color/m3_ref_palette_primary20 = 0x7f060118
com.dhass.myapp:id/barrier = 0x7f0a006b
com.dhass.myapp:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0701d5
com.dhass.myapp:id/exo_track_selection_view = 0x7f0a0107
com.dhass.myapp:id/fade = 0x7f0a010b
com.dhass.myapp:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0701d4
com.dhass.myapp:id/date_picker_actions = 0x7f0a00ac
com.dhass.myapp:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0701d1
com.dhass.myapp:drawable/rzp_white_border_black_bg = 0x7f080140
com.dhass.myapp:attr/pressedTranslationZ = 0x7f040358
com.dhass.myapp:dimen/mtrl_calendar_month_vertical_padding = 0x7f0701d0
com.dhass.myapp:id/arc = 0x7f0a0060
com.dhass.myapp:drawable/exo_icon_repeat_off = 0x7f0800b9
com.dhass.myapp:string/exo_controls_cc_enabled_description = 0x7f120068
com.dhass.myapp:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0701ce
com.dhass.myapp:id/exo_subtitles = 0x7f0a0104
com.dhass.myapp:dimen/abc_button_padding_vertical_material = 0x7f070015
com.dhass.myapp:dimen/mtrl_calendar_landscape_header_width = 0x7f0701cd
com.dhass.myapp:dimen/mtrl_calendar_header_text_padding = 0x7f0701ca
com.dhass.myapp:dimen/mtrl_calendar_header_selection_line_height = 0x7f0701c9
com.dhass.myapp:style/ShapeAppearanceOverlay.BottomLeftDifferentCornerSize = 0x7f130195
com.dhass.myapp:dimen/mtrl_calendar_header_height_fullscreen = 0x7f0701c8
com.dhass.myapp:style/Widget.Material3.Chip.Assist.Elevated = 0x7f130391
com.dhass.myapp:id/bounce = 0x7f0a0072
com.dhass.myapp:dimen/compat_notification_large_icon_max_width = 0x7f070060
com.dhass.myapp:dimen/mtrl_calendar_header_height = 0x7f0701c7
com.dhass.myapp:dimen/mtrl_calendar_header_divider_thickness = 0x7f0701c6
com.dhass.myapp:style/Theme.Material3.Light = 0x7f13026d
com.dhass.myapp:string/call_notification_screening_text = 0x7f120027
com.dhass.myapp:attr/submitBackground = 0x7f0403d5
com.dhass.myapp:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f0701c5
com.dhass.myapp:color/material_dynamic_neutral0 = 0x7f0601c1
com.dhass.myapp:dimen/mtrl_calendar_header_content_padding = 0x7f0701c4
com.dhass.myapp:id/action_context_bar = 0x7f0a0046
com.dhass.myapp:dimen/mtrl_calendar_dialog_background_inset = 0x7f0701c3
com.dhass.myapp:dimen/mtrl_calendar_days_of_week_height = 0x7f0701c2
com.dhass.myapp:attr/indeterminateAnimationType = 0x7f040226
com.dhass.myapp:dimen/mtrl_calendar_day_width = 0x7f0701c1
com.dhass.myapp:dimen/mtrl_calendar_day_horizontal_padding = 0x7f0701be
com.dhass.myapp:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_searchicon = 0x7f080123
com.dhass.myapp:dimen/mtrl_calendar_action_height = 0x7f0701b8
com.dhass.myapp:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f0701b7
com.dhass.myapp:color/m3_switch_thumb_tint = 0x7f060143
com.dhass.myapp:dimen/mtrl_btn_text_btn_padding_left = 0x7f0701b3
com.dhass.myapp:attr/alpha = 0x7f04002f
com.dhass.myapp:dimen/mtrl_btn_stroke_size = 0x7f0701b1
com.dhass.myapp:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.dhass.myapp:dimen/mtrl_btn_pressed_z = 0x7f0701af
com.dhass.myapp:id/exo_next = 0x7f0a00ef
com.dhass.myapp:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f13042a
com.dhass.myapp:dimen/m3_sys_elevation_level3 = 0x7f070137
com.dhass.myapp:dimen/mtrl_btn_padding_top = 0x7f0701ae
com.dhass.myapp:dimen/mtrl_btn_padding_right = 0x7f0701ad
com.dhass.myapp:styleable/View = 0x7f140099
com.dhass.myapp:dimen/mtrl_btn_padding_left = 0x7f0701ac
com.dhass.myapp:drawable/exo_styled_controls_repeat_one = 0x7f0800d6
com.dhass.myapp:attr/behavior_autoHide = 0x7f040062
com.dhass.myapp:color/catalyst_redbox_background = 0x7f060034
com.dhass.myapp:dimen/mtrl_low_ripple_pressed_alpha = 0x7f070209
com.dhass.myapp:dimen/mtrl_btn_padding_bottom = 0x7f0701ab
com.dhass.myapp:attr/closeIconSize = 0x7f0400d1
com.dhass.myapp:dimen/notification_large_icon_width = 0x7f070253
com.dhass.myapp:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1302da
com.dhass.myapp:dimen/mtrl_btn_inset = 0x7f0701a8
com.dhass.myapp:dimen/mtrl_btn_icon_padding = 0x7f0701a7
com.dhass.myapp:id/exo_settings_listview = 0x7f0a00ff
com.dhass.myapp:dimen/mtrl_btn_icon_btn_padding_left = 0x7f0701a6
com.dhass.myapp:dimen/mtrl_btn_dialog_btn_min_width = 0x7f0701a0
com.dhass.myapp:integer/m3_sys_motion_path = 0x7f0b0022
com.dhass.myapp:color/m3_ref_palette_error50 = 0x7f0600f4
com.dhass.myapp:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07019c
com.dhass.myapp:string/call_notification_answer_action = 0x7f120021
com.dhass.myapp:drawable/exo_ic_skip_previous = 0x7f0800ac
com.dhass.myapp:dimen/material_text_size_dp = 0x7f070182
com.dhass.myapp:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f07019b
com.dhass.myapp:id/media_controller_compat_view_tag = 0x7f0a017c
com.dhass.myapp:color/material_dynamic_tertiary60 = 0x7f0601fc
com.dhass.myapp:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070191
com.dhass.myapp:attr/rippleColor = 0x7f040370
com.dhass.myapp:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f070190
com.dhass.myapp:id/META = 0x7f0a0008
com.dhass.myapp:attr/listPopupWindowStyle = 0x7f0402ad
com.dhass.myapp:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f07018b
com.dhass.myapp:string/fallback_menu_item_share_link = 0x7f1200a5
com.dhass.myapp:dimen/splashscreen_icon_size = 0x7f070261
com.dhass.myapp:drawable/exo_styled_controls_shuffle_off = 0x7f0800d9
com.dhass.myapp:dimen/material_time_picker_minimum_screen_width = 0x7f07018a
com.dhass.myapp:dimen/material_time_picker_minimum_screen_height = 0x7f070189
com.dhass.myapp:id/right_icon = 0x7f0a01d8
com.dhass.myapp:dimen/material_textinput_max_width = 0x7f070187
com.dhass.myapp:dimen/material_textinput_default_width = 0x7f070186
com.dhass.myapp:attr/navigationIcon = 0x7f04031a
com.dhass.myapp:dimen/material_text_view_test_line_height_override = 0x7f070185
com.dhass.myapp:dimen/material_text_view_test_line_height = 0x7f070184
com.dhass.myapp:dimen/material_text_size_sp = 0x7f070183
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1303b2
com.dhass.myapp:dimen/material_helper_text_font_1_3_padding_top = 0x7f070180
com.dhass.myapp:drawable/exo_icon_next = 0x7f0800b4
com.dhass.myapp:color/design_default_color_secondary_variant = 0x7f06005d
com.dhass.myapp:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f07017d
com.dhass.myapp:color/mtrl_btn_text_btn_bg_color_selector = 0x7f060225
com.dhass.myapp:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f070179
com.dhass.myapp:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f130062
com.dhass.myapp:id/action_bar_root = 0x7f0a0041
com.dhass.myapp:attr/transitionDisable = 0x7f040472
com.dhass.myapp:dimen/material_emphasis_high_type = 0x7f070176
com.dhass.myapp:dimen/material_emphasis_disabled_background = 0x7f070175
com.dhass.myapp:dimen/material_emphasis_disabled = 0x7f070174
com.dhass.myapp:style/Theme.Material3.Dark.NoActionBar = 0x7f130262
com.dhass.myapp:string/template_percent = 0x7f120129
com.dhass.myapp:attr/drawerLayoutCornerSize = 0x7f04018f
com.dhass.myapp:dimen/material_cursor_width = 0x7f070172
com.dhass.myapp:dimen/material_clock_size = 0x7f07016f
com.dhass.myapp:dimen/material_clock_period_toggle_width = 0x7f07016e
com.dhass.myapp:style/Base.Widget.MaterialComponents.Chip = 0x7f130107
com.dhass.myapp:drawable/exo_controls_fullscreen_enter = 0x7f080091
com.dhass.myapp:dimen/material_clock_number_text_padding = 0x7f07016a
com.dhass.myapp:dimen/material_clock_hand_stroke_width = 0x7f070169
com.dhass.myapp:layout/mtrl_calendar_month_navigation = 0x7f0d0063
com.dhass.myapp:dimen/material_clock_hand_padding = 0x7f070168
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f13042f
com.dhass.myapp:id/view_tree_saved_state_registry_owner = 0x7f0a0272
com.dhass.myapp:id/inspection_slot_table_set = 0x7f0a014d
com.dhass.myapp:dimen/material_clock_hand_center_dot_radius = 0x7f070167
com.dhass.myapp:dimen/material_clock_display_padding = 0x7f070165
com.dhass.myapp:style/Widget.AppCompat.CompoundButton.Switch = 0x7f130328
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f130022
com.dhass.myapp:dimen/material_bottom_sheet_max_width = 0x7f070164
com.dhass.myapp:string/call_notification_hang_up_action = 0x7f120024
com.dhass.myapp:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070160
com.dhass.myapp:drawable/exo_notification_play = 0x7f0800c3
com.dhass.myapp:dimen/material_divider_thickness = 0x7f070173
com.dhass.myapp:integer/m3_card_anim_delay_ms = 0x7f0b000f
com.dhass.myapp:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f07015f
com.dhass.myapp:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f07015e
com.dhass.myapp:attr/dialogCornerRadius = 0x7f040175
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f07015b
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070158
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070154
com.dhass.myapp:drawable/abc_control_background_material = 0x7f080018
com.dhass.myapp:attr/windowActionBar = 0x7f040496
com.dhass.myapp:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070150
com.dhass.myapp:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1302d2
com.dhass.myapp:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f13002a
com.dhass.myapp:id/exo_overlay = 0x7f0a00f2
com.dhass.myapp:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f07014e
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f07014c
com.dhass.myapp:id/accessibility_custom_action_10 = 0x7f0a0018
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f07014a
com.dhass.myapp:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070181
com.dhass.myapp:color/m3_primary_text_disable_only = 0x7f0600aa
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070148
com.dhass.myapp:attr/chipIconEnabled = 0x7f0400b7
com.dhass.myapp:id/accessibility_role = 0x7f0a0039
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f070145
com.dhass.myapp:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f130147
com.dhass.myapp:string/material_timepicker_clock_mode_description = 0x7f1200d9
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f070144
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f070143
com.dhass.myapp:attr/maxActionInlineWidth = 0x7f0402ed
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f070142
com.dhass.myapp:id/parent_matrix = 0x7f0a01bb
com.dhass.myapp:attr/expandedTitleMargin = 0x7f0401b3
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070141
com.dhass.myapp:string/google_storage_bucket = 0x7f1200ab
com.dhass.myapp:id/exo_check = 0x7f0a00df
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f07013f
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f07013c
com.dhass.myapp:dimen/m3_sys_elevation_level1 = 0x7f070135
com.dhass.myapp:attr/appBarLayoutStyle = 0x7f040036
com.dhass.myapp:dimen/m3_snackbar_margin = 0x7f070133
com.dhass.myapp:dimen/m3_snackbar_action_text_color_alpha = 0x7f070132
com.dhass.myapp:attr/resize_mode = 0x7f04036c
com.dhass.myapp:id/focusCrop = 0x7f0a0121
com.dhass.myapp:drawable/rns_rounder_top_corners_shape = 0x7f080137
com.dhass.myapp:color/browser_actions_text_color = 0x7f060029
com.dhass.myapp:dimen/m3_ripple_selectable_pressed_alpha = 0x7f070130
com.dhass.myapp:id/dragEnd = 0x7f0a00c1
com.dhass.myapp:dimen/m3_ripple_pressed_alpha = 0x7f07012f
com.dhass.myapp:dimen/m3_ripple_hovered_alpha = 0x7f07012e
com.dhass.myapp:dimen/m3_ripple_focused_alpha = 0x7f07012d
com.dhass.myapp:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f070225
com.dhass.myapp:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f07014f
com.dhass.myapp:dimen/m3_ripple_default_alpha = 0x7f07012c
com.dhass.myapp:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f13032a
com.dhass.myapp:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1301c9
com.dhass.myapp:dimen/m3_navigation_rail_item_padding_bottom = 0x7f07012a
com.dhass.myapp:style/Widget.Material3.Button.TonalButton.Icon = 0x7f13038a
com.dhass.myapp:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f130083
com.dhass.myapp:dimen/m3_btn_translation_z_base = 0x7f0700fc
com.dhass.myapp:dimen/m3_navigation_rail_item_min_height = 0x7f070129
com.dhass.myapp:dimen/m3_bottom_sheet_elevation = 0x7f0700e0
com.dhass.myapp:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f070128
com.dhass.myapp:color/dim_foreground_material_light = 0x7f06006c
com.dhass.myapp:dimen/m3_navigation_item_vertical_padding = 0x7f070122
com.dhass.myapp:id/save_overlay_view = 0x7f0a01ed
com.dhass.myapp:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f070211
com.dhass.myapp:dimen/m3_navigation_item_shape_inset_top = 0x7f070121
com.dhass.myapp:attr/chipGroupStyle = 0x7f0400b5
com.dhass.myapp:dimen/m3_menu_elevation = 0x7f07011a
com.dhass.myapp:dimen/m3_fab_translation_z_hovered_focused = 0x7f070116
com.dhass.myapp:string/exo_download_paused = 0x7f120086
com.dhass.myapp:dimen/mtrl_high_ripple_default_alpha = 0x7f070201
com.dhass.myapp:color/iconBackground = 0x7f06007b
com.dhass.myapp:dimen/m3_fab_border_width = 0x7f070114
com.dhass.myapp:attr/colorOnContainer = 0x7f0400ec
com.dhass.myapp:dimen/m3_extended_fab_icon_padding = 0x7f070110
com.dhass.myapp:dimen/m3_chip_hovered_translation_z = 0x7f07010a
com.dhass.myapp:drawable/common_google_signin_btn_icon_dark = 0x7f080077
com.dhass.myapp:dimen/m3_chip_disabled_translation_z = 0x7f070107
com.dhass.myapp:color/secondary_text_disabled_material_light = 0x7f060266
com.dhass.myapp:dimen/m3_chip_checked_hovered_translation_z = 0x7f070105
com.dhass.myapp:dimen/m3_card_hovered_z = 0x7f070103
com.dhass.myapp:drawable/exo_styled_controls_previous = 0x7f0800d3
com.dhass.myapp:dimen/m3_card_elevation = 0x7f070102
com.dhass.myapp:dimen/m3_card_dragged_z = 0x7f0700fe
com.dhass.myapp:styleable/Constraint = 0x7f140027
com.dhass.myapp:styleable/ClockFaceView = 0x7f140021
com.dhass.myapp:dimen/m3_btn_translation_z_hovered = 0x7f0700fd
com.dhass.myapp:dimen/material_clock_face_margin_top = 0x7f070166
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.RepeatToggle = 0x7f130132
com.dhass.myapp:dimen/m3_btn_padding_top = 0x7f0700f6
com.dhass.myapp:dimen/m3_btn_padding_right = 0x7f0700f5
com.dhass.myapp:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08003d
com.dhass.myapp:id/scale = 0x7f0a01ef
com.dhass.myapp:dimen/m3_btn_max_width = 0x7f0700f2
com.dhass.myapp:drawable/material_ic_edit_black_24dp = 0x7f080105
com.dhass.myapp:dimen/m3_btn_inset = 0x7f0700f1
com.dhass.myapp:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.dhass.myapp:dimen/m3_btn_icon_only_min_width = 0x7f0700f0
com.dhass.myapp:dimen/test_dimen = 0x7f070264
com.dhass.myapp:id/always = 0x7f0a0057
com.dhass.myapp:dimen/m3_btn_icon_only_default_size = 0x7f0700ee
com.dhass.myapp:style/Base.Widget.AppCompat.PopupWindow = 0x7f1300e2
com.dhass.myapp:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f070260
com.dhass.myapp:dimen/m3_btn_icon_only_default_padding = 0x7f0700ed
com.dhass.myapp:dimen/m3_btn_disabled_translation_z = 0x7f0700e8
com.dhass.myapp:color/material_dynamic_neutral_variant99 = 0x7f0601da
com.dhass.myapp:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700e4
com.dhass.myapp:style/Widget.MaterialComponents.Tooltip = 0x7f130473
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1301e3
com.dhass.myapp:string/pick_image_gallery = 0x7f12010f
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f07015d
com.dhass.myapp:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700e2
com.dhass.myapp:dimen/test_navigation_bar_height = 0x7f07026a
com.dhass.myapp:dimen/m3_bottom_nav_min_height = 0x7f0700df
com.dhass.myapp:attr/isMaterialTheme = 0x7f040232
com.dhass.myapp:dimen/m3_bottom_nav_item_padding_top = 0x7f0700de
com.dhass.myapp:attr/latLngBoundsSouthWestLongitude = 0x7f04025d
com.dhass.myapp:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700db
com.dhass.myapp:id/mtrl_picker_text_input_date = 0x7f0a019b
com.dhass.myapp:dimen/hint_alpha_material_light = 0x7f0700bf
com.dhass.myapp:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700da
com.dhass.myapp:xml/image_picker_provider_paths = 0x7f150003
com.dhass.myapp:layout/design_navigation_item = 0x7f0d002c
com.dhass.myapp:dimen/m3_badge_with_text_radius = 0x7f0700d8
com.dhass.myapp:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700d7
com.dhass.myapp:attr/motionDurationLong2 = 0x7f040305
com.dhass.myapp:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f080107
com.dhass.myapp:drawable/exo_controls_repeat_one = 0x7f080099
com.dhass.myapp:dimen/m3_badge_radius = 0x7f0700d5
com.dhass.myapp:id/react_test_id = 0x7f0a01ce
com.dhass.myapp:id/fit = 0x7f0a0115
com.dhass.myapp:dimen/m3_badge_horizontal_offset = 0x7f0700d4
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f07014b
com.dhass.myapp:id/tag_unhandled_key_listeners = 0x7f0a0238
com.dhass.myapp:id/radio = 0x7f0a01cc
com.dhass.myapp:dimen/m3_appbar_size_medium = 0x7f0700d3
com.dhass.myapp:layout/test_exposed_dropdown_menu = 0x7f0d0095
com.dhass.myapp:dimen/m3_appbar_size_large = 0x7f0700d2
com.dhass.myapp:dimen/m3_appbar_size_compact = 0x7f0700d1
com.dhass.myapp:string/exo_controls_next_description = 0x7f12006e
com.dhass.myapp:attr/errorContentDescription = 0x7f0401a8
com.dhass.myapp:dimen/mtrl_progress_circular_inset_small = 0x7f07021f
com.dhass.myapp:style/Widget.MaterialComponents.ActionMode = 0x7f1303fb
com.dhass.myapp:dimen/m3_appbar_scrim_height_trigger = 0x7f0700ce
com.dhass.myapp:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1303fd
com.dhass.myapp:string/expo_splash_screen_resize_mode = 0x7f12009d
com.dhass.myapp:color/m3_ref_palette_primary70 = 0x7f06011d
com.dhass.myapp:dimen/mtrl_shape_corner_size_large_component = 0x7f07022a
com.dhass.myapp:dimen/m3_navigation_item_horizontal_padding = 0x7f07011c
com.dhass.myapp:dimen/m3_alert_dialog_icon_size = 0x7f0700ca
com.dhass.myapp:dimen/m3_alert_dialog_elevation = 0x7f0700c8
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f13043d
com.dhass.myapp:dimen/m3_alert_dialog_corner_size = 0x7f0700c7
com.dhass.myapp:dimen/m3_alert_dialog_action_top_padding = 0x7f0700c6
com.dhass.myapp:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700c4
com.dhass.myapp:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f0700c3
com.dhass.myapp:color/mtrl_btn_bg_color_selector = 0x7f060222
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07015a
com.dhass.myapp:attr/roundedCornerRadius = 0x7f04037d
com.dhass.myapp:color/material_timepicker_clockface = 0x7f060220
com.dhass.myapp:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f0700c2
com.dhass.myapp:color/m3_sys_color_dark_on_surface_variant = 0x7f060153
com.dhass.myapp:dimen/design_bottom_sheet_modal_elevation = 0x7f070071
com.dhass.myapp:drawable/common_google_signin_btn_icon_light_normal = 0x7f08007e
com.dhass.myapp:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f130295
com.dhass.myapp:dimen/m3_large_fab_size = 0x7f070119
com.dhass.myapp:dimen/hint_alpha_material_dark = 0x7f0700be
com.dhass.myapp:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f130110
com.dhass.myapp:attr/chipBackgroundColor = 0x7f0400b2
com.dhass.myapp:dimen/highlight_alpha_material_light = 0x7f0700bd
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f130309
com.dhass.myapp:color/mtrl_tabs_icon_color_selector = 0x7f06024b
com.dhass.myapp:drawable/exo_styled_controls_overflow_show = 0x7f0800d0
com.dhass.myapp:dimen/highlight_alpha_material_colored = 0x7f0700bb
com.dhass.myapp:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.dhass.myapp:id/chip1 = 0x7f0a008d
com.dhass.myapp:dimen/fastscroll_minimum_range = 0x7f0700ba
com.dhass.myapp:integer/m3_sys_motion_duration_200 = 0x7f0b0015
com.dhass.myapp:dimen/fastscroll_margin = 0x7f0700b9
com.dhass.myapp:dimen/fastscroll_default_thickness = 0x7f0700b8
com.dhass.myapp:dimen/exo_styled_progress_touch_target_height = 0x7f0700b7
com.dhass.myapp:color/material_dynamic_neutral_variant80 = 0x7f0601d7
com.dhass.myapp:dimen/exo_styled_progress_margin_bottom = 0x7f0700b6
com.dhass.myapp:dimen/exo_styled_progress_enabled_thumb_size = 0x7f0700b4
com.dhass.myapp:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0701da
com.dhass.myapp:dimen/exo_styled_progress_dragged_thumb_size = 0x7f0700b3
com.dhass.myapp:layout/test_design_checkbox = 0x7f0d0093
com.dhass.myapp:dimen/exo_styled_minimal_controls_margin_bottom = 0x7f0700b1
com.dhass.myapp:string/abc_menu_delete_shortcut_label = 0x7f12000a
com.dhass.myapp:dimen/exo_styled_controls_padding = 0x7f0700b0
com.dhass.myapp:id/adjust_height = 0x7f0a0052
com.dhass.myapp:dimen/exo_styled_bottom_bar_margin_top = 0x7f0700ae
com.dhass.myapp:dimen/exo_styled_bottom_bar_height = 0x7f0700ad
com.dhass.myapp:id/clip_vertical = 0x7f0a0096
com.dhass.myapp:id/material_timepicker_cancel_button = 0x7f0a0173
com.dhass.myapp:dimen/exo_small_icon_padding_vertical = 0x7f0700ab
com.dhass.myapp:layout/abc_action_mode_bar = 0x7f0d0004
com.dhass.myapp:dimen/exo_small_icon_padding_horizontal = 0x7f0700aa
com.dhass.myapp:style/Widget.Material3.Chip.Filter = 0x7f130392
com.dhass.myapp:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700dc
com.dhass.myapp:dimen/exo_small_icon_height = 0x7f0700a8
com.dhass.myapp:style/Base.Widget.MaterialComponents.Slider = 0x7f13010e
com.dhass.myapp:dimen/exo_settings_sub_text_size = 0x7f0700a6
com.dhass.myapp:style/TextAppearance.Material3.TitleMedium = 0x7f13021e
com.dhass.myapp:attr/cropMultiTouchEnabled = 0x7f040151
com.dhass.myapp:id/floating = 0x7f0a0120
com.dhass.myapp:attr/viewAspectRatio = 0x7f04048c
com.dhass.myapp:dimen/exo_settings_offset = 0x7f0700a5
com.dhass.myapp:dimen/exo_settings_icon_size = 0x7f0700a3
com.dhass.myapp:attr/materialCardViewOutlinedStyle = 0x7f0402e1
com.dhass.myapp:dimen/exo_setting_width = 0x7f0700a1
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f130440
com.dhass.myapp:drawable/exo_icon_stop = 0x7f0800be
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f13006c
com.dhass.myapp:dimen/exo_media_button_width = 0x7f0700a0
com.dhass.myapp:string/abc_searchview_description_clear = 0x7f120013
com.dhass.myapp:dimen/exo_media_button_height = 0x7f07009f
com.dhass.myapp:drawable/notification_bg_low = 0x7f080126
com.dhass.myapp:dimen/exo_icon_padding = 0x7f07009b
com.dhass.myapp:dimen/exo_icon_horizontal_margin = 0x7f07009a
com.dhass.myapp:interpolator/mtrl_linear_out_slow_in = 0x7f0c000a
com.dhass.myapp:color/wallet_primary_text_holo_light = 0x7f060280
com.dhass.myapp:dimen/exo_error_message_text_size = 0x7f070099
com.dhass.myapp:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f130241
com.dhass.myapp:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f07017b
com.dhass.myapp:attr/fabCustomSize = 0x7f0401c6
com.dhass.myapp:dimen/disabled_alpha_material_light = 0x7f070094
com.dhass.myapp:attr/navigationMode = 0x7f04031c
com.dhass.myapp:dimen/design_tab_scrollable_min_width = 0x7f07008f
com.dhass.myapp:dimen/design_tab_max_width = 0x7f07008e
com.dhass.myapp:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1303f8
com.dhass.myapp:layout/browser_actions_context_menu_page = 0x7f0d0020
com.dhass.myapp:dimen/design_snackbar_text_size = 0x7f07008d
com.dhass.myapp:id/design_menu_item_text = 0x7f0a00b5
com.dhass.myapp:dimen/design_snackbar_padding_vertical_2lines = 0x7f07008c
com.dhass.myapp:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f070242
com.dhass.myapp:dimen/exo_error_message_text_padding_vertical = 0x7f070098
com.dhass.myapp:id/text2 = 0x7f0a0241
com.dhass.myapp:dimen/design_snackbar_padding_horizontal = 0x7f07008a
com.dhass.myapp:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f130086
com.dhass.myapp:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070087
com.dhass.myapp:attr/listLayout = 0x7f0402ab
com.dhass.myapp:attr/startIconDrawable = 0x7f0403c2
com.dhass.myapp:dimen/design_snackbar_action_inline_max_width = 0x7f070083
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600c4
com.dhass.myapp:dimen/design_navigation_separator_vertical_padding = 0x7f070082
com.dhass.myapp:dimen/design_navigation_padding_bottom = 0x7f070081
com.dhass.myapp:dimen/design_navigation_item_vertical_padding = 0x7f07007f
com.dhass.myapp:drawable/ic_call_answer_low = 0x7f0800e3
com.dhass.myapp:attr/actualImageUri = 0x7f040027
com.dhass.myapp:dimen/design_navigation_item_icon_padding = 0x7f07007e
com.dhass.myapp:dimen/design_navigation_icon_size = 0x7f07007c
com.dhass.myapp:attr/tabGravity = 0x7f0403e7
com.dhass.myapp:attr/tabPadding = 0x7f0403f5
com.dhass.myapp:dimen/design_navigation_icon_padding = 0x7f07007b
com.dhass.myapp:id/rn_redbox_stack = 0x7f0a01e2
com.dhass.myapp:id/android_pay_dark = 0x7f0a005a
com.dhass.myapp:id/fixed_width = 0x7f0a011e
com.dhass.myapp:dimen/design_navigation_elevation = 0x7f07007a
com.dhass.myapp:dimen/design_fab_border_width = 0x7f070073
com.dhass.myapp:dimen/design_bottom_sheet_peek_height_min = 0x7f070072
com.dhass.myapp:attr/actionModeTheme = 0x7f04001d
com.dhass.myapp:dimen/design_bottom_sheet_elevation = 0x7f070070
com.dhass.myapp:id/tag_accessibility_clickable_spans = 0x7f0a022e
com.dhass.myapp:dimen/design_bottom_navigation_shadow_height = 0x7f07006e
com.dhass.myapp:dimen/design_bottom_navigation_label_padding = 0x7f07006c
com.dhass.myapp:layout/mtrl_alert_dialog = 0x7f0d0056
com.dhass.myapp:color/m3_ref_palette_error70 = 0x7f0600f6
com.dhass.myapp:dimen/design_bottom_navigation_item_max_width = 0x7f07006a
com.dhass.myapp:style/Base.AlertDialog.AppCompat = 0x7f13000b
com.dhass.myapp:attr/player_layout_id = 0x7f04034d
com.dhass.myapp:id/accessibility_custom_action_31 = 0x7f0a002f
com.dhass.myapp:dimen/test_navigation_bar_active_item_min_width = 0x7f070267
com.dhass.myapp:dimen/design_bottom_navigation_height = 0x7f070068
com.dhass.myapp:drawable/exo_styled_controls_play = 0x7f0800d2
com.dhass.myapp:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1300d4
com.dhass.myapp:layout/notification_action = 0x7f0d0074
com.dhass.myapp:attr/state_collapsible = 0x7f0403c7
com.dhass.myapp:dimen/design_bottom_navigation_elevation = 0x7f070067
com.dhass.myapp:id/exo_center_controls = 0x7f0a00de
com.dhass.myapp:id/ALT = 0x7f0a0000
com.dhass.myapp:dimen/design_bottom_navigation_active_item_min_width = 0x7f070065
com.dhass.myapp:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001a
com.dhass.myapp:dimen/material_helper_text_default_padding_top = 0x7f07017e
com.dhass.myapp:attr/subtitleTextStyle = 0x7f0403da
com.dhass.myapp:attr/simpleItemLayout = 0x7f0403ad
com.dhass.myapp:dimen/design_bottom_navigation_active_item_max_width = 0x7f070064
com.dhass.myapp:drawable/exo_icon_repeat_one = 0x7f0800ba
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f130333
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f07013e
com.dhass.myapp:dimen/design_appbar_elevation = 0x7f070063
com.dhass.myapp:dimen/default_dimension = 0x7f070062
com.dhass.myapp:dimen/def_drawer_elevation = 0x7f070061
com.dhass.myapp:dimen/compat_button_inset_vertical_material = 0x7f07005b
com.dhass.myapp:id/pin = 0x7f0a01c2
com.dhass.myapp:dimen/compat_button_inset_horizontal_material = 0x7f07005a
com.dhass.myapp:style/Base.Widget.AppCompat.ProgressBar = 0x7f1300e3
com.dhass.myapp:attr/borderWidth = 0x7f04006d
com.dhass.myapp:id/browser_actions_menu_view = 0x7f0a0077
com.dhass.myapp:dimen/clock_face_margin_start = 0x7f070059
com.dhass.myapp:dimen/cardview_default_elevation = 0x7f070057
com.dhass.myapp:id/dropdown_noneditable = 0x7f0a00c8
com.dhass.myapp:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.dhass.myapp:dimen/autofill_inline_suggestion_icon_size = 0x7f070053
com.dhass.myapp:dimen/appcompat_dialog_background_inset = 0x7f070052
com.dhass.myapp:attr/textAppearanceHeadlineMedium = 0x7f040417
com.dhass.myapp:drawable/exo_controls_fastforward = 0x7f080090
com.dhass.myapp:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.dhass.myapp:dimen/abc_text_size_title_material = 0x7f07004f
com.dhass.myapp:color/design_bottom_navigation_shadow_color = 0x7f060043
com.dhass.myapp:dimen/abc_text_size_subhead_material = 0x7f07004d
com.dhass.myapp:styleable/MaterialButtonToggleGroup = 0x7f140055
com.dhass.myapp:attr/titleMargins = 0x7f040456
com.dhass.myapp:dimen/abc_text_size_small_material = 0x7f07004c
com.dhass.myapp:attr/divider = 0x7f040179
com.dhass.myapp:dimen/abc_text_size_menu_material = 0x7f07004b
com.dhass.myapp:color/m3_dark_hint_foreground = 0x7f060091
com.dhass.myapp:dimen/abc_text_size_display_3_material = 0x7f070045
com.dhass.myapp:dimen/abc_text_size_body_2_material = 0x7f070040
com.dhass.myapp:id/off = 0x7f0a01af
com.dhass.myapp:anim/rns_no_animation_medium = 0x7f010039
com.dhass.myapp:dimen/abc_star_small = 0x7f07003d
com.dhass.myapp:dimen/abc_star_medium = 0x7f07003c
com.dhass.myapp:color/m3_sys_color_dynamic_dark_primary_container = 0x7f06016e
com.dhass.myapp:dimen/abc_star_big = 0x7f07003b
com.dhass.myapp:style/Base.V24.Theme.Material3.Dark = 0x7f1300a7
com.dhass.myapp:attr/indicatorSize = 0x7f04022c
com.dhass.myapp:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.dhass.myapp:attr/listDividerAlertDialog = 0x7f0402a9
com.dhass.myapp:id/android_pay_light_with_border = 0x7f0a005c
com.dhass.myapp:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.dhass.myapp:attr/tickColor = 0x7f040443
com.dhass.myapp:attr/cameraTilt = 0x7f040098
com.dhass.myapp:dimen/abc_search_view_preferred_height = 0x7f070036
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialcommunityicons = 0x7f110010
com.dhass.myapp:animator/mtrl_extended_fab_state_list_animator = 0x7f02001b
com.dhass.myapp:attr/materialCardViewElevatedStyle = 0x7f0402df
com.dhass.myapp:dimen/abc_list_item_height_small_material = 0x7f070032
com.dhass.myapp:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f08010a
com.dhass.myapp:string/m3_sys_motion_easing_linear = 0x7f1200c7
com.dhass.myapp:dimen/abc_list_item_height_material = 0x7f070031
com.dhass.myapp:dimen/mtrl_navigation_rail_text_size = 0x7f07021b
com.dhass.myapp:dimen/abc_list_item_height_large_material = 0x7f070030
com.dhass.myapp:string/common_google_play_services_enable_title = 0x7f12004c
com.dhass.myapp:attr/checkedTextViewStyle = 0x7f0400b1
com.dhass.myapp:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.dhass.myapp:attr/mock_diagonalsColor = 0x7f0402fd
com.dhass.myapp:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.dhass.myapp:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.dhass.myapp:drawable/design_ic_visibility_off = 0x7f08008d
com.dhass.myapp:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.dhass.myapp:styleable/AppCompatSeekBar = 0x7f14000f
com.dhass.myapp:color/m3_ref_palette_primary80 = 0x7f06011e
com.dhass.myapp:dimen/design_navigation_max_width = 0x7f070080
com.dhass.myapp:integer/mtrl_calendar_header_orientation = 0x7f0b002d
com.dhass.myapp:dimen/abc_dialog_title_divider_material = 0x7f070026
com.dhass.myapp:string/status_bar_notification_info_overflow = 0x7f120124
com.dhass.myapp:drawable/common_google_signin_btn_text_light = 0x7f080085
com.dhass.myapp:dimen/abc_dialog_padding_top_material = 0x7f070025
com.dhass.myapp:dimen/abc_dialog_min_width_major = 0x7f070022
com.dhass.myapp:styleable/SignInButton = 0x7f14007f
com.dhass.myapp:style/ShapeAppearance.MaterialComponents.Test = 0x7f130192
com.dhass.myapp:dimen/mtrl_alert_dialog_background_inset_start = 0x7f07018e
com.dhass.myapp:id/accessibility_state_expanded = 0x7f0a003b
com.dhass.myapp:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.dhass.myapp:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.dhass.myapp:attr/logo = 0x7f0402b6
com.dhass.myapp:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.dhass.myapp:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f130077
com.dhass.myapp:attr/contentInsetStart = 0x7f040116
com.dhass.myapp:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.dhass.myapp:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.dhass.myapp:dimen/abc_control_padding_material = 0x7f07001a
com.dhass.myapp:color/m3_assist_chip_stroke_color = 0x7f06007e
com.dhass.myapp:dimen/abc_control_inset_material = 0x7f070019
com.dhass.myapp:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.dhass.myapp:id/exo_play = 0x7f0a00f4
com.dhass.myapp:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f13017f
com.dhass.myapp:attr/chipStrokeWidth = 0x7f0400c3
com.dhass.myapp:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.dhass.myapp:integer/material_motion_duration_long_2 = 0x7f0b0024
com.dhass.myapp:dimen/mtrl_extended_fab_min_width = 0x7f0701f6
com.dhass.myapp:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.dhass.myapp:drawable/notification_bg_low_pressed = 0x7f080128
com.dhass.myapp:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.dhass.myapp:color/wallet_secondary_text_holo_dark = 0x7f060281
com.dhass.myapp:id/fill_horizontal = 0x7f0a0111
com.dhass.myapp:color/wallet_hint_foreground_holo_light = 0x7f06027d
com.dhass.myapp:color/wallet_highlighted_text_holo_dark = 0x7f06027a
com.dhass.myapp:attr/passwordToggleDrawable = 0x7f040339
com.dhass.myapp:color/wallet_dim_foreground_disabled_holo_dark = 0x7f060278
com.dhass.myapp:drawable/exo_icon_play = 0x7f0800b6
com.dhass.myapp:attr/layout_constraintHeight_min = 0x7f040279
com.dhass.myapp:attr/collapsingToolbarLayoutLargeSize = 0x7f0400dc
com.dhass.myapp:color/m3_sys_color_light_primary_container = 0x7f06019e
com.dhass.myapp:color/wallet_bright_foreground_holo_light = 0x7f060277
com.dhass.myapp:color/wallet_bright_foreground_disabled_holo_light = 0x7f060275
com.dhass.myapp:id/production = 0x7f0a01c8
com.dhass.myapp:color/test_mtrl_calendar_day = 0x7f06026f
com.dhass.myapp:style/Widget.Material3.ChipGroup = 0x7f13039a
com.dhass.myapp:color/test_color = 0x7f06026e
com.dhass.myapp:attr/fabCradleMargin = 0x7f0401c3
com.dhass.myapp:color/switch_thumb_normal_material_light = 0x7f06026d
com.dhass.myapp:attr/listChoiceBackgroundIndicator = 0x7f0402a6
com.dhass.myapp:color/switch_thumb_material_light = 0x7f06026b
com.dhass.myapp:drawable/common_google_signin_btn_icon_disabled = 0x7f08007b
com.dhass.myapp:color/switch_thumb_material_dark = 0x7f06026a
com.dhass.myapp:color/switch_thumb_disabled_material_dark = 0x7f060268
com.dhass.myapp:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13010d
com.dhass.myapp:id/accessibility_custom_action_3 = 0x7f0a002d
com.dhass.myapp:attr/drawableTopCompat = 0x7f04018d
com.dhass.myapp:drawable/exo_icon_shuffle_on = 0x7f0800bd
com.dhass.myapp:attr/useViewLifecycle = 0x7f040486
com.dhass.myapp:color/splashscreen_background = 0x7f060267
com.dhass.myapp:integer/m3_sys_motion_duration_900 = 0x7f0b0021
com.dhass.myapp:attr/borderlessButtonStyle = 0x7f04006e
com.dhass.myapp:drawable/googleg_standard_color_18 = 0x7f0800e0
com.dhass.myapp:drawable/m3_tabs_transparent_background = 0x7f080101
com.dhass.myapp:color/ripple_material_dark = 0x7f060261
com.dhass.myapp:id/exo_minimal_controls = 0x7f0a00ed
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_regular = 0x7f110008
com.dhass.myapp:color/radiobutton_themeable_attribute_color = 0x7f060260
com.dhass.myapp:color/primary_text_default_material_dark = 0x7f06025c
com.dhass.myapp:id/material_clock_hand = 0x7f0a0169
com.dhass.myapp:attr/cropMinCropResultWidthPX = 0x7f04014e
com.dhass.myapp:drawable/node_modules_exporouter_assets_file = 0x7f080118
com.dhass.myapp:layout/test_navigation_bar_item_layout = 0x7f0d0096
com.dhass.myapp:attr/alertDialogStyle = 0x7f04002c
com.dhass.myapp:color/primary_material_dark = 0x7f06025a
com.dhass.myapp:attr/tabIconTint = 0x7f0403e8
com.dhass.myapp:dimen/material_emphasis_medium = 0x7f070177
com.dhass.myapp:string/material_timepicker_text_input_mode_description = 0x7f1200de
com.dhass.myapp:string/catalyst_sample_profiler_toggle = 0x7f12003f
com.dhass.myapp:id/accessibility_custom_action_28 = 0x7f0a002b
com.dhass.myapp:dimen/compat_button_padding_horizontal_material = 0x7f07005c
com.dhass.myapp:drawable/abc_seekbar_track_material = 0x7f080042
com.dhass.myapp:color/primary_dark_material_dark = 0x7f060258
com.dhass.myapp:color/notification_material_background_media_default_color = 0x7f060257
com.dhass.myapp:color/notification_icon_bg_color = 0x7f060256
com.dhass.myapp:color/mtrl_textinput_focused_box_stroke_color = 0x7f060253
com.dhass.myapp:id/search_edit_frame = 0x7f0a01fa
com.dhass.myapp:attr/commitIcon = 0x7f04010a
com.dhass.myapp:color/mtrl_textinput_disabled_color = 0x7f060251
com.dhass.myapp:color/m3_ref_palette_error99 = 0x7f0600fa
com.dhass.myapp:color/mtrl_textinput_default_box_stroke_color = 0x7f060250
com.dhass.myapp:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f07017c
com.dhass.myapp:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f130149
com.dhass.myapp:color/mtrl_text_btn_text_color_selector = 0x7f06024f
com.dhass.myapp:string/exo_download_completed = 0x7f120081
com.dhass.myapp:color/mtrl_tabs_ripple_color = 0x7f06024e
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_ionicons = 0x7f11000f
com.dhass.myapp:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0701ee
com.dhass.myapp:color/mtrl_tabs_icon_color_selector_colored = 0x7f06024c
com.dhass.myapp:attr/suggestionRowLayout = 0x7f0403de
com.dhass.myapp:color/mtrl_tabs_colored_ripple_color = 0x7f06024a
com.dhass.myapp:color/mtrl_scrim_color = 0x7f060249
com.dhass.myapp:color/mtrl_popupmenu_overlay_color = 0x7f060248
com.dhass.myapp:id/accessibility_custom_action_14 = 0x7f0a001c
com.dhass.myapp:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f060244
com.dhass.myapp:color/mtrl_navigation_item_text_color = 0x7f060243
com.dhass.myapp:attr/progressBarImage = 0x7f04035a
com.dhass.myapp:color/mtrl_navigation_bar_item_tint = 0x7f06023f
com.dhass.myapp:color/mtrl_indicator_text_color = 0x7f06023c
com.dhass.myapp:color/mtrl_filled_icon_tint = 0x7f06023a
com.dhass.myapp:drawable/rzp_loader_circle = 0x7f08013b
com.dhass.myapp:style/Widget.Material3.Tooltip = 0x7f1303f6
com.dhass.myapp:plurals/exo_controls_rewind_by_amount_description = 0x7f100001
com.dhass.myapp:attr/tooltipText = 0x7f040463
com.dhass.myapp:attr/textAppearanceHeadline4 = 0x7f040413
com.dhass.myapp:color/mtrl_fab_icon_text_color_selector = 0x7f060237
com.dhass.myapp:id/accessibility_custom_action_5 = 0x7f0a0031
com.dhass.myapp:color/mtrl_fab_bg_color_selector = 0x7f060236
com.dhass.myapp:attr/collapsingToolbarLayoutMediumSize = 0x7f0400de
com.dhass.myapp:attr/shapeAppearanceSmallComponent = 0x7f040398
com.dhass.myapp:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.dhass.myapp:color/mtrl_error = 0x7f060235
com.dhass.myapp:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1301c6
com.dhass.myapp:color/mtrl_choice_chip_ripple_color = 0x7f060233
com.dhass.myapp:attr/onNegativeCross = 0x7f040326
com.dhass.myapp:color/mtrl_choice_chip_background_color = 0x7f060232
com.dhass.myapp:color/mtrl_chip_surface_color = 0x7f060230
com.dhass.myapp:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f130095
com.dhass.myapp:color/mtrl_chip_close_icon_tint = 0x7f06022f
com.dhass.myapp:styleable/AppCompatTextHelper = 0x7f140010
com.dhass.myapp:drawable/assets_paymentimg_success = 0x7f08005e
com.dhass.myapp:attr/cornerShape = 0x7f04012a
com.dhass.myapp:color/mtrl_chip_background_color = 0x7f06022e
com.dhass.myapp:color/mtrl_card_view_ripple = 0x7f06022d
com.dhass.myapp:style/Platform.AppCompat = 0x7f130157
com.dhass.myapp:color/mtrl_card_view_foreground = 0x7f06022c
com.dhass.myapp:dimen/m3_card_elevated_hovered_z = 0x7f070101
com.dhass.myapp:color/mtrl_calendar_selected_range = 0x7f06022b
com.dhass.myapp:color/mtrl_calendar_item_stroke_color = 0x7f06022a
com.dhass.myapp:color/mtrl_btn_text_btn_ripple_color = 0x7f060226
com.dhass.myapp:dimen/compat_button_padding_vertical_material = 0x7f07005d
com.dhass.myapp:dimen/m3_navigation_drawer_layout_corner_size = 0x7f07011b
com.dhass.myapp:color/mtrl_btn_stroke_color_selector = 0x7f060224
com.dhass.myapp:style/ThemeOverlay.AppCompat = 0x7f1302ab
com.dhass.myapp:dimen/m3_timepicker_window_elevation = 0x7f070163
com.dhass.myapp:dimen/material_clock_number_text_size = 0x7f07016b
com.dhass.myapp:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.dhass.myapp:color/material_timepicker_modebutton_tint = 0x7f060221
com.dhass.myapp:color/material_timepicker_button_stroke = 0x7f06021e
com.dhass.myapp:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f130413
com.dhass.myapp:color/mtrl_textinput_hovered_box_stroke_color = 0x7f060254
com.dhass.myapp:anim/rns_ios_from_left_background_open = 0x7f01002f
com.dhass.myapp:color/material_timepicker_button_background = 0x7f06021d
com.dhass.myapp:id/startVertical = 0x7f0a0222
com.dhass.myapp:drawable/abc_btn_check_material = 0x7f080009
com.dhass.myapp:attr/chipSpacing = 0x7f0400bd
com.dhass.myapp:color/material_slider_thumb_color = 0x7f06021c
com.dhass.myapp:drawable/abc_textfield_default_mtrl_alpha = 0x7f080050
com.dhass.myapp:color/material_slider_inactive_track_color = 0x7f06021b
com.dhass.myapp:color/material_slider_active_track_color = 0x7f060218
com.dhass.myapp:dimen/abc_action_button_min_width_material = 0x7f07000e
com.dhass.myapp:color/material_on_surface_stroke = 0x7f060216
com.dhass.myapp:color/material_on_surface_emphasis_medium = 0x7f060215
com.dhass.myapp:color/material_on_surface_emphasis_high_type = 0x7f060214
com.dhass.myapp:id/disablePostScroll = 0x7f0a00bb
com.dhass.myapp:dimen/design_snackbar_max_width = 0x7f070088
com.dhass.myapp:color/material_on_surface_disabled = 0x7f060213
com.dhass.myapp:id/info = 0x7f0a014c
com.dhass.myapp:color/material_on_primary_disabled = 0x7f060210
com.dhass.myapp:color/material_on_background_disabled = 0x7f06020d
com.dhass.myapp:color/material_harmonized_color_on_error_container = 0x7f06020c
com.dhass.myapp:drawable/exo_controls_shuffle_off = 0x7f08009b
com.dhass.myapp:attr/layout_keyline = 0x7f04029b
com.dhass.myapp:color/material_harmonized_color_on_error = 0x7f06020b
com.dhass.myapp:attr/paddingTopNoTitle = 0x7f040333
com.dhass.myapp:color/material_grey_900 = 0x7f060208
com.dhass.myapp:color/material_grey_600 = 0x7f060205
com.dhass.myapp:id/buy_now = 0x7f0a007c
com.dhass.myapp:drawable/rzp_border_bottom = 0x7f080139
com.dhass.myapp:color/m3_sys_color_light_on_error_container = 0x7f060193
com.dhass.myapp:color/material_grey_50 = 0x7f060204
com.dhass.myapp:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0701d7
com.dhass.myapp:attr/placeholderImage = 0x7f040345
com.dhass.myapp:color/material_grey_100 = 0x7f060202
com.dhass.myapp:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1300a8
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600c5
com.dhass.myapp:dimen/mtrl_high_ripple_focused_alpha = 0x7f070202
com.dhass.myapp:attr/layout_constraintVertical_chainStyle = 0x7f04028b
com.dhass.myapp:color/m3_ref_palette_tertiary0 = 0x7f06012f
com.dhass.myapp:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070194
com.dhass.myapp:color/material_dynamic_tertiary95 = 0x7f060200
com.dhass.myapp:attr/contentScrim = 0x7f04011f
com.dhass.myapp:color/material_dynamic_tertiary90 = 0x7f0601ff
com.dhass.myapp:attr/motionProgress = 0x7f040312
com.dhass.myapp:color/material_dynamic_tertiary80 = 0x7f0601fe
com.dhass.myapp:string/pick_image_camera = 0x7f12010d
com.dhass.myapp:color/material_dynamic_tertiary70 = 0x7f0601fd
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f07013d
com.dhass.myapp:dimen/abc_text_size_display_2_material = 0x7f070044
com.dhass.myapp:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f1200c2
com.dhass.myapp:color/material_dynamic_tertiary50 = 0x7f0601fb
com.dhass.myapp:attr/chipMinHeight = 0x7f0400bb
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600b0
com.dhass.myapp:color/material_dynamic_tertiary30 = 0x7f0601f9
com.dhass.myapp:string/exo_controls_time_placeholder = 0x7f12007f
com.dhass.myapp:id/mtrl_picker_header_toggle = 0x7f0a019a
com.dhass.myapp:color/material_dynamic_tertiary10 = 0x7f0601f6
com.dhass.myapp:color/material_dynamic_tertiary0 = 0x7f0601f5
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_screenshortlong = 0x7f080067
com.dhass.myapp:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13044d
com.dhass.myapp:id/asConfigured = 0x7f0a0061
com.dhass.myapp:color/material_dynamic_secondary99 = 0x7f0601f4
com.dhass.myapp:color/material_dynamic_secondary95 = 0x7f0601f3
com.dhass.myapp:color/material_dynamic_secondary80 = 0x7f0601f1
com.dhass.myapp:color/material_dynamic_secondary70 = 0x7f0601f0
com.dhass.myapp:color/common_google_signin_btn_text_light = 0x7f06003d
com.dhass.myapp:color/material_dynamic_secondary60 = 0x7f0601ef
com.dhass.myapp:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f130182
com.dhass.myapp:attr/currentState = 0x7f04015e
com.dhass.myapp:color/material_dynamic_primary95 = 0x7f0601e6
com.dhass.myapp:color/material_dynamic_secondary40 = 0x7f0601ed
com.dhass.myapp:id/confirm_button = 0x7f0a009c
com.dhass.myapp:dimen/mtrl_calendar_day_today_stroke = 0x7f0701bf
com.dhass.myapp:color/material_dynamic_secondary20 = 0x7f0601eb
com.dhass.myapp:id/cancel_action = 0x7f0a007f
com.dhass.myapp:color/material_dynamic_secondary100 = 0x7f0601ea
com.dhass.myapp:style/Base.v27.Theme.SplashScreen.Light = 0x7f130116
com.dhass.myapp:color/material_dynamic_secondary0 = 0x7f0601e8
com.dhass.myapp:attr/itemPadding = 0x7f04023d
com.dhass.myapp:color/mtrl_btn_ripple_color = 0x7f060223
com.dhass.myapp:color/material_dynamic_primary90 = 0x7f0601e5
com.dhass.myapp:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f130267
com.dhass.myapp:string/mtrl_picker_a11y_prev_month = 0x7f1200e7
com.dhass.myapp:color/material_dynamic_primary80 = 0x7f0601e4
com.dhass.myapp:drawable/abc_ic_voice_search_api_material = 0x7f080027
com.dhass.myapp:string/abc_action_bar_up_description = 0x7f120001
com.dhass.myapp:color/material_dynamic_primary60 = 0x7f0601e2
com.dhass.myapp:drawable/m3_selection_control_ripple = 0x7f0800fd
com.dhass.myapp:style/Widget.Material3.Slider = 0x7f1303e0
com.dhass.myapp:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.dhass.myapp:color/material_dynamic_primary50 = 0x7f0601e1
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f130032
com.dhass.myapp:color/material_dynamic_primary40 = 0x7f0601e0
com.dhass.myapp:color/material_dynamic_primary30 = 0x7f0601df
com.dhass.myapp:drawable/ic_rotate_left_24 = 0x7f0800f6
com.dhass.myapp:color/material_dynamic_primary20 = 0x7f0601de
com.dhass.myapp:color/design_default_color_primary = 0x7f060059
com.dhass.myapp:dimen/mtrl_progress_circular_size = 0x7f070221
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1302fe
com.dhass.myapp:dimen/exo_styled_progress_bar_height = 0x7f0700b2
com.dhass.myapp:layout/abc_alert_dialog_button_bar_material = 0x7f0d0008
com.dhass.myapp:color/material_dynamic_primary10 = 0x7f0601dc
com.dhass.myapp:dimen/abc_floating_window_z = 0x7f07002f
com.dhass.myapp:color/material_dynamic_neutral_variant95 = 0x7f0601d9
com.dhass.myapp:id/material = 0x7f0a0166
com.dhass.myapp:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f07020b
com.dhass.myapp:attr/textAppearanceHeadlineLarge = 0x7f040416
com.dhass.myapp:color/material_dynamic_neutral_variant90 = 0x7f0601d8
com.dhass.myapp:color/material_dynamic_neutral_variant50 = 0x7f0601d4
com.dhass.myapp:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f13028f
com.dhass.myapp:color/material_dynamic_secondary50 = 0x7f0601ee
com.dhass.myapp:attr/indicatorDirectionLinear = 0x7f04022a
com.dhass.myapp:color/material_dynamic_neutral_variant40 = 0x7f0601d3
com.dhass.myapp:style/ShapeAppearance.Material3.Corner.Medium = 0x7f130186
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f13006b
com.dhass.myapp:drawable/ic_m3_chip_check = 0x7f0800ee
com.dhass.myapp:dimen/abc_dialog_min_width_minor = 0x7f070023
com.dhass.myapp:color/material_dynamic_neutral_variant30 = 0x7f0601d2
com.dhass.myapp:attr/editTextColor = 0x7f040196
com.dhass.myapp:id/exo_shutter = 0x7f0a0101
com.dhass.myapp:color/material_dynamic_neutral_variant100 = 0x7f0601d0
com.dhass.myapp:layout/mtrl_picker_text_input_date_range = 0x7f0d0073
com.dhass.myapp:color/material_dynamic_neutral_variant10 = 0x7f0601cf
com.dhass.myapp:layout/redbox_view = 0x7f0d0087
com.dhass.myapp:attr/listPreferredItemHeight = 0x7f0402ae
com.dhass.myapp:attr/waveDecay = 0x7f040491
com.dhass.myapp:color/material_dynamic_neutral_variant0 = 0x7f0601ce
com.dhass.myapp:string/material_hour_suffix = 0x7f1200ce
com.dhass.myapp:attr/autoCompleteTextViewStyle = 0x7f04003d
com.dhass.myapp:color/material_dynamic_neutral95 = 0x7f0601cc
com.dhass.myapp:color/m3_sys_color_light_on_tertiary = 0x7f06019a
com.dhass.myapp:id/chronometer = 0x7f0a0091
com.dhass.myapp:color/material_dynamic_neutral90 = 0x7f0601cb
com.dhass.myapp:styleable/KeyAttribute = 0x7f140042
com.dhass.myapp:color/material_dynamic_neutral80 = 0x7f0601ca
com.dhass.myapp:style/Widget.Design.TabLayout = 0x7f13036c
com.dhass.myapp:layout/exo_list_divider = 0x7f0d0035
com.dhass.myapp:drawable/exo_styled_controls_audiotrack = 0x7f0800c9
com.dhass.myapp:integer/mtrl_card_anim_delay_ms = 0x7f0b0030
com.dhass.myapp:dimen/design_textinput_caption_translate_y = 0x7f070092
com.dhass.myapp:id/italic = 0x7f0a0152
com.dhass.myapp:color/material_dynamic_neutral50 = 0x7f0601c7
com.dhass.myapp:color/material_dynamic_neutral40 = 0x7f0601c6
com.dhass.myapp:style/Base.Widget.AppCompat.Toolbar = 0x7f1300f0
com.dhass.myapp:layout/design_text_input_start_icon = 0x7f0d0033
com.dhass.myapp:anim/rns_ios_from_right_background_close = 0x7f010032
com.dhass.myapp:id/fillStart = 0x7f0a0110
com.dhass.myapp:color/material_dynamic_neutral20 = 0x7f0601c4
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1302f4
com.dhass.myapp:color/material_dynamic_neutral100 = 0x7f0601c3
com.dhass.myapp:color/material_dynamic_neutral10 = 0x7f0601c2
com.dhass.myapp:styleable/MaterialTextView = 0x7f14005e
com.dhass.myapp:color/material_divider_color = 0x7f0601c0
com.dhass.myapp:id/material_clock_face = 0x7f0a0168
com.dhass.myapp:id/exo_overflow_hide = 0x7f0a00f0
com.dhass.myapp:style/ResetEditText = 0x7f130165
com.dhass.myapp:attr/cropMaxZoom = 0x7f04014c
com.dhass.myapp:dimen/mtrl_card_checked_icon_size = 0x7f0701e2
com.dhass.myapp:color/material_deep_teal_500 = 0x7f0601bf
com.dhass.myapp:color/material_blue_grey_950 = 0x7f0601bc
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f13014d
com.dhass.myapp:dimen/mtrl_progress_circular_track_thickness_small = 0x7f070227
com.dhass.myapp:color/material_blue_grey_900 = 0x7f0601bb
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f130287
com.dhass.myapp:layout/abc_action_menu_item_layout = 0x7f0d0002
com.dhass.myapp:color/material_blue_grey_800 = 0x7f0601ba
com.dhass.myapp:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0700f8
com.dhass.myapp:drawable/exo_icon_fullscreen_exit = 0x7f0800b3
com.dhass.myapp:dimen/m3_navigation_rail_default_width = 0x7f070125
com.dhass.myapp:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f130106
com.dhass.myapp:color/m3_tonal_button_ripple_color_selector = 0x7f0601b9
com.dhass.myapp:attr/layout_constraintHorizontal_chainStyle = 0x7f04027c
com.dhass.myapp:drawable/ic_call_decline_low = 0x7f0800e7
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f130169
com.dhass.myapp:color/m3_timepicker_secondary_text_button_text_color = 0x7f0601b8
com.dhass.myapp:dimen/exo_styled_bottom_bar_time_padding = 0x7f0700af
com.dhass.myapp:color/material_on_background_emphasis_high_type = 0x7f06020e
com.dhass.myapp:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f130266
com.dhass.myapp:id/amu_text = 0x7f0a0058
com.dhass.myapp:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f0601b7
com.dhass.myapp:color/m3_timepicker_display_text_color = 0x7f0601b6
com.dhass.myapp:color/m3_timepicker_display_stroke_color = 0x7f0601b5
com.dhass.myapp:drawable/ic_resume = 0x7f0800f5
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_send = 0x7f080068
com.dhass.myapp:color/m3_timepicker_clock_text_color = 0x7f0601b2
com.dhass.myapp:attr/marginRightSystemWindowInsets = 0x7f0402be
com.dhass.myapp:attr/font = 0x7f0401ee
com.dhass.myapp:dimen/m3_btn_stroke_size = 0x7f0700f7
com.dhass.myapp:color/m3_timepicker_button_ripple_color = 0x7f0601b0
com.dhass.myapp:styleable/ActionBar = 0x7f140000
com.dhass.myapp:attr/spanCount = 0x7f0403b7
com.dhass.myapp:drawable/exo_notification_next = 0x7f0800c1
com.dhass.myapp:style/Platform.V21.AppCompat.Light = 0x7f130161
com.dhass.myapp:string/mtrl_picker_date_header_title = 0x7f1200ec
com.dhass.myapp:color/m3_timepicker_button_background_color = 0x7f0601af
com.dhass.myapp:color/m3_textfield_input_text_color = 0x7f0601ac
com.dhass.myapp:style/Widget.Material3.Snackbar = 0x7f1303e1
com.dhass.myapp:color/m3_textfield_indicator_text_color = 0x7f0601ab
com.dhass.myapp:color/mtrl_btn_transparent_bg_color = 0x7f060229
com.dhass.myapp:color/m3_textfield_filled_background_color = 0x7f0601aa
com.dhass.myapp:color/m3_text_button_ripple_color_selector = 0x7f0601a9
com.dhass.myapp:attr/shortcutMatchRequired = 0x7f040399
com.dhass.myapp:id/exo_playback_speed = 0x7f0a00f6
com.dhass.myapp:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1303e9
com.dhass.myapp:color/m3_tabs_icon_color = 0x7f0601a5
com.dhass.myapp:style/Test.Widget.MaterialComponents.MaterialCalendar = 0x7f1301b1
com.dhass.myapp:style/Base.V23.Theme.AppCompat = 0x7f1300a5
com.dhass.myapp:color/m3_sys_color_light_tertiary_container = 0x7f0601a4
com.dhass.myapp:color/m3_sys_color_light_tertiary = 0x7f0601a3
com.dhass.myapp:id/startToEnd = 0x7f0a0221
com.dhass.myapp:drawable/abc_text_select_handle_left_mtrl = 0x7f08004c
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1301ff
com.dhass.myapp:string/common_google_play_services_install_text = 0x7f12004e
com.dhass.myapp:dimen/mtrl_badge_with_text_radius = 0x7f070198
com.dhass.myapp:color/m3_sys_color_light_surface_variant = 0x7f0601a2
com.dhass.myapp:color/m3_sys_color_light_secondary_container = 0x7f0601a0
com.dhass.myapp:color/m3_sys_color_light_primary = 0x7f06019d
com.dhass.myapp:attr/listPreferredItemHeightLarge = 0x7f0402af
com.dhass.myapp:color/m3_sys_color_light_on_surface = 0x7f060198
com.dhass.myapp:attr/textInputFilledStyle = 0x7f040430
com.dhass.myapp:color/m3_sys_color_light_on_secondary_container = 0x7f060197
com.dhass.myapp:style/Theme.Design.Light.BottomSheetDialog = 0x7f130256
com.dhass.myapp:dimen/m3_divider_heavy_thickness = 0x7f07010d
com.dhass.myapp:id/search_button = 0x7f0a01f8
com.dhass.myapp:color/design_dark_default_color_on_error = 0x7f060048
com.dhass.myapp:color/m3_sys_color_light_on_secondary = 0x7f060196
com.dhass.myapp:color/m3_sys_color_light_on_primary_container = 0x7f060195
com.dhass.myapp:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07019d
com.dhass.myapp:id/filled = 0x7f0a0113
com.dhass.myapp:color/m3_sys_color_light_on_primary = 0x7f060194
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600ae
com.dhass.myapp:color/m3_sys_color_light_on_error = 0x7f060192
com.dhass.myapp:attr/cameraMinZoomPreference = 0x7f040095
com.dhass.myapp:color/m3_sys_color_light_on_background = 0x7f060191
com.dhass.myapp:attr/switchStyle = 0x7f0403e3
com.dhass.myapp:color/m3_sys_color_light_error_container = 0x7f06018d
com.dhass.myapp:color/m3_sys_color_light_background = 0x7f06018b
com.dhass.myapp:string/mtrl_picker_save = 0x7f1200fa
com.dhass.myapp:color/m3_sys_color_dynamic_light_tertiary = 0x7f060189
com.dhass.myapp:style/Base.v27.Theme.SplashScreen = 0x7f130115
com.dhass.myapp:color/m3_sys_color_dynamic_light_surface = 0x7f060187
com.dhass.myapp:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1300a2
com.dhass.myapp:color/m3_sys_color_dynamic_light_primary = 0x7f060183
com.dhass.myapp:color/m3_sys_color_dynamic_light_outline = 0x7f060182
com.dhass.myapp:attr/layout_constraintRight_toLeftOf = 0x7f040282
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f060181
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f060180
com.dhass.myapp:style/Base.Theme.AppCompat = 0x7f13004a
com.dhass.myapp:id/tag_accessibility_heading = 0x7f0a022f
com.dhass.myapp:dimen/design_fab_size_mini = 0x7f070076
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f06017f
com.dhass.myapp:color/wallet_holo_blue_light = 0x7f06027e
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f06017d
com.dhass.myapp:dimen/mtrl_calendar_day_vertical_padding = 0x7f0701c0
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_secondary = 0x7f06017c
com.dhass.myapp:attr/chipIconSize = 0x7f0400b8
com.dhass.myapp:color/m3_sys_color_light_secondary = 0x7f06019f
com.dhass.myapp:dimen/m3_sys_elevation_level0 = 0x7f070134
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f06017b
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_primary = 0x7f06017a
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_background = 0x7f060179
com.dhass.myapp:dimen/m3_fab_corner_size = 0x7f070115
com.dhass.myapp:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f060178
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f13003e
com.dhass.myapp:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f060177
com.dhass.myapp:color/m3_sys_color_dynamic_light_background = 0x7f060175
com.dhass.myapp:dimen/mtrl_progress_circular_size_extra_small = 0x7f070222
com.dhass.myapp:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f060172
com.dhass.myapp:attr/materialCalendarHeaderSelection = 0x7f0402d7
com.dhass.myapp:id/compose_view_saveable_id_tag = 0x7f0a009a
com.dhass.myapp:style/Widget.Material3.MaterialCalendar = 0x7f1303b8
com.dhass.myapp:style/Widget.Design.CollapsingToolbar = 0x7f130367
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f070157
com.dhass.myapp:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f060170
com.dhass.myapp:id/line3 = 0x7f0a015e
com.dhass.myapp:drawable/abc_seekbar_tick_mark_material = 0x7f080041
com.dhass.myapp:dimen/mtrl_progress_circular_size_medium = 0x7f070223
com.dhass.myapp:string/abc_action_menu_overflow_description = 0x7f120002
com.dhass.myapp:color/m3_sys_color_dynamic_dark_secondary = 0x7f06016f
com.dhass.myapp:color/m3_sys_color_dynamic_dark_primary = 0x7f06016d
com.dhass.myapp:color/m3_sys_color_dynamic_dark_outline = 0x7f06016c
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f060169
com.dhass.myapp:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f080108
com.dhass.myapp:id/search_bar = 0x7f0a01f7
com.dhass.myapp:dimen/design_navigation_item_horizontal_padding = 0x7f07007d
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f060166
com.dhass.myapp:color/m3_card_foreground_color = 0x7f060086
com.dhass.myapp:dimen/tooltip_horizontal_padding = 0x7f070272
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1301e8
com.dhass.myapp:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f130049
com.dhass.myapp:layout/abc_search_view = 0x7f0d0019
com.dhass.myapp:attr/icon = 0x7f040219
com.dhass.myapp:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f060176
com.dhass.myapp:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1302d0
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1301e2
com.dhass.myapp:attr/prefixText = 0x7f040353
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f060165
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_background = 0x7f060163
com.dhass.myapp:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f060161
com.dhass.myapp:id/accessibility_custom_action_4 = 0x7f0a0030
com.dhass.myapp:color/mtrl_filled_stroke_color = 0x7f06023b
com.dhass.myapp:color/m3_sys_color_dynamic_dark_background = 0x7f06015f
com.dhass.myapp:color/m3_sys_color_dark_tertiary_container = 0x7f06015e
com.dhass.myapp:color/m3_sys_color_dark_tertiary = 0x7f06015d
com.dhass.myapp:style/Widget.Material3.NavigationRailView = 0x7f1303d9
com.dhass.myapp:attr/liftOnScroll = 0x7f0402a0
com.dhass.myapp:id/exo_icon = 0x7f0a00eb
com.dhass.myapp:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.dhass.myapp:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f13045a
com.dhass.myapp:color/m3_sys_color_dark_surface = 0x7f06015b
com.dhass.myapp:attr/autoTransition = 0x7f040043
com.dhass.myapp:color/m3_sys_color_dark_secondary_container = 0x7f06015a
com.dhass.myapp:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1303f7
com.dhass.myapp:style/Base.Widget.AppCompat.SearchView = 0x7f1300e8
com.dhass.myapp:color/m3_sys_color_dark_primary = 0x7f060157
com.dhass.myapp:color/m3_sys_color_dark_on_tertiary_container = 0x7f060155
com.dhass.myapp:color/m3_sys_color_dark_on_tertiary = 0x7f060154
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1301d8
com.dhass.myapp:attr/constraint_referenced_ids = 0x7f04010e
com.dhass.myapp:dimen/notification_top_pad_large_text = 0x7f07025c
com.dhass.myapp:color/m3_sys_color_dark_on_secondary_container = 0x7f060151
com.dhass.myapp:color/material_on_primary_emphasis_medium = 0x7f060212
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f070155
com.dhass.myapp:color/m3_sys_color_dark_on_secondary = 0x7f060150
com.dhass.myapp:color/m3_sys_color_dark_on_primary_container = 0x7f06014f
com.dhass.myapp:color/m3_sys_color_dark_on_primary = 0x7f06014e
com.dhass.myapp:id/container = 0x7f0a009e
com.dhass.myapp:style/Base.Widget.AppCompat.Button = 0x7f1300c3
com.dhass.myapp:color/m3_sys_color_dark_on_error_container = 0x7f06014d
com.dhass.myapp:color/m3_sys_color_dark_on_error = 0x7f06014c
com.dhass.myapp:id/rn_redbox_dismiss_button = 0x7f0a01dc
com.dhass.myapp:attr/behavior_overlapTop = 0x7f040069
com.dhass.myapp:drawable/exo_icon_shuffle_off = 0x7f0800bc
com.dhass.myapp:color/m3_sys_color_dark_on_background = 0x7f06014b
com.dhass.myapp:attr/maxVelocity = 0x7f0402f3
com.dhass.myapp:drawable/abc_btn_radio_material = 0x7f08000f
com.dhass.myapp:layout/abc_alert_dialog_material = 0x7f0d0009
com.dhass.myapp:dimen/m3_btn_text_btn_padding_right = 0x7f0700fb
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600b6
com.dhass.myapp:attr/textAppearanceSmallPopupMenu = 0x7f040425
com.dhass.myapp:color/m3_sys_color_dark_inverse_surface = 0x7f06014a
com.dhass.myapp:layout/mtrl_calendar_day = 0x7f0d005d
com.dhass.myapp:color/m3_ref_palette_secondary20 = 0x7f060125
com.dhass.myapp:color/m3_sys_color_dark_inverse_primary = 0x7f060149
com.dhass.myapp:attr/extendMotionSpec = 0x7f0401ba
com.dhass.myapp:color/m3_sys_color_dark_inverse_on_surface = 0x7f060148
com.dhass.myapp:color/m3_sys_color_dark_error_container = 0x7f060147
com.dhass.myapp:color/mtrl_fab_ripple_color = 0x7f060238
com.dhass.myapp:color/m3_switch_track_tint = 0x7f060144
com.dhass.myapp:style/ExoMediaButton = 0x7f130121
com.dhass.myapp:color/m3_slider_halo_color = 0x7f060140
com.dhass.myapp:color/m3_slider_active_track_color = 0x7f06013f
com.dhass.myapp:dimen/mtrl_card_dragged_z = 0x7f0701e4
com.dhass.myapp:color/m3_selection_control_ripple_color_selector = 0x7f06013e
com.dhass.myapp:attr/path_percent = 0x7f04033e
com.dhass.myapp:color/m3_selection_control_button_tint = 0x7f06013d
com.dhass.myapp:attr/hoveredFocusedTranslationZ = 0x7f040218
com.dhass.myapp:color/m3_ref_palette_tertiary95 = 0x7f06013a
com.dhass.myapp:attr/passwordToggleContentDescription = 0x7f040338
com.dhass.myapp:attr/cropFlipHorizontally = 0x7f040144
com.dhass.myapp:color/m3_ref_palette_tertiary90 = 0x7f060139
com.dhass.myapp:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700cb
com.dhass.myapp:color/m3_ref_palette_tertiary70 = 0x7f060137
com.dhass.myapp:styleable/KeyPosition = 0x7f140047
com.dhass.myapp:style/Base.V22.Theme.AppCompat.Light = 0x7f1300a4
com.dhass.myapp:color/m3_ref_palette_tertiary60 = 0x7f060136
com.dhass.myapp:color/m3_ref_palette_tertiary40 = 0x7f060134
com.dhass.myapp:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080051
com.dhass.myapp:color/m3_ref_palette_tertiary30 = 0x7f060133
com.dhass.myapp:attr/cornerSizeTopRight = 0x7f04012f
com.dhass.myapp:color/m3_ref_palette_tertiary20 = 0x7f060132
com.dhass.myapp:string/ic_rotate_right_24 = 0x7f1200b2
com.dhass.myapp:color/m3_ref_palette_tertiary10 = 0x7f060130
com.dhass.myapp:color/m3_ref_palette_secondary95 = 0x7f06012d
com.dhass.myapp:color/exo_white_opacity_70 = 0x7f060076
com.dhass.myapp:color/m3_ref_palette_secondary80 = 0x7f06012b
com.dhass.myapp:attr/tabIndicatorGravity = 0x7f0403ef
com.dhass.myapp:attr/elevationOverlayAccentColor = 0x7f040199
com.dhass.myapp:color/m3_ref_palette_secondary70 = 0x7f06012a
com.dhass.myapp:style/Widget.MaterialComponents.ChipGroup = 0x7f13041e
com.dhass.myapp:color/m3_ref_palette_secondary60 = 0x7f060129
com.dhass.myapp:attr/itemShapeInsetBottom = 0x7f040244
com.dhass.myapp:attr/mock_showDiagonals = 0x7f040301
com.dhass.myapp:attr/materialButtonToggleGroupStyle = 0x7f0402cf
com.dhass.myapp:attr/textAppearanceHeadline1 = 0x7f040410
com.dhass.myapp:color/m3_ref_palette_secondary50 = 0x7f060128
com.dhass.myapp:attr/cropSnapRadius = 0x7f040158
com.dhass.myapp:attr/scrimVisibleHeightTrigger = 0x7f040386
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f130202
com.dhass.myapp:id/on = 0x7f0a01b0
com.dhass.myapp:color/m3_ref_palette_secondary40 = 0x7f060127
com.dhass.myapp:color/m3_dynamic_dark_hint_foreground = 0x7f060098
com.dhass.myapp:attr/warmth = 0x7f040490
com.dhass.myapp:color/m3_ref_palette_secondary100 = 0x7f060124
com.dhass.myapp:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.dhass.myapp:id/action_divider = 0x7f0a0047
com.dhass.myapp:style/Widget.AppCompat.RatingBar.Indicator = 0x7f13034e
com.dhass.myapp:attr/tabIconTintMode = 0x7f0403e9
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0600e1
com.dhass.myapp:attr/toolbarTextColorStyle = 0x7f04045f
com.dhass.myapp:color/abc_search_url_text_pressed = 0x7f06000f
com.dhass.myapp:color/m3_ref_palette_secondary0 = 0x7f060122
com.dhass.myapp:color/m3_ref_palette_primary90 = 0x7f06011f
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600bf
com.dhass.myapp:layout/notification_template_lines_media = 0x7f0d007f
com.dhass.myapp:attr/played_color = 0x7f04034c
com.dhass.myapp:attr/insetForeground = 0x7f04022e
com.dhass.myapp:color/m3_ref_palette_primary100 = 0x7f060117
com.dhass.myapp:attr/percentHeight = 0x7f04033f
com.dhass.myapp:attr/textAppearanceHeadline3 = 0x7f040412
com.dhass.myapp:attr/iconSize = 0x7f04021d
com.dhass.myapp:color/m3_ref_palette_primary0 = 0x7f060115
com.dhass.myapp:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f130402
com.dhass.myapp:attr/shapeAppearance = 0x7f040394
com.dhass.myapp:color/m3_ref_palette_neutral_variant70 = 0x7f060110
com.dhass.myapp:attr/dividerVertical = 0x7f040180
com.dhass.myapp:dimen/m3_extended_fab_bottom_padding = 0x7f07010e
com.dhass.myapp:color/m3_ref_palette_neutral_variant60 = 0x7f06010f
com.dhass.myapp:attr/tabPaddingTop = 0x7f0403f9
com.dhass.myapp:color/m3_ref_palette_neutral_variant40 = 0x7f06010d
com.dhass.myapp:color/m3_ref_palette_neutral_variant30 = 0x7f06010c
com.dhass.myapp:anim/rns_slide_out_to_bottom = 0x7f01003d
com.dhass.myapp:color/m3_ref_palette_neutral_variant20 = 0x7f06010b
com.dhass.myapp:color/m3_ref_palette_neutral_variant0 = 0x7f060108
com.dhass.myapp:attr/checkMarkCompat = 0x7f0400a4
com.dhass.myapp:style/Widget.Design.NavigationView = 0x7f130369
com.dhass.myapp:id/image = 0x7f0a014b
com.dhass.myapp:attr/passwordToggleEnabled = 0x7f04033a
com.dhass.myapp:color/exo_black_opacity_70 = 0x7f060070
com.dhass.myapp:string/exo_controls_cc_disabled_description = 0x7f120067
com.dhass.myapp:color/m3_ref_palette_neutral99 = 0x7f060107
com.dhass.myapp:attr/mock_labelColor = 0x7f040300
com.dhass.myapp:color/m3_ref_palette_neutral60 = 0x7f060102
com.dhass.myapp:color/m3_ref_palette_neutral_variant90 = 0x7f060112
com.dhass.myapp:color/m3_ref_palette_neutral50 = 0x7f060101
com.dhass.myapp:id/cancel_button = 0x7f0a0080
com.dhass.myapp:id/mtrl_calendar_days_of_week = 0x7f0a018b
com.dhass.myapp:attr/elevation = 0x7f040198
com.dhass.myapp:color/m3_ref_palette_neutral40 = 0x7f060100
com.dhass.myapp:style/Widget.AppCompat.Spinner.Underlined = 0x7f130357
com.dhass.myapp:drawable/exo_icon_fastforward = 0x7f0800b1
com.dhass.myapp:attr/textInputFilledExposedDropdownMenuStyle = 0x7f04042f
com.dhass.myapp:color/m3_ref_palette_neutral20 = 0x7f0600fe
com.dhass.myapp:color/m3_ref_palette_neutral100 = 0x7f0600fd
com.dhass.myapp:attr/contentInsetLeft = 0x7f040114
com.dhass.myapp:color/abc_hint_foreground_material_dark = 0x7f060007
com.dhass.myapp:attr/textAppearanceBodyMedium = 0x7f040409
com.dhass.myapp:color/m3_ref_palette_neutral10 = 0x7f0600fc
com.dhass.myapp:attr/show_rewind_button = 0x7f0403a6
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f13045d
com.dhass.myapp:color/mtrl_navigation_item_icon_tint = 0x7f060242
com.dhass.myapp:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f130012
com.dhass.myapp:color/m3_ref_palette_error90 = 0x7f0600f8
com.dhass.myapp:attr/implementationMode = 0x7f040225
com.dhass.myapp:id/adjust_width = 0x7f0a0053
com.dhass.myapp:attr/listChoiceIndicatorSingleAnimated = 0x7f0402a8
com.dhass.myapp:color/m3_ref_palette_error80 = 0x7f0600f7
com.dhass.myapp:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f02001e
com.dhass.myapp:attr/itemHorizontalTranslationEnabled = 0x7f040237
com.dhass.myapp:style/Base.Widget.Material3.TabLayout = 0x7f130102
com.dhass.myapp:attr/contrast = 0x7f040120
com.dhass.myapp:color/catalyst_logbox_background = 0x7f060033
com.dhass.myapp:color/m3_ref_palette_error40 = 0x7f0600f3
com.dhass.myapp:attr/cardBackgroundColor = 0x7f04009a
com.dhass.myapp:attr/itemShapeInsetTop = 0x7f040247
com.dhass.myapp:color/m3_ref_palette_error30 = 0x7f0600f2
com.dhass.myapp:dimen/exo_error_message_margin_bottom = 0x7f070096
com.dhass.myapp:attr/itemShapeInsetEnd = 0x7f040245
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1303c1
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0600eb
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0600ea
com.dhass.myapp:attr/buttonGravity = 0x7f040087
com.dhass.myapp:color/m3_ref_palette_neutral0 = 0x7f0600fb
com.dhass.myapp:drawable/notification_action_background = 0x7f080124
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0600e4
com.dhass.myapp:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f130234
com.dhass.myapp:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1301c8
com.dhass.myapp:id/forever = 0x7f0a0122
com.dhass.myapp:color/m3_ref_palette_dynamic_primary90 = 0x7f0600d1
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0600e3
com.dhass.myapp:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f130399
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary80 = 0x7f0600dd
com.dhass.myapp:color/m3_ref_palette_primary10 = 0x7f060116
com.dhass.myapp:style/Widget.AppCompat.ActionBar.TabText = 0x7f130316
com.dhass.myapp:string/state_on = 0x7f120121
com.dhass.myapp:layout/select_dialog_item_material = 0x7f0d008b
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600dc
com.dhass.myapp:dimen/browser_actions_context_menu_max_width = 0x7f070054
com.dhass.myapp:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1300bb
com.dhass.myapp:attr/scrubber_enabled_size = 0x7f04038b
com.dhass.myapp:styleable/ViewPager2 = 0x7f14009b
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600d7
com.dhass.myapp:styleable/MaterialButton = 0x7f140054
com.dhass.myapp:color/m3_ref_palette_neutral_variant80 = 0x7f060111
com.dhass.myapp:id/exo_audio_track = 0x7f0a00da
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600d5
com.dhass.myapp:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080017
com.dhass.myapp:attr/navigationViewStyle = 0x7f04031e
com.dhass.myapp:attr/floatingActionButtonSecondaryStyle = 0x7f0401d7
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600d4
com.dhass.myapp:color/m3_ref_palette_dynamic_primary95 = 0x7f0600d2
com.dhass.myapp:string/exo_controls_shuffle_on_description = 0x7f12007d
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070153
com.dhass.myapp:color/m3_ref_palette_dynamic_primary70 = 0x7f0600cf
com.dhass.myapp:color/m3_ref_palette_dynamic_primary50 = 0x7f0600cd
com.dhass.myapp:dimen/m3_btn_icon_only_icon_padding = 0x7f0700ef
com.dhass.myapp:color/m3_ref_palette_dynamic_primary30 = 0x7f0600cb
com.dhass.myapp:drawable/exo_controls_rewind = 0x7f08009a
com.dhass.myapp:anim/catalyst_push_up_out = 0x7f01001b
com.dhass.myapp:attr/textAppearanceDisplaySmall = 0x7f04040f
com.dhass.myapp:color/m3_ref_palette_dynamic_primary0 = 0x7f0600c7
com.dhass.myapp:style/Widget.Material3.Badge = 0x7f130376
com.dhass.myapp:attr/maxCharacterCount = 0x7f0402ef
com.dhass.myapp:drawable/compat_splash_screen = 0x7f080089
com.dhass.myapp:attr/actionMenuTextAppearance = 0x7f04000e
com.dhass.myapp:attr/materialClockStyle = 0x7f0402e4
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600c3
com.dhass.myapp:string/material_clock_toggle_content_description = 0x7f1200cc
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0600e9
com.dhass.myapp:id/glide_custom_view_target_tag = 0x7f0a0129
com.dhass.myapp:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1302c7
com.dhass.myapp:attr/layout_constrainedHeight = 0x7f040267
com.dhass.myapp:id/accessibility_custom_action_27 = 0x7f0a002a
com.dhass.myapp:style/WalletFragmentDefaultDetailsTextAppearance = 0x7f130311
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600c0
com.dhass.myapp:attr/cornerRadius = 0x7f040129
com.dhass.myapp:color/m3_appbar_overlay_color = 0x7f06007c
com.dhass.myapp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130013
com.dhass.myapp:style/AlertDialog.AppCompat.Light = 0x7f130001
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600be
com.dhass.myapp:dimen/m3_extended_fab_start_padding = 0x7f070112
com.dhass.myapp:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f070199
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600bb
com.dhass.myapp:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f070124
com.dhass.myapp:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f13039d
com.dhass.myapp:animator/m3_btn_state_list_anim = 0x7f02000e
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600b9
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600b8
com.dhass.myapp:style/SpinnerTimePickerDialogBase = 0x7f1301ad
com.dhass.myapp:attr/searchHintIcon = 0x7f04038c
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130090
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600b7
com.dhass.myapp:integer/m3_sys_motion_duration_300 = 0x7f0b0017
com.dhass.myapp:id/crop_image_menu_crop = 0x7f0a00a6
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600b5
com.dhass.myapp:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1300c6
com.dhass.myapp:color/m3_ref_palette_error0 = 0x7f0600ee
com.dhass.myapp:dimen/mtrl_alert_dialog_background_inset_end = 0x7f07018d
com.dhass.myapp:color/m3_sys_color_dark_secondary = 0x7f060159
com.dhass.myapp:attr/cropInitialCropWindowPaddingRatio = 0x7f040149
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600b1
com.dhass.myapp:style/Widget.Material3.Chip.Assist = 0x7f130390
com.dhass.myapp:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f1300f8
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600ad
com.dhass.myapp:drawable/assets_profiledefault = 0x7f08005f
com.dhass.myapp:color/m3_radiobutton_ripple_tint = 0x7f0600ab
com.dhass.myapp:style/Widget.AppCompat.ImageButton = 0x7f13032c
com.dhass.myapp:color/mtrl_navigation_item_background_color = 0x7f060241
com.dhass.myapp:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600a3
com.dhass.myapp:attr/floatingActionButtonLargeStyle = 0x7f0401d3
com.dhass.myapp:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130459
com.dhass.myapp:attr/contentInsetEndWithActions = 0x7f040113
com.dhass.myapp:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600a2
com.dhass.myapp:attr/actionModePopupWindowStyle = 0x7f040018
com.dhass.myapp:color/m3_highlighted_text = 0x7f0600a0
com.dhass.myapp:id/snackbar_text = 0x7f0a020f
com.dhass.myapp:color/m3_elevated_chip_background_color = 0x7f06009f
com.dhass.myapp:styleable/ConstraintSet = 0x7f14002a
com.dhass.myapp:dimen/tooltip_y_offset_touch = 0x7f070278
com.dhass.myapp:color/m3_dynamic_hint_foreground = 0x7f06009d
com.dhass.myapp:color/m3_tabs_ripple_color = 0x7f0601a6
com.dhass.myapp:attr/panelMenuListTheme = 0x7f040336
com.dhass.myapp:id/decelerateAndComplete = 0x7f0a00ae
com.dhass.myapp:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f13033e
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1302f0
com.dhass.myapp:dimen/mtrl_card_corner_radius = 0x7f0701e3
com.dhass.myapp:color/m3_dynamic_default_color_primary_text = 0x7f06009a
com.dhass.myapp:attr/maskedWalletDetailsBackground = 0x7f0402c0
com.dhass.myapp:attr/actionOverflowMenuStyle = 0x7f040020
com.dhass.myapp:color/m3_dynamic_dark_highlighted_text = 0x7f060097
com.dhass.myapp:dimen/material_textinput_min_width = 0x7f070188
com.dhass.myapp:color/mtrl_outlined_stroke_color = 0x7f060247
com.dhass.myapp:attr/shapeAppearanceLargeComponent = 0x7f040395
com.dhass.myapp:dimen/tooltip_corner_radius = 0x7f070271
com.dhass.myapp:attr/showAnimationBehavior = 0x7f04039a
com.dhass.myapp:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1302ce
com.dhass.myapp:color/m3_dynamic_dark_default_color_secondary_text = 0x7f060096
com.dhass.myapp:attr/clockIcon = 0x7f0400cc
com.dhass.myapp:color/m3_dynamic_dark_default_color_primary_text = 0x7f060095
com.dhass.myapp:id/action_bar_container = 0x7f0a0040
com.dhass.myapp:attr/layout_constraintWidth_default = 0x7f04028d
com.dhass.myapp:style/MaterialAlertDialog.Material3 = 0x7f130143
com.dhass.myapp:color/m3_default_color_secondary_text = 0x7f060094
com.dhass.myapp:color/ripple_material_light = 0x7f060262
com.dhass.myapp:id/icon_group = 0x7f0a0146
com.dhass.myapp:attr/backgroundInsetEnd = 0x7f04004e
com.dhass.myapp:color/m3_dark_highlighted_text = 0x7f060090
com.dhass.myapp:id/rewind_button = 0x7f0a01d5
com.dhass.myapp:attr/snackbarStyle = 0x7f0403b5
com.dhass.myapp:color/m3_dark_default_color_secondary_text = 0x7f06008f
com.dhass.myapp:attr/chipMinTouchTargetSize = 0x7f0400bc
com.dhass.myapp:attr/imageAspectRatio = 0x7f040222
com.dhass.myapp:style/TextAppearance.Design.Suffix = 0x7f1301fd
com.dhass.myapp:color/m3_dark_default_color_primary_text = 0x7f06008e
com.dhass.myapp:color/m3_chip_text_color = 0x7f06008d
com.dhass.myapp:attr/content = 0x7f040110
com.dhass.myapp:color/design_fab_stroke_end_inner_color = 0x7f060063
com.dhass.myapp:attr/waveShape = 0x7f040494
com.dhass.myapp:color/m3_card_stroke_color = 0x7f060088
com.dhass.myapp:color/m3_calendar_item_disabled_text = 0x7f060084
com.dhass.myapp:color/m3_textfield_stroke_color = 0x7f0601ae
com.dhass.myapp:dimen/design_tab_text_size = 0x7f070090
com.dhass.myapp:color/m3_ref_palette_error60 = 0x7f0600f5
com.dhass.myapp:attr/keyboardIcon = 0x7f040252
com.dhass.myapp:color/m3_button_ripple_color_selector = 0x7f060083
com.dhass.myapp:attr/layout_goneMarginLeft = 0x7f040296
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f06016b
com.dhass.myapp:color/m3_ref_palette_neutral90 = 0x7f060105
com.dhass.myapp:attr/tabIndicatorHeight = 0x7f0403f0
com.dhass.myapp:color/m3_button_foreground_color_selector = 0x7f060080
com.dhass.myapp:color/m3_button_background_color_selector = 0x7f06007f
com.dhass.myapp:id/tag_unhandled_key_event_manager = 0x7f0a0237
com.dhass.myapp:dimen/m3_navigation_item_shape_inset_bottom = 0x7f07011e
com.dhass.myapp:attr/colorOnBackground = 0x7f0400eb
com.dhass.myapp:color/highlighted_text_material_dark = 0x7f060079
com.dhass.myapp:id/exo_fullscreen = 0x7f0a00ea
com.dhass.myapp:color/foreground_material_dark = 0x7f060077
com.dhass.myapp:color/exo_bottom_bar_background = 0x7f060071
com.dhass.myapp:style/Widget.Material3.Toolbar.OnSurface = 0x7f1303f4
com.dhass.myapp:color/error_color_material_light = 0x7f06006e
com.dhass.myapp:style/TextAppearance.Material3.ActionBar.Title = 0x7f13020f
com.dhass.myapp:color/m3_sys_color_dynamic_light_secondary_container = 0x7f060186
com.dhass.myapp:anim/abc_fade_out = 0x7f010001
com.dhass.myapp:id/tv_sub_item = 0x7f0a0264
com.dhass.myapp:attr/actionBarSplitStyle = 0x7f040004
com.dhass.myapp:attr/actionViewClass = 0x7f040023
com.dhass.myapp:color/dim_foreground_disabled_material_dark = 0x7f060069
com.dhass.myapp:color/design_icon_tint = 0x7f060067
com.dhass.myapp:styleable/NavigationBarActiveIndicator = 0x7f14006a
com.dhass.myapp:id/up = 0x7f0a0269
com.dhass.myapp:id/search_src_text = 0x7f0a01fe
com.dhass.myapp:dimen/mtrl_btn_z = 0x7f0701b6
com.dhass.myapp:drawable/exo_ic_skip_next = 0x7f0800ab
com.dhass.myapp:layout/ime_secondary_split_test_activity = 0x7f0d0043
com.dhass.myapp:color/design_fab_stroke_top_inner_color = 0x7f060065
com.dhass.myapp:color/design_default_color_secondary = 0x7f06005c
com.dhass.myapp:style/Widget.Autofill.InlineSuggestionStartIconStyle = 0x7f13035f
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_evilicons = 0x7f110004
com.dhass.myapp:drawable/common_google_signin_btn_text_disabled = 0x7f080084
com.dhass.myapp:id/exo_position = 0x7f0a00f7
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f130037
com.dhass.myapp:color/design_default_color_primary_dark = 0x7f06005a
com.dhass.myapp:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f13007f
com.dhass.myapp:attr/iconGravity = 0x7f04021b
com.dhass.myapp:dimen/abc_progress_bar_height_material = 0x7f070035
com.dhass.myapp:color/design_default_color_on_secondary = 0x7f060057
com.dhass.myapp:animator/m3_card_elevated_state_list_anim = 0x7f02000f
com.dhass.myapp:color/design_dark_default_color_secondary = 0x7f06004f
com.dhass.myapp:id/book_now = 0x7f0a0070
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0600e6
com.dhass.myapp:attr/hideOnScroll = 0x7f04020d
com.dhass.myapp:drawable/amu_bubble_mask = 0x7f080055
com.dhass.myapp:color/design_dark_default_color_primary_dark = 0x7f06004d
com.dhass.myapp:dimen/test_navigation_bar_icon_size = 0x7f07026b
com.dhass.myapp:attr/behavior_autoShrink = 0x7f040063
com.dhass.myapp:styleable/MotionTelltales = 0x7f140069
com.dhass.myapp:color/m3_navigation_item_background_color = 0x7f0600a5
com.dhass.myapp:dimen/abc_text_size_medium_material = 0x7f070049
com.dhass.myapp:color/common_google_signin_btn_text_light_pressed = 0x7f060041
com.dhass.myapp:attr/track = 0x7f040469
com.dhass.myapp:color/common_google_signin_btn_text_light_default = 0x7f06003e
com.dhass.myapp:color/common_google_signin_btn_text_dark_pressed = 0x7f06003c
com.dhass.myapp:style/ThemeOverlay.Material3.Light = 0x7f1302d4
com.dhass.myapp:attr/dialogTheme = 0x7f040177
com.dhass.myapp:color/common_google_signin_btn_text_dark_default = 0x7f060039
com.dhass.myapp:dimen/design_fab_size_normal = 0x7f070077
com.dhass.myapp:id/gone = 0x7f0a012a
com.dhass.myapp:color/checkbox_themeable_attribute_color = 0x7f060035
com.dhass.myapp:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.dhass.myapp:attr/colorPrimaryDark = 0x7f0400fc
com.dhass.myapp:attr/placeholderText = 0x7f040347
com.dhass.myapp:color/cardview_shadow_start_color = 0x7f060032
com.dhass.myapp:attr/wavePeriod = 0x7f040493
com.dhass.myapp:color/call_notification_answer_color = 0x7f06002d
com.dhass.myapp:dimen/mtrl_fab_min_touch_target = 0x7f0701fe
com.dhass.myapp:attr/cameraMaxZoomPreference = 0x7f040094
com.dhass.myapp:style/ExoStyledControls.TimeText.Duration = 0x7f13013e
com.dhass.myapp:color/primary_dark_material_light = 0x7f060259
com.dhass.myapp:styleable/AppCompatTextView = 0x7f140011
com.dhass.myapp:layout/m3_alert_dialog_title = 0x7f0d0046
com.dhass.myapp:attr/textAppearanceOverline = 0x7f040421
com.dhass.myapp:color/browser_actions_bg_grey = 0x7f060027
com.dhass.myapp:color/m3_sys_color_dynamic_dark_surface = 0x7f060171
com.dhass.myapp:style/Theme.MaterialComponents.Dialog = 0x7f130288
com.dhass.myapp:color/bright_foreground_material_light = 0x7f060026
com.dhass.myapp:color/bright_foreground_inverse_material_light = 0x7f060024
com.dhass.myapp:color/bright_foreground_inverse_material_dark = 0x7f060023
com.dhass.myapp:color/tooltip_background_dark = 0x7f060271
com.dhass.myapp:attr/motionTarget = 0x7f040314
com.dhass.myapp:color/mtrl_btn_text_color_disabled = 0x7f060227
com.dhass.myapp:color/bright_foreground_disabled_material_dark = 0x7f060021
com.dhass.myapp:drawable/mtrl_popupmenu_background = 0x7f080113
com.dhass.myapp:color/background_floating_material_light = 0x7f06001e
com.dhass.myapp:drawable/abc_ic_clear_material = 0x7f08001d
com.dhass.myapp:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0701cb
com.dhass.myapp:string/exo_item_list = 0x7f12008a
com.dhass.myapp:attr/keylines = 0x7f040253
com.dhass.myapp:color/background_floating_material_dark = 0x7f06001d
com.dhass.myapp:attr/colorOnPrimaryContainer = 0x7f0400f0
com.dhass.myapp:color/accent_material_dark = 0x7f060019
com.dhass.myapp:dimen/abc_text_size_large_material = 0x7f070048
com.dhass.myapp:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f13039c
com.dhass.myapp:attr/actionMenuTextColor = 0x7f04000f
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f13014f
com.dhass.myapp:attr/queryHint = 0x7f04035f
com.dhass.myapp:color/design_dark_default_color_on_primary = 0x7f060049
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f13017b
com.dhass.myapp:id/texture_view = 0x7f0a0251
com.dhass.myapp:dimen/mtrl_badge_radius = 0x7f070193
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f130170
com.dhass.myapp:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f130065
com.dhass.myapp:string/m3_ref_typeface_brand_medium = 0x7f1200bc
com.dhass.myapp:layout/text_view_with_line_height_from_layout = 0x7f0d009d
com.dhass.myapp:color/abc_tint_spinner = 0x7f060017
com.dhass.myapp:color/abc_tint_seek_thumb = 0x7f060016
com.dhass.myapp:color/abc_search_url_text_normal = 0x7f06000e
com.dhass.myapp:color/abc_tint_edittext = 0x7f060015
com.dhass.myapp:id/enterAlways = 0x7f0a00d5
com.dhass.myapp:layout/mtrl_navigation_rail_item = 0x7f0d0069
com.dhass.myapp:attr/fabAnimationMode = 0x7f0401c2
com.dhass.myapp:color/abc_tint_btn_checkable = 0x7f060013
com.dhass.myapp:dimen/m3_btn_elevated_btn_elevation = 0x7f0700e9
com.dhass.myapp:color/abc_secondary_text_material_light = 0x7f060012
com.dhass.myapp:color/m3_ref_palette_dynamic_primary60 = 0x7f0600ce
com.dhass.myapp:attr/customDimension = 0x7f040163
com.dhass.myapp:color/m3_sys_color_light_error = 0x7f06018c
com.dhass.myapp:color/abc_secondary_text_material_dark = 0x7f060011
com.dhass.myapp:id/strict_sandbox = 0x7f0a0228
com.dhass.myapp:dimen/mtrl_slider_track_top = 0x7f070235
com.dhass.myapp:attr/dividerHorizontal = 0x7f04017b
com.dhass.myapp:style/TextAppearance.Design.Snackbar.Message = 0x7f1301fc
com.dhass.myapp:attr/hideMotionSpec = 0x7f04020b
com.dhass.myapp:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f130235
com.dhass.myapp:attr/bottomSheetStyle = 0x7f040073
com.dhass.myapp:attr/iconEndPadding = 0x7f04021a
com.dhass.myapp:attr/state_above_anchor = 0x7f0403c5
com.dhass.myapp:color/abc_primary_text_material_light = 0x7f06000c
com.dhass.myapp:dimen/mtrl_alert_dialog_background_inset_top = 0x7f07018f
com.dhass.myapp:styleable/CompoundButton = 0x7f140026
com.dhass.myapp:dimen/exo_settings_main_text_size = 0x7f0700a4
com.dhass.myapp:attr/itemVerticalPadding = 0x7f04024f
com.dhass.myapp:drawable/$avd_show_password__0 = 0x7f080003
com.dhass.myapp:attr/layoutManager = 0x7f040261
com.dhass.myapp:color/abc_decor_view_status_guard_light = 0x7f060006
com.dhass.myapp:styleable/StateListDrawable = 0x7f140086
com.dhass.myapp:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.dhass.myapp:style/Base.Widget.AppCompat.ActionButton = 0x7f1300bd
com.dhass.myapp:attr/flow_lastHorizontalStyle = 0x7f0401e4
com.dhass.myapp:dimen/m3_slider_thumb_elevation = 0x7f070131
com.dhass.myapp:bool/enable_system_foreground_service_default = 0x7f050003
com.dhass.myapp:id/end_time_text = 0x7f0a00d4
com.dhass.myapp:drawable/common_google_signin_btn_icon_light = 0x7f08007c
com.dhass.myapp:string/copy_toast_msg = 0x7f12005c
com.dhass.myapp:bool/abc_action_bar_embed_tabs = 0x7f050000
com.dhass.myapp:attr/autoSizeStepGranularity = 0x7f040041
com.dhass.myapp:attr/yearStyle = 0x7f0404a6
com.dhass.myapp:string/radiogroup_description = 0x7f120112
com.dhass.myapp:drawable/common_google_signin_btn_icon_dark_focused = 0x7f080078
com.dhass.myapp:color/material_dynamic_primary70 = 0x7f0601e3
com.dhass.myapp:attr/windowTransitionStyle = 0x7f0404a4
com.dhass.myapp:drawable/common_google_signin_btn_text_light_normal_background = 0x7f080088
com.dhass.myapp:color/androidx_core_ripple_material_light = 0x7f06001b
com.dhass.myapp:id/accessibility_custom_action_24 = 0x7f0a0027
com.dhass.myapp:attr/actionBarTabStyle = 0x7f040007
com.dhass.myapp:attr/windowSplashScreenIconBackgroundColor = 0x7f0404a3
com.dhass.myapp:dimen/mtrl_btn_letter_spacing = 0x7f0701a9
com.dhass.myapp:attr/windowSplashScreenAnimatedIcon = 0x7f0404a0
com.dhass.myapp:dimen/m3_sys_elevation_level5 = 0x7f070139
com.dhass.myapp:attr/paddingBottomSystemWindowInsets = 0x7f04032e
com.dhass.myapp:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f08001e
com.dhass.myapp:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f130082
com.dhass.myapp:attr/windowNoTitle = 0x7f04049f
com.dhass.myapp:attr/windowMinWidthMinor = 0x7f04049e
com.dhass.myapp:attr/topInsetScrimEnabled = 0x7f040464
com.dhass.myapp:attr/windowMinWidthMajor = 0x7f04049d
com.dhass.myapp:attr/windowFixedWidthMajor = 0x7f04049b
com.dhass.myapp:attr/waveOffset = 0x7f040492
com.dhass.myapp:color/cardview_light_background = 0x7f060030
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary99 = 0x7f0600e0
com.dhass.myapp:anim/fragment_fast_out_extra_slow_in = 0x7f010022
com.dhass.myapp:id/tag_state_description = 0x7f0a0235
com.dhass.myapp:attr/backgroundInsetTop = 0x7f040050
com.dhass.myapp:attr/voiceIcon = 0x7f04048f
com.dhass.myapp:attr/verticalOffsetWithText = 0x7f04048b
com.dhass.myapp:string/material_clock_display_divider = 0x7f1200cb
com.dhass.myapp:dimen/mtrl_low_ripple_hovered_alpha = 0x7f070208
com.dhass.myapp:attr/use_controller = 0x7f040488
com.dhass.myapp:attr/maxHeight = 0x7f0402f0
com.dhass.myapp:bool/enable_system_alarm_service_default = 0x7f050002
com.dhass.myapp:drawable/exo_controls_repeat_off = 0x7f080098
com.dhass.myapp:color/material_dynamic_primary99 = 0x7f0601e7
com.dhass.myapp:color/m3_chip_background_color = 0x7f06008a
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f13020d
com.dhass.myapp:attr/useCompatPadding = 0x7f040484
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0600e7
com.dhass.myapp:attr/uiZoomGestures = 0x7f040482
com.dhass.myapp:id/animateToStart = 0x7f0a005f
com.dhass.myapp:attr/retryImage = 0x7f04036d
com.dhass.myapp:attr/tabPaddingEnd = 0x7f0403f7
com.dhass.myapp:attr/uiZoomControls = 0x7f040481
com.dhass.myapp:id/accessibility_action_clickable_span = 0x7f0a0012
com.dhass.myapp:color/notification_action_color_filter = 0x7f060255
com.dhass.myapp:attr/layout_goneMarginEnd = 0x7f040295
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600c2
com.dhass.myapp:dimen/disabled_alpha_material_dark = 0x7f070093
com.dhass.myapp:attr/constraintSetStart = 0x7f04010d
com.dhass.myapp:anim/abc_popup_exit = 0x7f010004
com.dhass.myapp:attr/materialAlertDialogTitlePanelStyle = 0x7f0402cb
com.dhass.myapp:attr/uiScrollGesturesDuringRotateOrZoom = 0x7f04047f
com.dhass.myapp:dimen/mtrl_progress_circular_size_small = 0x7f070224
com.dhass.myapp:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1302e1
com.dhass.myapp:attr/uiMapToolbar = 0x7f04047c
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Item = 0x7f1303c7
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1303bc
com.dhass.myapp:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0a0271
com.dhass.myapp:attr/triggerSlack = 0x7f040479
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f13043c
com.dhass.myapp:dimen/exo_error_message_text_padding_horizontal = 0x7f070097
com.dhass.myapp:id/action_menu_presenter = 0x7f0a004a
com.dhass.myapp:color/m3_sys_color_dynamic_light_secondary = 0x7f060185
com.dhass.myapp:layout/notification_media_cancel_action = 0x7f0d0078
com.dhass.myapp:id/oval = 0x7f0a01b5
com.dhass.myapp:attr/materialCalendarDay = 0x7f0402d0
com.dhass.myapp:dimen/mtrl_progress_track_thickness = 0x7f070229
com.dhass.myapp:attr/toolbarNavigationButtonStyle = 0x7f04045c
com.dhass.myapp:string/abc_toolbar_collapse_description = 0x7f12001a
com.dhass.myapp:attr/triggerId = 0x7f040477
com.dhass.myapp:string/material_timepicker_select_time = 0x7f1200dd
com.dhass.myapp:color/design_default_color_on_error = 0x7f060055
com.dhass.myapp:attr/transitionShapeAppearance = 0x7f040476
com.dhass.myapp:attr/transitionPathRotate = 0x7f040475
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Body1 = 0x7f130017
com.dhass.myapp:attr/trackTintMode = 0x7f040471
com.dhass.myapp:attr/checkedIconGravity = 0x7f0400ac
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0600ed
com.dhass.myapp:string/exo_track_selection_title_text = 0x7f120095
com.dhass.myapp:dimen/mtrl_shape_corner_size_small_component = 0x7f07022c
com.dhass.myapp:attr/trackThickness = 0x7f04046f
com.dhass.myapp:id/accessibility_custom_action_21 = 0x7f0a0024
com.dhass.myapp:dimen/m3_datepicker_elevation = 0x7f07010c
com.dhass.myapp:attr/backgroundImage = 0x7f04004c
com.dhass.myapp:attr/trackHeight = 0x7f04046e
com.dhass.myapp:attr/textAppearanceTitleMedium = 0x7f040429
com.dhass.myapp:attr/trackCornerRadius = 0x7f04046d
com.dhass.myapp:id/autoCompleteToEnd = 0x7f0a0065
com.dhass.myapp:layout/mtrl_picker_header_selection_text = 0x7f0d006f
com.dhass.myapp:attr/itemPaddingTop = 0x7f04023f
com.dhass.myapp:attr/touchAnchorId = 0x7f040465
com.dhass.myapp:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f130375
com.dhass.myapp:attr/tooltipStyle = 0x7f040462
com.dhass.myapp:attr/toolbarSurfaceStyle = 0x7f04045e
com.dhass.myapp:color/material_slider_halo_color = 0x7f060219
com.dhass.myapp:attr/toolbarStyle = 0x7f04045d
com.dhass.myapp:attr/listPreferredItemPaddingLeft = 0x7f0402b2
com.dhass.myapp:color/m3_ref_palette_neutral_variant50 = 0x7f06010e
com.dhass.myapp:drawable/exo_ic_settings = 0x7f0800aa
com.dhass.myapp:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401d4
com.dhass.myapp:attr/toolbarId = 0x7f04045b
com.dhass.myapp:anim/rns_ios_from_left_foreground_close = 0x7f010030
com.dhass.myapp:attr/titleTextStyle = 0x7f04045a
com.dhass.myapp:attr/titleTextColor = 0x7f040459
com.dhass.myapp:style/Base.V21.Theme.AppCompat = 0x7f130098
com.dhass.myapp:dimen/exo_settings_height = 0x7f0700a2
com.dhass.myapp:attr/arcMode = 0x7f040039
com.dhass.myapp:attr/windowActionBarOverlay = 0x7f040497
com.dhass.myapp:attr/titleMarginBottom = 0x7f040452
com.dhass.myapp:color/m3_sys_color_dynamic_light_primary_container = 0x7f060184
com.dhass.myapp:attr/titleMarginTop = 0x7f040455
com.dhass.myapp:color/mtrl_choice_chip_text_color = 0x7f060234
com.dhass.myapp:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f130414
com.dhass.myapp:attr/titlePositionInterpolator = 0x7f040457
com.dhass.myapp:attr/extraMultilineHeightEnabled = 0x7f0401c0
com.dhass.myapp:attr/titleMarginStart = 0x7f040454
com.dhass.myapp:attr/title = 0x7f04044d
com.dhass.myapp:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f130397
com.dhass.myapp:attr/helperTextEnabled = 0x7f040207
com.dhass.myapp:attr/tickVisible = 0x7f040449
com.dhass.myapp:attr/tickMarkTintMode = 0x7f040448
com.dhass.myapp:drawable/exo_controls_previous = 0x7f080096
com.dhass.myapp:attr/use_artwork = 0x7f040487
com.dhass.myapp:attr/floatingActionButtonTertiaryStyle = 0x7f0401da
com.dhass.myapp:attr/tickMark = 0x7f040446
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1302e9
com.dhass.myapp:attr/nestedScrollViewStyle = 0x7f040320
com.dhass.myapp:attr/materialCardViewStyle = 0x7f0402e2
com.dhass.myapp:attr/tickColorActive = 0x7f040444
com.dhass.myapp:color/vector_tint_color = 0x7f060273
com.dhass.myapp:attr/thumbTint = 0x7f040441
com.dhass.myapp:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.dhass.myapp:styleable/MaterialTimePicker = 0x7f14005f
com.dhass.myapp:style/Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen = 0x7f1302a6
com.dhass.myapp:attr/thumbTextPadding = 0x7f040440
com.dhass.myapp:attr/contentInsetEnd = 0x7f040112
com.dhass.myapp:attr/thumbStrokeWidth = 0x7f04043f
com.dhass.myapp:attr/placeholderImageScaleType = 0x7f040346
com.dhass.myapp:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f130111
com.dhass.myapp:attr/thumbStrokeColor = 0x7f04043e
com.dhass.myapp:attr/thickness = 0x7f04043a
com.dhass.myapp:plurals/mtrl_badge_content_description = 0x7f100002
com.dhass.myapp:color/cardview_dark_background = 0x7f06002f
com.dhass.myapp:drawable/abc_scrubber_track_mtrl_alpha = 0x7f08003f
com.dhass.myapp:attr/themeLineHeight = 0x7f040439
com.dhass.myapp:drawable/common_google_signin_btn_icon_light_focused = 0x7f08007d
com.dhass.myapp:color/m3_sys_color_dynamic_light_on_surface = 0x7f06017e
com.dhass.myapp:attr/motionEasingStandard = 0x7f04030e
com.dhass.myapp:color/m3_timepicker_display_ripple_color = 0x7f0601b4
com.dhass.myapp:attr/textStartPadding = 0x7f040437
com.dhass.myapp:attr/allowStacking = 0x7f04002e
com.dhass.myapp:attr/spinnerDropDownItemStyle = 0x7f0403b9
com.dhass.myapp:attr/theme = 0x7f040438
com.dhass.myapp:attr/textLocale = 0x7f040436
com.dhass.myapp:drawable/btn_radio_off_mtrl = 0x7f080072
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f13020b
com.dhass.myapp:attr/textInputStyle = 0x7f040435
com.dhass.myapp:attr/titleMarginEnd = 0x7f040453
com.dhass.myapp:attr/textInputOutlinedStyle = 0x7f040434
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1301a4
com.dhass.myapp:drawable/ic_mtrl_checked_circle = 0x7f0800f1
com.dhass.myapp:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f040433
com.dhass.myapp:attr/textInputOutlinedDenseStyle = 0x7f040432
com.dhass.myapp:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0701de
com.dhass.myapp:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f130069
com.dhass.myapp:id/google_wallet_classic = 0x7f0a012c
com.dhass.myapp:attr/textInputFilledDenseStyle = 0x7f04042e
com.dhass.myapp:style/ExoStyledControls.Button.Center.RewWithAmount = 0x7f13013b
com.dhass.myapp:attr/textColorAlertDialogListItem = 0x7f04042b
com.dhass.myapp:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f080109
com.dhass.myapp:styleable/CheckedTextView = 0x7f14001d
com.dhass.myapp:id/spread_inside = 0x7f0a0219
com.dhass.myapp:attr/textAppearanceTitleSmall = 0x7f04042a
com.dhass.myapp:attr/textAppearanceTitleLarge = 0x7f040428
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130305
com.dhass.myapp:attr/textAppearanceSubtitle2 = 0x7f040427
com.dhass.myapp:attr/viewInflaterClass = 0x7f04048d
com.dhass.myapp:attr/textAppearanceSubtitle1 = 0x7f040426
com.dhass.myapp:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1302b2
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13029a
com.dhass.myapp:style/TextAppearance.Compat.Notification.Time = 0x7f1301f0
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.Shuffle = 0x7f130134
com.dhass.myapp:animator/m3_elevated_chip_state_list_anim = 0x7f020012
com.dhass.myapp:style/Widget.AppCompat.ProgressBar = 0x7f13034b
com.dhass.myapp:attr/tabIndicatorAnimationDuration = 0x7f0403eb
com.dhass.myapp:layout/material_timepicker_textinput_display = 0x7f0d0055
com.dhass.myapp:attr/colorError = 0x7f0400e9
com.dhass.myapp:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f080106
com.dhass.myapp:dimen/notification_action_text_size = 0x7f07024f
com.dhass.myapp:layout/design_bottom_sheet_dialog = 0x7f0d0026
com.dhass.myapp:attr/titleCollapseMode = 0x7f04044f
com.dhass.myapp:attr/drawableBottomCompat = 0x7f040185
com.dhass.myapp:layout/custom_dialog = 0x7f0d0024
com.dhass.myapp:attr/textAppearanceSearchResultTitle = 0x7f040424
com.dhass.myapp:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070192
com.dhass.myapp:attr/textAppearanceSearchResultSubtitle = 0x7f040423
com.dhass.myapp:string/fab_transformation_sheet_behavior = 0x7f1200a2
com.dhass.myapp:attr/textAppearancePopupMenuHeader = 0x7f040422
com.dhass.myapp:attr/colorSurface = 0x7f040104
com.dhass.myapp:attr/linearProgressIndicatorStyle = 0x7f0402a5
com.dhass.myapp:attr/textAppearanceListItemSecondary = 0x7f04041f
com.dhass.myapp:drawable/avd_show_password = 0x7f08006d
com.dhass.myapp:drawable/ic_clock_black_24dp = 0x7f0800e8
com.dhass.myapp:attr/ambientEnabled = 0x7f040032
com.dhass.myapp:dimen/m3_extended_fab_min_height = 0x7f070111
com.dhass.myapp:attr/textAppearanceListItem = 0x7f04041e
com.dhass.myapp:attr/textAppearanceLabelSmall = 0x7f04041b
com.dhass.myapp:drawable/ic_m3_chip_checked_circle = 0x7f0800ef
com.dhass.myapp:attr/textAppearanceHeadline6 = 0x7f040415
com.dhass.myapp:style/Widget.MaterialComponents.CardView = 0x7f130418
com.dhass.myapp:attr/cameraTargetLng = 0x7f040097
com.dhass.myapp:attr/customColorValue = 0x7f040162
com.dhass.myapp:color/design_box_stroke_color = 0x7f060044
com.dhass.myapp:attr/tabMode = 0x7f0403f4
com.dhass.myapp:drawable/abc_ic_ab_back_material = 0x7f08001b
com.dhass.myapp:dimen/test_navigation_bar_text_size = 0x7f070270
com.dhass.myapp:attr/progressBarPadding = 0x7f04035c
com.dhass.myapp:attr/autofillInlineSuggestionSubtitle = 0x7f040048
com.dhass.myapp:attr/textAppearanceHeadline5 = 0x7f040414
com.dhass.myapp:attr/actionTextColorAlpha = 0x7f040022
com.dhass.myapp:attr/textAppearanceHeadline2 = 0x7f040411
com.dhass.myapp:attr/tintMode = 0x7f04044c
com.dhass.myapp:dimen/test_navigation_bar_active_item_max_width = 0x7f070266
com.dhass.myapp:attr/layout_constraintEnd_toEndOf = 0x7f040272
com.dhass.myapp:attr/textAppearanceBody1 = 0x7f040406
com.dhass.myapp:styleable/ViewStubCompat = 0x7f14009c
com.dhass.myapp:styleable/AppCompatImageView = 0x7f14000e
com.dhass.myapp:attr/maskedWalletDetailsLogoImageType = 0x7f0402c4
com.dhass.myapp:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080011
com.dhass.myapp:color/m3_ref_palette_secondary90 = 0x7f06012c
com.dhass.myapp:attr/counterMaxLength = 0x7f040131
com.dhass.myapp:id/fullscreen_header = 0x7f0a0125
com.dhass.myapp:attr/telltales_velocityMode = 0x7f040404
com.dhass.myapp:string/mtrl_picker_text_input_date_hint = 0x7f1200fb
com.dhass.myapp:attr/flow_horizontalBias = 0x7f0401e0
com.dhass.myapp:color/m3_ref_palette_neutral_variant10 = 0x7f060109
com.dhass.myapp:integer/m3_sys_motion_duration_400 = 0x7f0b0019
com.dhass.myapp:attr/tabStyle = 0x7f0403fd
com.dhass.myapp:string/common_open_on_phone = 0x7f120059
com.dhass.myapp:attr/telltales_tailScale = 0x7f040403
com.dhass.myapp:style/Widget.Material3.Toolbar = 0x7f1303f3
com.dhass.myapp:layout/mtrl_auto_complete_simple_item = 0x7f0d005c
com.dhass.myapp:anim/rns_slide_in_from_right = 0x7f01003c
com.dhass.myapp:attr/targetId = 0x7f040401
com.dhass.myapp:attr/tabUnboundedRipple = 0x7f040400
com.dhass.myapp:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1303e7
com.dhass.myapp:id/mtrl_picker_header = 0x7f0a0197
com.dhass.myapp:color/m3_ref_palette_neutral80 = 0x7f060104
com.dhass.myapp:color/design_default_color_primary_variant = 0x7f06005b
com.dhass.myapp:attr/titleMargin = 0x7f040451
com.dhass.myapp:attr/tabTextAppearance = 0x7f0403fe
com.dhass.myapp:attr/iconTint = 0x7f04021f
com.dhass.myapp:color/common_google_signin_btn_text_dark_focused = 0x7f06003b
com.dhass.myapp:attr/tabSecondaryStyle = 0x7f0403fb
com.dhass.myapp:dimen/mtrl_btn_corner_radius = 0x7f07019f
com.dhass.myapp:attr/contentInsetRight = 0x7f040115
com.dhass.myapp:color/m3_ref_palette_dynamic_primary10 = 0x7f0600c8
com.dhass.myapp:drawable/$avd_hide_password__1 = 0x7f080001
com.dhass.myapp:id/exo_text = 0x7f0a0105
com.dhass.myapp:dimen/m3_btn_elevation = 0x7f0700ea
com.dhass.myapp:attr/titleTextAppearance = 0x7f040458
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary95 = 0x7f0600df
com.dhass.myapp:dimen/mtrl_textinput_box_corner_radius_small = 0x7f07023f
com.dhass.myapp:animator/mtrl_fab_show_motion_spec = 0x7f02001d
com.dhass.myapp:color/design_fab_shadow_end_color = 0x7f060060
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1301e4
com.dhass.myapp:drawable/ic_alert = 0x7f0800e1
com.dhass.myapp:styleable/BaseProgressIndicator = 0x7f140016
com.dhass.myapp:anim/abc_tooltip_enter = 0x7f01000a
com.dhass.myapp:attr/tabMinWidth = 0x7f0403f3
com.dhass.myapp:attr/tabInlineLabel = 0x7f0403f1
com.dhass.myapp:id/android_pay = 0x7f0a0059
com.dhass.myapp:color/abc_color_highlight_material = 0x7f060004
com.dhass.myapp:attr/autoSizeTextType = 0x7f040042
com.dhass.myapp:attr/overlay = 0x7f04032b
com.dhass.myapp:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1301d5
com.dhass.myapp:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401bf
com.dhass.myapp:id/action_text = 0x7f0a004e
com.dhass.myapp:attr/tabContentStart = 0x7f0403e6
com.dhass.myapp:dimen/abc_panel_menu_list_width = 0x7f070034
com.dhass.myapp:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1300d7
com.dhass.myapp:attr/tooltipForegroundColor = 0x7f040460
com.dhass.myapp:color/exo_edit_mode_background_color = 0x7f060072
com.dhass.myapp:attr/tabBackground = 0x7f0403e5
com.dhass.myapp:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f130446
com.dhass.myapp:attr/switchPadding = 0x7f0403e2
com.dhass.myapp:attr/paddingBottomNoButtons = 0x7f04032d
com.dhass.myapp:attr/motion_postLayoutCollision = 0x7f040315
com.dhass.myapp:attr/circleCrop = 0x7f0400c6
com.dhass.myapp:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f070212
com.dhass.myapp:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1303f2
com.dhass.myapp:attr/suffixTextAppearance = 0x7f0403dc
com.dhass.myapp:color/material_grey_850 = 0x7f060207
com.dhass.myapp:color/material_dynamic_tertiary99 = 0x7f060201
com.dhass.myapp:color/m3_ref_palette_neutral_variant99 = 0x7f060114
com.dhass.myapp:drawable/ic_launcher_background = 0x7f0800ed
com.dhass.myapp:attr/suffixText = 0x7f0403db
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f130208
com.dhass.myapp:attr/barrierDirection = 0x7f040060
com.dhass.myapp:string/catalyst_hot_reloading_auto_enable = 0x7f120034
com.dhass.myapp:attr/subtitleTextAppearance = 0x7f0403d8
com.dhass.myapp:xml/rn_dev_preferences = 0x7f150006
com.dhass.myapp:attr/subtitleCentered = 0x7f0403d7
com.dhass.myapp:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401d2
com.dhass.myapp:attr/cropBorderCornerLength = 0x7f04013b
com.dhass.myapp:style/TextAppearance.MaterialComponents.Headline5 = 0x7f13022a
com.dhass.myapp:id/skipCollapsed = 0x7f0a020a
com.dhass.myapp:color/material_cursor_color = 0x7f0601bd
com.dhass.myapp:styleable/PopupWindow = 0x7f140072
com.dhass.myapp:attr/subheaderTextAppearance = 0x7f0403d4
com.dhass.myapp:attr/subheaderInsetStart = 0x7f0403d3
com.dhass.myapp:attr/subMenuArrow = 0x7f0403d0
com.dhass.myapp:id/autofill_inline_suggestion_start_icon = 0x7f0a0068
com.dhass.myapp:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f070127
com.dhass.myapp:attr/strokeWidth = 0x7f0403cf
com.dhass.myapp:layout/abc_popup_menu_item_layout = 0x7f0d0013
com.dhass.myapp:attr/strokeColor = 0x7f0403ce
com.dhass.myapp:id/textStart = 0x7f0a0245
com.dhass.myapp:attr/statusBarForeground = 0x7f0403cc
com.dhass.myapp:style/TextAppearance.Test.UsesDp = 0x7f130232
com.dhass.myapp:attr/statusBarBackground = 0x7f0403cb
com.dhass.myapp:attr/state_lifted = 0x7f0403ca
com.dhass.myapp:attr/state_liftable = 0x7f0403c9
com.dhass.myapp:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f070178
com.dhass.myapp:attr/fontStyle = 0x7f0401f7
com.dhass.myapp:attr/sizePercent = 0x7f0403b2
com.dhass.myapp:attr/state_collapsed = 0x7f0403c6
com.dhass.myapp:dimen/mtrl_bottomappbar_height = 0x7f07019e
com.dhass.myapp:attr/textEndPadding = 0x7f04042d
com.dhass.myapp:attr/flow_lastVerticalStyle = 0x7f0401e6
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600bc
com.dhass.myapp:attr/startIconTintMode = 0x7f0403c4
com.dhass.myapp:style/Widget.MaterialComponents.TabLayout = 0x7f130455
com.dhass.myapp:style/TextAppearance.Material3.DisplayLarge = 0x7f130213
com.dhass.myapp:attr/minSeparation = 0x7f0402fa
com.dhass.myapp:attr/startIconTint = 0x7f0403c3
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f13002e
com.dhass.myapp:attr/buttonBarButtonStyle = 0x7f040081
com.dhass.myapp:attr/showMotionSpec = 0x7f04039e
com.dhass.myapp:dimen/exo_error_message_height = 0x7f070095
com.dhass.myapp:attr/actionOverflowButtonStyle = 0x7f04001f
com.dhass.myapp:string/mtrl_exceed_max_badge_number_content_description = 0x7f1200e4
com.dhass.myapp:dimen/compat_notification_large_icon_max_height = 0x7f07005f
com.dhass.myapp:attr/liteMode = 0x7f0402b5
com.dhass.myapp:attr/startIconContentDescription = 0x7f0403c1
com.dhass.myapp:color/switch_thumb_disabled_material_light = 0x7f060269
com.dhass.myapp:drawable/abc_ratingbar_indicator_material = 0x7f080038
com.dhass.myapp:dimen/action_bar_size = 0x7f070051
com.dhass.myapp:attr/layout_constraintTop_toTopOf = 0x7f040289
com.dhass.myapp:color/accent_material_light = 0x7f06001a
com.dhass.myapp:style/Theme.AppCompat.DayNight = 0x7f13023b
com.dhass.myapp:attr/startIconCheckable = 0x7f0403c0
com.dhass.myapp:attr/enforceMaterialTheme = 0x7f0401a4
com.dhass.myapp:attr/stackFromEnd = 0x7f0403be
com.dhass.myapp:bool/mtrl_btn_textappearance_all_caps = 0x7f050006
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1303cb
com.dhass.myapp:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f130183
com.dhass.myapp:color/exo_styled_error_message_background = 0x7f060074
com.dhass.myapp:drawable/design_snackbar_background = 0x7f08008f
com.dhass.myapp:attr/onTouchUp = 0x7f040329
com.dhass.myapp:style/ThemeOverlay.Design.TextInputEditText = 0x7f1302b4
com.dhass.myapp:attr/srcCompat = 0x7f0403bd
com.dhass.myapp:id/unchecked = 0x7f0a0266
com.dhass.myapp:attr/maskedWalletDetailsLogoTextColor = 0x7f0402c5
com.dhass.myapp:attr/splashScreenIconSize = 0x7f0403bb
com.dhass.myapp:attr/spinnerStyle = 0x7f0403ba
com.dhass.myapp:style/TextAppearance.AppCompat.Tooltip = 0x7f1301d6
com.dhass.myapp:drawable/notification_bg_normal_pressed = 0x7f08012a
com.dhass.myapp:attr/region_widthLessThan = 0x7f040369
com.dhass.myapp:dimen/exo_small_icon_width = 0x7f0700ac
com.dhass.myapp:attr/drawerArrowStyle = 0x7f04018e
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_primary = 0x7f060164
com.dhass.myapp:attr/spinBars = 0x7f0403b8
com.dhass.myapp:attr/snackbarTextViewStyle = 0x7f0403b6
com.dhass.myapp:layout/abc_screen_content_include = 0x7f0d0014
com.dhass.myapp:dimen/splashscreen_icon_mask_size_with_background = 0x7f07025e
com.dhass.myapp:attr/expandedTitleTextColor = 0x7f0401b9
com.dhass.myapp:color/m3_sys_color_dark_on_surface = 0x7f060152
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.Large.End = 0x7f13017d
com.dhass.myapp:attr/colorOnTertiary = 0x7f0400f7
com.dhass.myapp:attr/snackbarButtonStyle = 0x7f0403b4
com.dhass.myapp:integer/m3_sys_motion_duration_1000 = 0x7f0b0013
com.dhass.myapp:attr/zOrderOnTop = 0x7f0404a8
com.dhass.myapp:dimen/m3_btn_icon_btn_padding_right = 0x7f0700ec
com.dhass.myapp:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f070239
com.dhass.myapp:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.dhass.myapp:style/Theme.Design.Light.NoActionBar = 0x7f130257
com.dhass.myapp:drawable/notification_template_icon_low_bg = 0x7f08012e
com.dhass.myapp:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f070123
com.dhass.myapp:attr/trackColorInactive = 0x7f04046c
com.dhass.myapp:id/ifRoom = 0x7f0a0148
com.dhass.myapp:attr/colorOnTertiaryContainer = 0x7f0400f8
com.dhass.myapp:styleable/BottomSheetBehavior_Layout = 0x7f140019
com.dhass.myapp:attr/singleLine = 0x7f0403b0
com.dhass.myapp:id/icon_only = 0x7f0a0147
com.dhass.myapp:drawable/abc_star_half_black_48dp = 0x7f080046
com.dhass.myapp:attr/singleChoiceItemLayout = 0x7f0403af
com.dhass.myapp:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1303b5
com.dhass.myapp:attr/helperTextTextAppearance = 0x7f040208
com.dhass.myapp:attr/shutter_background_color = 0x7f0403ac
com.dhass.myapp:id/notification_main_column_container = 0x7f0a01ae
com.dhass.myapp:attr/shrinkMotionSpec = 0x7f0403ab
com.dhass.myapp:layout/abc_activity_chooser_view = 0x7f0d0006
com.dhass.myapp:attr/cropCornerCircleFillColor = 0x7f040141
com.dhass.myapp:attr/show_vr_button = 0x7f0403aa
com.dhass.myapp:attr/show_shuffle_button = 0x7f0403a7
com.dhass.myapp:attr/show_previous_button = 0x7f0403a5
com.dhass.myapp:string/exo_controls_playback_speed = 0x7f120073
com.dhass.myapp:drawable/node_modules_exporouter_assets_pkg = 0x7f08011a
com.dhass.myapp:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1300ba
com.dhass.myapp:dimen/abc_action_bar_default_height_material = 0x7f070002
com.dhass.myapp:attr/showPaths = 0x7f04039f
com.dhass.myapp:integer/mtrl_chip_anim_duration = 0x7f0b0032
com.dhass.myapp:attr/showDividers = 0x7f04039d
com.dhass.myapp:attr/showDelay = 0x7f04039c
com.dhass.myapp:attr/menu = 0x7f0402f6
com.dhass.myapp:attr/showAsAction = 0x7f04039b
com.dhass.myapp:dimen/mtrl_slider_widget_height = 0x7f070236
com.dhass.myapp:attr/visibilityMode = 0x7f04048e
com.dhass.myapp:attr/gestureInsetBottomIgnored = 0x7f040200
com.dhass.myapp:styleable/ButtonBarLayout = 0x7f14001a
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1301dc
com.dhass.myapp:anim/abc_popup_enter = 0x7f010003
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge.Top = 0x7f130178
com.dhass.myapp:string/material_timepicker_minute = 0x7f1200db
com.dhass.myapp:attr/shapeAppearanceOverlay = 0x7f040397
com.dhass.myapp:id/accessibility_custom_action_29 = 0x7f0a002c
com.dhass.myapp:attr/cornerSizeBottomRight = 0x7f04012d
com.dhass.myapp:attr/selectionRequired = 0x7f040392
com.dhass.myapp:attr/tickColorInactive = 0x7f040445
com.dhass.myapp:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070196
com.dhass.myapp:attr/layout_behavior = 0x7f040264
com.dhass.myapp:anim/rns_default_exit_out = 0x7f010029
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600bd
com.dhass.myapp:attr/selectableItemBackground = 0x7f040390
com.dhass.myapp:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1302db
com.dhass.myapp:string/catalyst_dismiss_button = 0x7f120030
com.dhass.myapp:attr/cameraTargetLat = 0x7f040096
com.dhass.myapp:attr/seekBarStyle = 0x7f04038f
com.dhass.myapp:attr/materialCalendarHeaderLayout = 0x7f0402d6
com.dhass.myapp:color/abc_primary_text_material_dark = 0x7f06000b
com.dhass.myapp:color/material_dynamic_neutral70 = 0x7f0601c9
com.dhass.myapp:attr/fontProviderPackage = 0x7f0401f4
com.dhass.myapp:attr/searchViewStyle = 0x7f04038e
com.dhass.myapp:attr/scrubber_drawable = 0x7f04038a
com.dhass.myapp:drawable/ripple_effect = 0x7f080135
com.dhass.myapp:layout/abc_screen_simple_overlay_action_mode = 0x7f0d0016
com.dhass.myapp:attr/scrubber_disabled_size = 0x7f040388
com.dhass.myapp:attr/hintTextColor = 0x7f040213
com.dhass.myapp:attr/scrubber_color = 0x7f040387
com.dhass.myapp:attr/scopeUris = 0x7f040383
com.dhass.myapp:style/Base.Theme.Material3.Light.Dialog = 0x7f13005d
com.dhass.myapp:attr/round = 0x7f040371
com.dhass.myapp:dimen/notification_main_column_padding_top = 0x7f070254
com.dhass.myapp:attr/region_widthMoreThan = 0x7f04036a
com.dhass.myapp:id/button = 0x7f0a0078
com.dhass.myapp:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f13004d
com.dhass.myapp:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080024
com.dhass.myapp:attr/flow_verticalAlign = 0x7f0401e9
com.dhass.myapp:attr/region_heightLessThan = 0x7f040367
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1303c5
com.dhass.myapp:style/Base.V21.Theme.MaterialComponents = 0x7f13009c
com.dhass.myapp:attr/recyclerViewStyle = 0x7f040366
com.dhass.myapp:attr/ratingBarStyleSmall = 0x7f040365
com.dhass.myapp:id/normal = 0x7f0a01ab
com.dhass.myapp:id/browser_actions_menu_item_text = 0x7f0a0075
com.dhass.myapp:styleable/MaterialAlertDialogTheme = 0x7f140052
com.dhass.myapp:dimen/mtrl_btn_disabled_elevation = 0x7f0701a1
com.dhass.myapp:anim/design_snackbar_in = 0x7f010020
com.dhass.myapp:attr/ratingBarStyle = 0x7f040363
com.dhass.myapp:attr/queryPatterns = 0x7f040360
com.dhass.myapp:id/android_pay_light = 0x7f0a005b
com.dhass.myapp:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0700f9
com.dhass.myapp:style/TextAppearance.AppCompat.Button = 0x7f1301bd
com.dhass.myapp:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.dhass.myapp:attr/time_bar_min_update_interval = 0x7f04044a
com.dhass.myapp:animator/design_fab_show_motion_spec = 0x7f020002
com.dhass.myapp:attr/roundingBorderWidth = 0x7f040380
com.dhass.myapp:attr/curveFit = 0x7f04015f
com.dhass.myapp:string/material_minute_suffix = 0x7f1200d0
com.dhass.myapp:dimen/abc_control_corner_material = 0x7f070018
com.dhass.myapp:attr/preserveIconSpacing = 0x7f040356
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600d6
com.dhass.myapp:bool/isTablet = 0x7f050005
com.dhass.myapp:attr/singleSelection = 0x7f0403b1
com.dhass.myapp:style/Widget.AppCompat.ActionBar.TabBar = 0x7f130315
com.dhass.myapp:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f130064
com.dhass.myapp:id/line1 = 0x7f0a015d
com.dhass.myapp:attr/prefixTextColor = 0x7f040355
com.dhass.myapp:attr/postSplashScreenTheme = 0x7f040352
com.dhass.myapp:color/m3_ref_palette_white = 0x7f06013c
com.dhass.myapp:color/abc_search_url_text_selected = 0x7f060010
com.dhass.myapp:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700d0
com.dhass.myapp:attr/touchAnchorSide = 0x7f040466
com.dhass.myapp:dimen/cardview_compat_inset_shadow = 0x7f070056
com.dhass.myapp:attr/roundAsCircle = 0x7f040372
com.dhass.myapp:attr/popupWindowStyle = 0x7f040351
com.dhass.myapp:dimen/m3_navigation_item_icon_padding = 0x7f07011d
com.dhass.myapp:color/design_fab_stroke_top_outer_color = 0x7f060066
com.dhass.myapp:styleable/KeyTimeCycle = 0x7f140048
com.dhass.myapp:attr/passwordToggleTint = 0x7f04033b
com.dhass.myapp:attr/popupTheme = 0x7f040350
com.dhass.myapp:string/spinbutton_description = 0x7f120119
com.dhass.myapp:id/textinput_placeholder = 0x7f0a024e
com.dhass.myapp:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f070226
com.dhass.myapp:id/action_container = 0x7f0a0045
com.dhass.myapp:attr/subtitleTextColor = 0x7f0403d9
com.dhass.myapp:style/Base.V14.Theme.MaterialComponents = 0x7f13008a
com.dhass.myapp:id/hybrid = 0x7f0a013f
com.dhass.myapp:attr/played_ad_marker_color = 0x7f04034b
com.dhass.myapp:dimen/m3_chip_dragged_translation_z = 0x7f070108
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1301e5
com.dhass.myapp:layout/exo_styled_settings_list = 0x7f0d003c
com.dhass.myapp:attr/placeholder_emptyVisibility = 0x7f04034a
com.dhass.myapp:dimen/m3_btn_text_btn_padding_left = 0x7f0700fa
com.dhass.myapp:id/ghost_view = 0x7f0a0127
com.dhass.myapp:attr/textAppearanceDisplayLarge = 0x7f04040d
com.dhass.myapp:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f07024d
com.dhass.myapp:animator/linear_indeterminate_line2_tail_interpolator = 0x7f02000c
com.dhass.myapp:color/design_fab_stroke_end_outer_color = 0x7f060064
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_screenshort = 0x7f080066
com.dhass.myapp:color/material_dynamic_tertiary40 = 0x7f0601fa
com.dhass.myapp:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1303fc
com.dhass.myapp:id/current_time_text = 0x7f0a00a7
com.dhass.myapp:integer/m3_sys_motion_duration_700 = 0x7f0b001f
com.dhass.myapp:attr/pivotAnchor = 0x7f040344
com.dhass.myapp:color/m3_sys_color_dark_error = 0x7f060146
com.dhass.myapp:attr/perpendicularPath_percent = 0x7f040343
com.dhass.myapp:attr/percentY = 0x7f040342
com.dhass.myapp:dimen/mtrl_btn_focused_z = 0x7f0701a4
com.dhass.myapp:style/WalletFragmentDefaultStyle = 0x7f130312
com.dhass.myapp:attr/windowSplashScreenAnimationDuration = 0x7f0404a1
com.dhass.myapp:string/enter_fullscreen_mode = 0x7f120064
com.dhass.myapp:attr/paddingStart = 0x7f040332
com.dhass.myapp:style/TextAppearance.Material3.DisplayMedium = 0x7f130214
com.dhass.myapp:style/ExoStyledControls.Button.Bottom.PlaybackSpeed = 0x7f130131
com.dhass.myapp:layout/design_layout_tab_text = 0x7f0d002a
com.dhass.myapp:id/exo_overflow_show = 0x7f0a00f1
com.dhass.myapp:attr/paddingRightSystemWindowInsets = 0x7f040331
com.dhass.myapp:attr/floatingActionButtonStyle = 0x7f0401d8
com.dhass.myapp:attr/paddingEnd = 0x7f04032f
com.dhass.myapp:layout/crop_image_activity = 0x7f0d0022
com.dhass.myapp:dimen/abc_text_size_caption_material = 0x7f070042
com.dhass.myapp:color/m3_ref_palette_black = 0x7f0600ac
com.dhass.myapp:color/m3_ref_palette_secondary10 = 0x7f060123
com.dhass.myapp:style/Base.Widget.AppCompat.ImageButton = 0x7f1300d2
com.dhass.myapp:layout/fps_view = 0x7f0d0041
com.dhass.myapp:dimen/highlight_alpha_material_dark = 0x7f0700bc
com.dhass.myapp:id/test_checkbox_app_button_tint = 0x7f0a023d
com.dhass.myapp:attr/boxCornerRadiusTopStart = 0x7f04007a
com.dhass.myapp:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700dd
com.dhass.myapp:attr/overlayImage = 0x7f04032c
com.dhass.myapp:id/autoComplete = 0x7f0a0064
com.dhass.myapp:string/combobox_description = 0x7f120049
com.dhass.myapp:attr/sliderStyle = 0x7f0403b3
com.dhass.myapp:style/Theme.SplashScreen.IconBackground = 0x7f1302aa
com.dhass.myapp:color/m3_textfield_label_color = 0x7f0601ad
com.dhass.myapp:attr/overlapAnchor = 0x7f04032a
com.dhass.myapp:attr/scrimAnimationDuration = 0x7f040384
com.dhass.myapp:styleable/Variant = 0x7f140098
com.dhass.myapp:attr/onShow = 0x7f040328
com.dhass.myapp:attr/repeat_toggle_modes = 0x7f04036b
com.dhass.myapp:id/deltaRelative = 0x7f0a00b1
com.dhass.myapp:attr/onCross = 0x7f040324
com.dhass.myapp:id/postLayout = 0x7f0a01c7
com.dhass.myapp:attr/number = 0x7f040322
com.dhass.myapp:attr/nestedScrollable = 0x7f040321
com.dhass.myapp:attr/panelBackground = 0x7f040335
com.dhass.myapp:id/navigation_bar_item_large_label_view = 0x7f0a01a5
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070152
com.dhass.myapp:attr/moveWhenScrollAtTop = 0x7f040317
com.dhass.myapp:attr/nestedScrollFlags = 0x7f04031f
com.dhass.myapp:string/default_error_message = 0x7f120060
com.dhass.myapp:id/search_close_btn = 0x7f0a01f9
com.dhass.myapp:attr/layout_constraintLeft_toRightOf = 0x7f040280
com.dhass.myapp:attr/navigationIconTint = 0x7f04031b
com.dhass.myapp:dimen/mtrl_textinput_box_stroke_width_default = 0x7f070241
com.dhass.myapp:drawable/exo_icon_vr = 0x7f0800bf
com.dhass.myapp:drawable/exo_ic_fullscreen_exit = 0x7f0800a6
com.dhass.myapp:attr/multiChoiceItemLayout = 0x7f040318
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Large = 0x7f130021
com.dhass.myapp:attr/controller_layout_id = 0x7f040122
com.dhass.myapp:attr/floatingActionButtonSurfaceStyle = 0x7f0401d9
com.dhass.myapp:attr/motionStagger = 0x7f040313
com.dhass.myapp:color/m3_text_button_foreground_color_selector = 0x7f0601a8
com.dhass.myapp:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f02001f
com.dhass.myapp:attr/motionPath = 0x7f040310
com.dhass.myapp:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f130093
com.dhass.myapp:id/exo_basic_controls = 0x7f0a00db
com.dhass.myapp:attr/errorIconDrawable = 0x7f0401aa
com.dhass.myapp:attr/motionInterpolator = 0x7f04030f
com.dhass.myapp:id/dimensions = 0x7f0a00b8
com.dhass.myapp:attr/contentPaddingRight = 0x7f04011c
com.dhass.myapp:attr/motionEasingAccelerated = 0x7f04030a
com.dhass.myapp:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f08003e
com.dhass.myapp:attr/motionDurationShort2 = 0x7f040309
com.dhass.myapp:id/action_bar_spinner = 0x7f0a0042
com.dhass.myapp:id/outline = 0x7f0a01b3
com.dhass.myapp:attr/subtitle = 0x7f0403d6
com.dhass.myapp:style/ShapeAppearance.Material3.LargeComponent = 0x7f130189
com.dhass.myapp:attr/buttonBarNeutralButtonStyle = 0x7f040083
com.dhass.myapp:attr/labelBehavior = 0x7f040255
com.dhass.myapp:attr/motionDurationMedium2 = 0x7f040307
com.dhass.myapp:color/m3_ref_palette_neutral_variant95 = 0x7f060113
com.dhass.myapp:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002
com.dhass.myapp:id/mini = 0x7f0a017f
com.dhass.myapp:attr/motionDurationMedium1 = 0x7f040306
com.dhass.myapp:style/TextAppearance.Design.Counter.Overflow = 0x7f1301f6
com.dhass.myapp:attr/prefixTextAppearance = 0x7f040354
com.dhass.myapp:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080006
com.dhass.myapp:attr/cropShape = 0x7f040154
com.dhass.myapp:color/m3_button_ripple_color = 0x7f060082
com.dhass.myapp:color/design_default_color_error = 0x7f060053
com.dhass.myapp:attr/motionDurationLong1 = 0x7f040304
com.dhass.myapp:drawable/$avd_show_password__1 = 0x7f080004
com.dhass.myapp:attr/minHeight = 0x7f0402f8
com.dhass.myapp:attr/colorOnErrorContainer = 0x7f0400ee
com.dhass.myapp:attr/motionDebug = 0x7f040303
com.dhass.myapp:attr/fontProviderSystemFontFamily = 0x7f0401f6
com.dhass.myapp:attr/mock_labelBackgroundColor = 0x7f0402ff
com.dhass.myapp:attr/windowFixedWidthMinor = 0x7f04049c
com.dhass.myapp:color/tooltip_background_light = 0x7f060272
com.dhass.myapp:attr/touch_target_height = 0x7f040468
com.dhass.myapp:id/material_clock_display = 0x7f0a0167
com.dhass.myapp:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700c5
com.dhass.myapp:attr/buttonSize = 0x7f04008a
com.dhass.myapp:dimen/exo_settings_text_height = 0x7f0700a7
com.dhass.myapp:animator/m3_chip_state_list_anim = 0x7f020011
com.dhass.myapp:string/expo_notifications_fallback_channel_name = 0x7f12009c
com.dhass.myapp:attr/itemTextAppearanceActive = 0x7f04024c
com.dhass.myapp:string/google_api_key = 0x7f1200a8
com.dhass.myapp:layout/test_toolbar = 0x7f0d0098
com.dhass.myapp:attr/showText = 0x7f0403a0
com.dhass.myapp:dimen/material_cursor_inset_top = 0x7f070171
com.dhass.myapp:attr/cropShowProgressBar = 0x7f040157
com.dhass.myapp:color/design_dark_default_color_on_surface = 0x7f06004b
com.dhass.myapp:string/search_menu_title = 0x7f120117
com.dhass.myapp:attr/drawableStartCompat = 0x7f04018a
com.dhass.myapp:integer/default_icon_animation_duration = 0x7f0b0006
com.dhass.myapp:attr/minTouchTargetSize = 0x7f0402fb
com.dhass.myapp:attr/closeIconEndPadding = 0x7f0400d0
com.dhass.myapp:color/m3_navigation_item_ripple_color = 0x7f0600a7
com.dhass.myapp:dimen/abc_text_size_headline_material = 0x7f070047
com.dhass.myapp:attr/placeholderTextColor = 0x7f040349
com.dhass.myapp:attr/layout_constraintBottom_toBottomOf = 0x7f04026c
com.dhass.myapp:attr/yearTodayStyle = 0x7f0404a7
com.dhass.myapp:attr/maxWidth = 0x7f0402f4
com.dhass.myapp:attr/tickMarkTint = 0x7f040447
com.dhass.myapp:attr/maxLines = 0x7f0402f2
com.dhass.myapp:attr/maxImageSize = 0x7f0402f1
com.dhass.myapp:attr/buttonBarNegativeButtonStyle = 0x7f040082
com.dhass.myapp:color/abc_tint_default = 0x7f060014
com.dhass.myapp:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f130449
com.dhass.myapp:id/material_clock_period_toggle = 0x7f0a016c
com.dhass.myapp:drawable/navigation_empty_icon = 0x7f080116
com.dhass.myapp:attr/maxAcceleration = 0x7f0402ec
com.dhass.myapp:attr/materialTimePickerTitleStyle = 0x7f0402eb
com.dhass.myapp:attr/uiCompass = 0x7f04047b
com.dhass.myapp:attr/materialTimePickerTheme = 0x7f0402ea
com.dhass.myapp:color/m3_ref_palette_tertiary80 = 0x7f060138
com.dhass.myapp:id/holo_dark = 0x7f0a013a
com.dhass.myapp:attr/materialThemeOverlay = 0x7f0402e8
com.dhass.myapp:dimen/mtrl_navigation_rail_default_width = 0x7f070215
com.dhass.myapp:attr/mapId = 0x7f0402ba
com.dhass.myapp:attr/materialDividerStyle = 0x7f0402e7
com.dhass.myapp:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.dhass.myapp:anim/rns_no_animation_250 = 0x7f010037
com.dhass.myapp:attr/materialDividerHeavyStyle = 0x7f0402e6
com.dhass.myapp:style/Widget.AppCompat.ButtonBar = 0x7f130324
com.dhass.myapp:id/inward = 0x7f0a0150
com.dhass.myapp:attr/badgeRadius = 0x7f040057
com.dhass.myapp:attr/materialDisplayDividerStyle = 0x7f0402e5
com.dhass.myapp:attr/transitionEasing = 0x7f040473
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f130171
com.dhass.myapp:dimen/m3_btn_padding_left = 0x7f0700f4
com.dhass.myapp:dimen/mtrl_calendar_day_height = 0x7f0701bd
com.dhass.myapp:string/mtrl_picker_text_input_year_abbr = 0x7f120100
com.dhass.myapp:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.dhass.myapp:attr/dayTodayStyle = 0x7f04016d
com.dhass.myapp:attr/windowActionModeOverlay = 0x7f040498
com.dhass.myapp:attr/materialCalendarStyle = 0x7f0402dc
com.dhass.myapp:attr/dragScale = 0x7f040182
com.dhass.myapp:attr/actionBarPopupTheme = 0x7f040002
com.dhass.myapp:color/design_dark_default_color_on_secondary = 0x7f06004a
com.dhass.myapp:attr/motionPathRotate = 0x7f040311
com.dhass.myapp:layout/amu_text_bubble = 0x7f0d001d
com.dhass.myapp:attr/deltaPolarRadius = 0x7f040173
com.dhass.myapp:attr/materialCalendarHeaderToggleButton = 0x7f0402d9
com.dhass.myapp:styleable/CardView = 0x7f14001c
com.dhass.myapp:id/edit_query = 0x7f0a00cc
com.dhass.myapp:id/rounded = 0x7f0a01e4
com.dhass.myapp:dimen/mtrl_tooltip_arrowSize = 0x7f070248
com.dhass.myapp:attr/materialCalendarHeaderTitle = 0x7f0402d8
com.dhass.myapp:dimen/mtrl_calendar_action_padding = 0x7f0701b9
com.dhass.myapp:attr/emojiCompatEnabled = 0x7f04019c
com.dhass.myapp:attr/materialCalendarHeaderDivider = 0x7f0402d5
com.dhass.myapp:color/colorPrimary = 0x7f060036
com.dhass.myapp:dimen/design_bottom_navigation_item_min_width = 0x7f07006b
com.dhass.myapp:attr/materialCalendarHeaderCancelButton = 0x7f0402d3
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f130023
com.dhass.myapp:attr/motion_triggerOnCollision = 0x7f040316
com.dhass.myapp:id/baseline = 0x7f0a006c
com.dhass.myapp:layout/abc_dialog_title_material = 0x7f0d000c
com.dhass.myapp:id/position = 0x7f0a01c6
com.dhass.myapp:dimen/abc_dialog_padding_material = 0x7f070024
com.dhass.myapp:attr/materialCalendarFullscreenTheme = 0x7f0402d2
com.dhass.myapp:attr/color = 0x7f0400e1
com.dhass.myapp:dimen/m3_timepicker_display_stroke_width = 0x7f070162
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600db
com.dhass.myapp:anim/catalyst_slide_down = 0x7f01001c
com.dhass.myapp:attr/onHide = 0x7f040325
com.dhass.myapp:attr/itemRippleColor = 0x7f040240
com.dhass.myapp:style/Widget.Autofill.InlineSuggestionSubtitle = 0x7f130360
com.dhass.myapp:color/m3_sys_color_light_on_surface_variant = 0x7f060199
com.dhass.myapp:attr/backgroundTint = 0x7f040054
com.dhass.myapp:attr/materialButtonOutlinedStyle = 0x7f0402cd
com.dhass.myapp:styleable/PreviewView = 0x7f140074
com.dhass.myapp:color/wallet_highlighted_text_holo_light = 0x7f06027b
com.dhass.myapp:bool/enable_system_job_service_default = 0x7f050004
com.dhass.myapp:attr/materialAlertDialogTitleTextStyle = 0x7f0402cc
com.dhass.myapp:dimen/mtrl_textinput_end_icon_margin_start = 0x7f070244
com.dhass.myapp:color/m3_ref_palette_error95 = 0x7f0600f9
com.dhass.myapp:id/textSpacerNoButtons = 0x7f0a0243
com.dhass.myapp:attr/contentPaddingLeft = 0x7f04011b
com.dhass.myapp:style/TextAppearance.AppCompat.Body2 = 0x7f1301bc
com.dhass.myapp:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402c8
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1301db
com.dhass.myapp:attr/maxButtonHeight = 0x7f0402ee
com.dhass.myapp:dimen/m3_chip_elevated_elevation = 0x7f070109
com.dhass.myapp:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080020
com.dhass.myapp:style/Widget.Autofill = 0x7f13035c
com.dhass.myapp:attr/queryBackground = 0x7f04035e
com.dhass.myapp:style/ThemeOverlay.Material3 = 0x7f1302b5
com.dhass.myapp:attr/progressBarStyle = 0x7f04035d
com.dhass.myapp:layout/splash_screen_view = 0x7f0d008f
com.dhass.myapp:attr/maskedWalletDetailsHeaderTextAppearance = 0x7f0402c3
com.dhass.myapp:dimen/mtrl_switch_thumb_elevation = 0x7f07023d
com.dhass.myapp:style/Widget.AppCompat.Spinner = 0x7f130354
com.dhass.myapp:attr/maskedWalletDetailsButtonBackground = 0x7f0402c1
com.dhass.myapp:attr/layout_constraintGuide_end = 0x7f040275
com.dhass.myapp:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0701ef
com.dhass.myapp:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f0701b0
com.dhass.myapp:style/Widget.MaterialComponents.BottomNavigationView = 0x7f130407
com.dhass.myapp:attr/chipStartPadding = 0x7f0400c1
com.dhass.myapp:styleable/ShapeableImageView = 0x7f14007e
com.dhass.myapp:id/seek_bar = 0x7f0a0200
com.dhass.myapp:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401d5
com.dhass.myapp:color/m3_sys_color_dark_surface_variant = 0x7f06015c
com.dhass.myapp:attr/listItemLayout = 0x7f0402aa
com.dhass.myapp:id/disableScroll = 0x7f0a00bc
com.dhass.myapp:color/m3_ref_palette_dynamic_primary20 = 0x7f0600ca
com.dhass.myapp:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401bc
com.dhass.myapp:id/action_bar_subtitle = 0x7f0a0043
com.dhass.myapp:attr/lineHeight = 0x7f0402a3
com.dhass.myapp:color/m3_default_color_primary_text = 0x7f060093
com.dhass.myapp:animator/mtrl_card_state_list_anim = 0x7f020015
com.dhass.myapp:attr/ttcIndex = 0x7f04047a
com.dhass.myapp:attr/selectableItemBackgroundBorderless = 0x7f040391
com.dhass.myapp:attr/limitBoundsTo = 0x7f0402a2
com.dhass.myapp:id/search_go_btn = 0x7f0a01fb
com.dhass.myapp:attr/layout_scrollFlags = 0x7f04029e
com.dhass.myapp:attr/autoSizeMaxTextSize = 0x7f04003e
com.dhass.myapp:attr/layout_optimizationLevel = 0x7f04029c
com.dhass.myapp:style/Base.TextAppearance.MaterialComponents.Button = 0x7f130044
com.dhass.myapp:id/exo_vr = 0x7f0a0108
com.dhass.myapp:attr/motionEasingEmphasized = 0x7f04030c
com.dhass.myapp:attr/layout_goneMarginTop = 0x7f040299
com.dhass.myapp:id/easeIn = 0x7f0a00c9
com.dhass.myapp:drawable/googleg_disabled_color_18 = 0x7f0800df
com.dhass.myapp:attr/flow_firstHorizontalBias = 0x7f0401db
com.dhass.myapp:attr/cardCornerRadius = 0x7f04009b
com.dhass.myapp:attr/colorSwitchThumbNormal = 0x7f040107
com.dhass.myapp:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.dhass.myapp:style/TextAppearance.AppCompat.Inverse = 0x7f1301c4
com.dhass.myapp:attr/textAppearanceLargePopupMenu = 0x7f04041c
com.dhass.myapp:attr/layout_goneMarginBottom = 0x7f040294
com.dhass.myapp:attr/chainUseRtl = 0x7f0400a3
com.dhass.myapp:attr/layout_editor_absoluteY = 0x7f040293
com.dhass.myapp:style/Widget.MaterialComponents.PopupMenu = 0x7f13044b
com.dhass.myapp:bool/workmanager_test_configuration = 0x7f050007
com.dhass.myapp:attr/layout_editor_absoluteX = 0x7f040292
com.dhass.myapp:string/material_timepicker_pm = 0x7f1200dc
com.dhass.myapp:attr/layout_insetEdge = 0x7f04029a
com.dhass.myapp:drawable/exo_ic_forward = 0x7f0800a4
com.dhass.myapp:dimen/mtrl_chip_pressed_translation_z = 0x7f0701e7
com.dhass.myapp:attr/layout_dodgeInsetEdges = 0x7f040291
com.dhass.myapp:attr/indicatorDirectionCircular = 0x7f040229
com.dhass.myapp:id/exo_error_message = 0x7f0a00e5
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1302fb
com.dhass.myapp:style/Theme.AppCompat.DialogWhenLarge = 0x7f130245
com.dhass.myapp:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.dhass.myapp:drawable/avd_hide_password = 0x7f08006c
com.dhass.myapp:attr/layout_constraintWidth_min = 0x7f04028f
com.dhass.myapp:drawable/paused_in_debugger_background = 0x7f080131
com.dhass.myapp:style/Widget.Material3.TabLayout = 0x7f1303e4
com.dhass.myapp:layout/mtrl_calendar_day_of_week = 0x7f0d005e
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f070159
com.dhass.myapp:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700cf
com.dhass.myapp:attr/itemIconSize = 0x7f040239
com.dhass.myapp:style/Base.Theme.SplashScreen = 0x7f130071
com.dhass.myapp:id/exo_extra_controls = 0x7f0a00e6
com.dhass.myapp:attr/layout_constraintVertical_bias = 0x7f04028a
com.dhass.myapp:attr/colorControlActivated = 0x7f0400e6
com.dhass.myapp:string/not_selected = 0x7f120107
com.dhass.myapp:color/highlighted_text_material_light = 0x7f06007a
com.dhass.myapp:string/m3_sys_motion_easing_legacy_accelerate = 0x7f1200c5
com.dhass.myapp:attr/paddingTopSystemWindowInsets = 0x7f040334
com.dhass.myapp:attr/displayOptions = 0x7f040178
com.dhass.myapp:dimen/m3_badge_vertical_offset = 0x7f0700d6
com.dhass.myapp:color/m3_ref_palette_primary99 = 0x7f060121
com.dhass.myapp:attr/scrimBackground = 0x7f040385
com.dhass.myapp:attr/fontProviderFetchStrategy = 0x7f0401f2
com.dhass.myapp:drawable/$avd_hide_password__2 = 0x7f080002
com.dhass.myapp:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1300be
com.dhass.myapp:attr/layout_constraintRight_creator = 0x7f040281
com.dhass.myapp:attr/layout_constraintTop_toBottomOf = 0x7f040288
com.dhass.myapp:color/button_material_dark = 0x7f06002b
com.dhass.myapp:attr/layout_constraintTop_creator = 0x7f040287
com.dhass.myapp:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.dhass.myapp:color/mtrl_textinput_filled_box_default_background_color = 0x7f060252
com.dhass.myapp:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400dd
com.dhass.myapp:color/m3_slider_inactive_track_color = 0x7f060141
com.dhass.myapp:attr/actionModeCopyDrawable = 0x7f040014
com.dhass.myapp:attr/layout_constraintStart_toEndOf = 0x7f040284
com.dhass.myapp:attr/horizontalOffset = 0x7f040216
com.dhass.myapp:attr/layout_constraintHorizontal_bias = 0x7f04027b
com.dhass.myapp:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f130145
com.dhass.myapp:color/error_color_material_dark = 0x7f06006d
com.dhass.myapp:dimen/mtrl_slider_label_padding = 0x7f07022e
com.dhass.myapp:attr/layout_constraintHeight_percent = 0x7f04027a
com.dhass.myapp:string/crop_image_menu_crop = 0x7f12005f
com.dhass.myapp:drawable/abc_btn_default_mtrl_shape = 0x7f08000e
com.dhass.myapp:dimen/m3_navigation_item_shape_inset_start = 0x7f070120
com.dhass.myapp:xml/standalone_badge_gravity_bottom_end = 0x7f150009
com.dhass.myapp:attr/layout_constraintGuide_begin = 0x7f040274
com.dhass.myapp:id/TOP_START = 0x7f0a0010
com.dhass.myapp:attr/layout_constraintDimensionRatio = 0x7f040271
com.dhass.myapp:attr/layout_constrainedWidth = 0x7f040268
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1303d5
com.dhass.myapp:anim/catalyst_fade_out = 0x7f010019
com.dhass.myapp:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f13028a
com.dhass.myapp:attr/actionDropDownStyle = 0x7f04000c
com.dhass.myapp:attr/layout_collapseParallaxMultiplier = 0x7f040266
com.dhass.myapp:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f07023b
com.dhass.myapp:color/material_timepicker_clock_text_color = 0x7f06021f
com.dhass.myapp:attr/layout_anchor = 0x7f040262
com.dhass.myapp:attr/layoutDescription = 0x7f04025f
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_brands = 0x7f11000a
com.dhass.myapp:color/mtrl_tabs_legacy_text_color_selector = 0x7f06024d
com.dhass.myapp:attr/uiRotateGestures = 0x7f04047d
com.dhass.myapp:attr/autoSizePresetSizes = 0x7f040040
com.dhass.myapp:attr/latLngBoundsNorthEastLongitude = 0x7f04025b
com.dhass.myapp:color/m3_ref_palette_secondary30 = 0x7f060126
com.dhass.myapp:attr/errorIconTint = 0x7f0401ab
com.dhass.myapp:attr/lastBaselineToBottomHeight = 0x7f040258
com.dhass.myapp:attr/fastScrollVerticalTrackDrawable = 0x7f0401cf
com.dhass.myapp:attr/actionModeSelectAllDrawable = 0x7f040019
com.dhass.myapp:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f1302a4
com.dhass.myapp:color/m3_ref_palette_dynamic_primary100 = 0x7f0600c9
com.dhass.myapp:dimen/m3_card_elevated_elevation = 0x7f070100
com.dhass.myapp:style/ShapeAppearanceOverlay = 0x7f130194
com.dhass.myapp:attr/chipSpacingVertical = 0x7f0400bf
com.dhass.myapp:id/material_value_index = 0x7f0a0179
com.dhass.myapp:anim/rns_ios_from_right_foreground_close = 0x7f010034
com.dhass.myapp:attr/labelStyle = 0x7f040256
com.dhass.myapp:color/design_fab_shadow_start_color = 0x7f060062
com.dhass.myapp:attr/windowFixedHeightMinor = 0x7f04049a
com.dhass.myapp:dimen/design_tab_text_size_2line = 0x7f070091
com.dhass.myapp:dimen/m3_large_fab_max_image_size = 0x7f070118
com.dhass.myapp:string/catalyst_debug_error = 0x7f12002b
com.dhass.myapp:color/exo_black_opacity_60 = 0x7f06006f
com.dhass.myapp:attr/lStar = 0x7f040254
com.dhass.myapp:attr/counterOverflowTextColor = 0x7f040133
com.dhass.myapp:anim/rns_fade_from_bottom = 0x7f01002a
com.dhass.myapp:attr/layout_collapseMode = 0x7f040265
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f130038
com.dhass.myapp:color/m3_ref_palette_neutral_variant100 = 0x7f06010a
com.dhass.myapp:color/m3_ref_palette_dynamic_primary80 = 0x7f0600d0
com.dhass.myapp:id/exo_extra_controls_scroll_view = 0x7f0a00e7
com.dhass.myapp:dimen/abc_text_size_body_1_material = 0x7f07003f
com.dhass.myapp:color/m3_sys_color_light_inverse_surface = 0x7f060190
com.dhass.myapp:attr/cropFixAspectRatio = 0x7f040143
com.dhass.myapp:attr/keep_content_on_player_reset = 0x7f040250
com.dhass.myapp:attr/itemTextColor = 0x7f04024e
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar = 0x7f13042d
com.dhass.myapp:attr/actualImageResource = 0x7f040025
com.dhass.myapp:attr/itemTextAppearanceInactive = 0x7f04024d
com.dhass.myapp:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f130052
com.dhass.myapp:drawable/ic_fullscreen_exit_32dp = 0x7f0800eb
com.dhass.myapp:style/TextAppearance.Design.Error = 0x7f1301f7
com.dhass.myapp:attr/listMenuViewStyle = 0x7f0402ac
com.dhass.myapp:id/action_bar = 0x7f0a003e
com.dhass.myapp:attr/colorSecondaryContainer = 0x7f040102
com.dhass.myapp:id/role = 0x7f0a01e3
com.dhass.myapp:attr/layout_constraintStart_toStartOf = 0x7f040285
com.dhass.myapp:layout/notification_template_big_media = 0x7f0d0079
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0600ec
com.dhass.myapp:drawable/splashscreen_logo = 0x7f080141
com.dhass.myapp:attr/cropGuidelinesThickness = 0x7f040148
com.dhass.myapp:color/common_google_signin_btn_text_light_focused = 0x7f060040
com.dhass.myapp:drawable/abc_spinner_textfield_background_material = 0x7f080044
com.dhass.myapp:anim/rns_standard_accelerate_interpolator = 0x7f010040
com.dhass.myapp:attr/itemTextAppearance = 0x7f04024b
com.dhass.myapp:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.dhass.myapp:attr/floatingActionButtonPrimaryStyle = 0x7f0401d6
com.dhass.myapp:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1302e3
com.dhass.myapp:attr/flow_wrapMode = 0x7f0401ed
com.dhass.myapp:id/dragLeft = 0x7f0a00c2
com.dhass.myapp:attr/fontWeight = 0x7f0401f9
com.dhass.myapp:attr/itemStrokeWidth = 0x7f04024a
com.dhass.myapp:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07018c
com.dhass.myapp:style/Widget.AppCompat.Button.Small = 0x7f130323
com.dhass.myapp:id/accessibility_custom_action_18 = 0x7f0a0020
com.dhass.myapp:attr/itemShapeInsetStart = 0x7f040246
com.dhass.myapp:plurals/exo_controls_fastforward_by_amount_description = 0x7f100000
com.dhass.myapp:attr/itemShapeAppearanceOverlay = 0x7f040242
com.dhass.myapp:attr/itemShapeAppearance = 0x7f040241
com.dhass.myapp:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1300f3
com.dhass.myapp:attr/cornerSize = 0x7f04012b
com.dhass.myapp:color/m3_ref_palette_primary60 = 0x7f06011c
com.dhass.myapp:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f130425
com.dhass.myapp:style/Widget.Material3.ActionMode = 0x7f130370
com.dhass.myapp:dimen/splashscreen_icon_size_with_background = 0x7f070263
com.dhass.myapp:attr/itemPaddingBottom = 0x7f04023e
com.dhass.myapp:styleable/ThemeEnforcement = 0x7f140093
com.dhass.myapp:layout/mtrl_calendar_vertical = 0x7f0d0065
com.dhass.myapp:attr/itemMinHeight = 0x7f04023c
com.dhass.myapp:style/Widget.AppCompat.TextView = 0x7f130358
com.dhass.myapp:attr/roundBottomLeft = 0x7f040374
com.dhass.myapp:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700e1
com.dhass.myapp:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f13020e
com.dhass.myapp:attr/itemHorizontalPadding = 0x7f040236
com.dhass.myapp:dimen/m3_navigation_item_shape_inset_end = 0x7f07011f
com.dhass.myapp:attr/itemFillColor = 0x7f040235
com.dhass.myapp:style/TextAppearance.Material3.BodyLarge = 0x7f130210
com.dhass.myapp:anim/rns_ios_from_right_foreground_open = 0x7f010035
com.dhass.myapp:attr/endIconTintMode = 0x7f0401a3
com.dhass.myapp:attr/numericModifiers = 0x7f040323
com.dhass.myapp:attr/expanded = 0x7f0401b0
com.dhass.myapp:attr/isMaterial3Theme = 0x7f040231
com.dhass.myapp:dimen/design_fab_translation_z_pressed = 0x7f070079
com.dhass.myapp:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f06018a
com.dhass.myapp:style/ThemeOverlayColorAccentRed = 0x7f13030e
com.dhass.myapp:attr/retryImageScaleType = 0x7f04036e
com.dhass.myapp:dimen/m3_btn_dialog_btn_spacing = 0x7f0700e6
com.dhass.myapp:attr/isLightTheme = 0x7f040230
com.dhass.myapp:string/common_signin_button_text = 0x7f12005a
com.dhass.myapp:color/m3_chip_ripple_color = 0x7f06008b
com.dhass.myapp:attr/logoScaleType = 0x7f0402b9
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f130300
com.dhass.myapp:attr/itemMaxLines = 0x7f04023b
com.dhass.myapp:attr/backgroundColor = 0x7f04004b
com.dhass.myapp:id/CropOverlayView = 0x7f0a0004
com.dhass.myapp:dimen/m3_btn_icon_btn_padding_left = 0x7f0700eb
com.dhass.myapp:attr/indicatorColor = 0x7f040228
com.dhass.myapp:dimen/mtrl_card_spacing = 0x7f0701e6
com.dhass.myapp:attr/fabCradleRoundedCornerRadius = 0x7f0401c4
com.dhass.myapp:attr/indeterminateProgressStyle = 0x7f040227
com.dhass.myapp:id/SHOW_PATH = 0x7f0a000c
com.dhass.myapp:dimen/design_snackbar_elevation = 0x7f070086
com.dhass.myapp:color/mtrl_on_surface_ripple_color = 0x7f060245
com.dhass.myapp:attr/subheaderColor = 0x7f0403d1
com.dhass.myapp:attr/iconifiedByDefault = 0x7f040221
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1303a9
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f060167
com.dhass.myapp:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402a7
com.dhass.myapp:dimen/mtrl_navigation_rail_compact_width = 0x7f070214
com.dhass.myapp:attr/flow_padding = 0x7f0401e8
com.dhass.myapp:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f070126
com.dhass.myapp:xml/sharing_provider_paths = 0x7f150007
com.dhass.myapp:attr/labelVisibilityMode = 0x7f040257
com.dhass.myapp:styleable/FlowLayout = 0x7f140037
com.dhass.myapp:attr/cropMaxCropResultWidthPX = 0x7f04014b
com.dhass.myapp:attr/actionModeCloseButtonStyle = 0x7f040011
com.dhass.myapp:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.dhass.myapp:style/Widget.Material3.BottomSheet = 0x7f13037a
com.dhass.myapp:attr/iconPadding = 0x7f04021c
com.dhass.myapp:style/Theme.Material3.Dark.Dialog = 0x7f13025e
com.dhass.myapp:color/m3_timepicker_button_text_color = 0x7f0601b1
com.dhass.myapp:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f13040e
com.dhass.myapp:attr/actionBarDivider = 0x7f040000
com.dhass.myapp:attr/layout_constraintWidth_percent = 0x7f040290
com.dhass.myapp:drawable/abc_btn_radio_material_anim = 0x7f080010
com.dhass.myapp:attr/buttonStyle = 0x7f04008b
com.dhass.myapp:attr/backgroundSplit = 0x7f040052
com.dhass.myapp:attr/popupMenuStyle = 0x7f04034f
com.dhass.myapp:attr/hintEnabled = 0x7f040211
com.dhass.myapp:attr/dropDownListViewStyle = 0x7f040191
com.dhass.myapp:attr/tabIndicatorAnimationMode = 0x7f0403ec
com.dhass.myapp:drawable/common_google_signin_btn_text_dark_focused = 0x7f080081
com.dhass.myapp:color/colorPrimaryDark = 0x7f060037
com.dhass.myapp:style/Theme.MaterialComponents.Light.Dialog = 0x7f130297
com.dhass.myapp:attr/collapsedSize = 0x7f0400d8
com.dhass.myapp:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f130047
com.dhass.myapp:drawable/mtrl_dropdown_arrow = 0x7f08010d
com.dhass.myapp:style/ExoStyledControls.TimeText.Position = 0x7f13013f
com.dhass.myapp:attr/windowSplashScreenBackground = 0x7f0404a2
com.dhass.myapp:attr/materialCalendarYearNavigationButton = 0x7f0402de
com.dhass.myapp:dimen/hint_pressed_alpha_material_dark = 0x7f0700c0
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1301a0
com.dhass.myapp:layout/design_bottom_navigation_item = 0x7f0d0025
com.dhass.myapp:color/wallet_hint_foreground_holo_dark = 0x7f06027c
com.dhass.myapp:attr/hideAnimationBehavior = 0x7f04020a
com.dhass.myapp:attr/helperTextTextColor = 0x7f040209
com.dhass.myapp:dimen/notification_large_icon_height = 0x7f070252
com.dhass.myapp:attr/altSrc = 0x7f040031
com.dhass.myapp:color/design_dark_default_color_primary = 0x7f06004c
com.dhass.myapp:attr/foregroundInsidePadding = 0x7f0401fb
com.dhass.myapp:attr/autofillInlineSuggestionStartIconStyle = 0x7f040047
com.dhass.myapp:attr/checkedChip = 0x7f0400a9
com.dhass.myapp:attr/goIcon = 0x7f040201
com.dhass.myapp:id/center_vertical = 0x7f0a0086
com.dhass.myapp:dimen/abc_config_prefDialogWidth = 0x7f070017
com.dhass.myapp:integer/material_motion_duration_long_1 = 0x7f0b0023
com.dhass.myapp:color/wallet_dim_foreground_holo_dark = 0x7f060279
com.dhass.myapp:anim/mtrl_bottom_sheet_slide_out = 0x7f010024
com.dhass.myapp:attr/checkedIconTint = 0x7f0400af
com.dhass.myapp:integer/design_tab_indicator_anim_duration_ms = 0x7f0b0008
com.dhass.myapp:attr/framePosition = 0x7f0401fe
com.dhass.myapp:styleable/CustomAttribute = 0x7f14002e
com.dhass.myapp:attr/flow_verticalGap = 0x7f0401eb
com.dhass.myapp:attr/fragmentStyle = 0x7f0401fd
com.dhass.myapp:styleable/FragmentContainerView = 0x7f14003c
com.dhass.myapp:color/primary_material_light = 0x7f06025b
com.dhass.myapp:style/amu_ClusterIcon.TextAppearance = 0x7f130477
com.dhass.myapp:string/abc_menu_function_shortcut_label = 0x7f12000c
com.dhass.myapp:attr/textAppearanceBody2 = 0x7f040407
com.dhass.myapp:attr/layout_constraintCircleRadius = 0x7f040270
com.dhass.myapp:attr/listPreferredItemPaddingEnd = 0x7f0402b1
com.dhass.myapp:id/parentRelative = 0x7f0a01ba
com.dhass.myapp:attr/badgeStyle = 0x7f040058
com.dhass.myapp:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401bb
com.dhass.myapp:attr/fabSize = 0x7f0401c7
com.dhass.myapp:color/m3_ref_palette_neutral95 = 0x7f060106
com.dhass.myapp:style/TextAppearance.AppCompat.Medium = 0x7f1301cb
com.dhass.myapp:layout/dev_loading_view = 0x7f0d0034
com.dhass.myapp:color/material_dynamic_neutral99 = 0x7f0601cd
com.dhass.myapp:style/Base.Widget.AppCompat.Button.Colored = 0x7f1300c7
com.dhass.myapp:id/rectangles = 0x7f0a01d2
com.dhass.myapp:attr/switchTextAppearance = 0x7f0403e4
com.dhass.myapp:array/exo_controls_playback_speeds = 0x7f030000
com.dhass.myapp:color/design_dark_default_color_secondary_variant = 0x7f060050
com.dhass.myapp:attr/staggered = 0x7f0403bf
com.dhass.myapp:attr/ensureMinTouchTargetSize = 0x7f0401a6
com.dhass.myapp:attr/layout_constraintCircle = 0x7f04026e
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0600e8
com.dhass.myapp:attr/cardViewStyle = 0x7f0400a1
com.dhass.myapp:attr/fontProviderAuthority = 0x7f0401f0
com.dhass.myapp:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f13010a
com.dhass.myapp:color/m3_ref_palette_tertiary99 = 0x7f06013b
com.dhass.myapp:attr/values = 0x7f040489
com.dhass.myapp:attr/cropMinCropWindowWidth = 0x7f040150
com.dhass.myapp:id/rn_redbox_line_separator = 0x7f0a01dd
com.dhass.myapp:attr/itemStrokeColor = 0x7f040249
com.dhass.myapp:style/TextAppearance.MaterialComponents.Badge = 0x7f130220
com.dhass.myapp:attr/fontFamily = 0x7f0401ef
com.dhass.myapp:id/material_timepicker_view = 0x7f0a0178
com.dhass.myapp:color/material_dynamic_primary0 = 0x7f0601db
com.dhass.myapp:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f120101
com.dhass.myapp:attr/dragThreshold = 0x7f040183
com.dhass.myapp:color/background_material_light = 0x7f060020
com.dhass.myapp:style/Widget.Material3.CompoundButton.Switch = 0x7f1303a4
com.dhass.myapp:string/catalyst_perf_monitor_stop = 0x7f12003a
com.dhass.myapp:attr/colorContainer = 0x7f0400e5
com.dhass.myapp:id/clockwise = 0x7f0a0097
com.dhass.myapp:color/m3_sys_color_light_surface = 0x7f0601a1
com.dhass.myapp:attr/buttonTintMode = 0x7f04008e
com.dhass.myapp:id/NO_DEBUG = 0x7f0a0009
com.dhass.myapp:attr/searchIcon = 0x7f04038d
com.dhass.myapp:attr/collapsingToolbarLayoutStyle = 0x7f0400e0
com.dhass.myapp:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1300b5
com.dhass.myapp:attr/layout_constraintWidth_max = 0x7f04028e
com.dhass.myapp:style/TextAppearance.Material3.TitleSmall = 0x7f13021f
com.dhass.myapp:color/design_snackbar_background_color = 0x7f060068
com.dhass.myapp:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1303ab
com.dhass.myapp:attr/materialButtonStyle = 0x7f0402ce
com.dhass.myapp:id/material_minute_text_input = 0x7f0a0170
com.dhass.myapp:dimen/m3_extended_fab_end_padding = 0x7f07010f
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f07013a
com.dhass.myapp:attr/flow_maxElementsWrap = 0x7f0401e7
com.dhass.myapp:attr/flow_lastHorizontalBias = 0x7f0401e3
com.dhass.myapp:attr/itemActiveIndicatorStyle = 0x7f040233
com.dhass.myapp:dimen/material_clock_period_toggle_margin_left = 0x7f07016d
com.dhass.myapp:style/ExoMediaButton.Pause = 0x7f130124
com.dhass.myapp:attr/checkedButton = 0x7f0400a8
com.dhass.myapp:attr/roundTopStart = 0x7f04037b
com.dhass.myapp:attr/flow_lastVerticalBias = 0x7f0401e5
com.dhass.myapp:attr/tooltipFrameBackground = 0x7f040461
com.dhass.myapp:attr/flow_horizontalStyle = 0x7f0401e2
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f13042e
com.dhass.myapp:attr/expandedTitleMarginBottom = 0x7f0401b4
com.dhass.myapp:color/material_on_background_emphasis_medium = 0x7f06020f
com.dhass.myapp:attr/actionBarStyle = 0x7f040005
com.dhass.myapp:attr/flow_firstVerticalStyle = 0x7f0401de
com.dhass.myapp:attr/flow_firstHorizontalStyle = 0x7f0401dc
com.dhass.myapp:id/ll_loader = 0x7f0a0162
com.dhass.myapp:attr/thumbElevation = 0x7f04043c
com.dhass.myapp:color/design_default_color_on_surface = 0x7f060058
com.dhass.myapp:dimen/exo_icon_padding_bottom = 0x7f07009c
com.dhass.myapp:attr/horizontalOffsetWithText = 0x7f040217
com.dhass.myapp:layout/test_toolbar_surface = 0x7f0d009b
com.dhass.myapp:attr/colorAccent = 0x7f0400e2
com.dhass.myapp:attr/boxStrokeWidth = 0x7f04007d
com.dhass.myapp:id/standard = 0x7f0a021e
com.dhass.myapp:attr/fontProviderQuery = 0x7f0401f5
com.dhass.myapp:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401d1
com.dhass.myapp:attr/hide_during_ads = 0x7f04020e
com.dhass.myapp:color/vector_tint_theme_color = 0x7f060274
com.dhass.myapp:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f130458
com.dhass.myapp:anim/rns_fade_to_bottom = 0x7f01002d
com.dhass.myapp:attr/textAppearanceLabelLarge = 0x7f040419
com.dhass.myapp:attr/fastScrollHorizontalTrackDrawable = 0x7f0401cd
com.dhass.myapp:attr/fastScrollHorizontalThumbDrawable = 0x7f0401cc
com.dhass.myapp:attr/failureImageScaleType = 0x7f0401ca
com.dhass.myapp:attr/failureImage = 0x7f0401c9
com.dhass.myapp:id/accelerate = 0x7f0a0011
com.dhass.myapp:attr/show_buffering = 0x7f0403a2
com.dhass.myapp:anim/rns_slide_out_to_right = 0x7f01003f
com.dhass.myapp:dimen/m3_card_stroke_width = 0x7f070104
com.dhass.myapp:color/test_mtrl_calendar_day_selected = 0x7f060270
com.dhass.myapp:id/status_bar_latest_event_content = 0x7f0a0225
com.dhass.myapp:attr/fadeDuration = 0x7f0401c8
com.dhass.myapp:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401be
com.dhass.myapp:id/parallax = 0x7f0a01b7
com.dhass.myapp:dimen/mtrl_progress_circular_inset_extra_small = 0x7f07021d
com.dhass.myapp:styleable/KeyFramesVelocity = 0x7f140046
com.dhass.myapp:style/Theme.AppCompat.Light.Dialog = 0x7f130249
com.dhass.myapp:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.dhass.myapp:attr/actionBarTheme = 0x7f040009
com.dhass.myapp:attr/titleCentered = 0x7f04044e
com.dhass.myapp:style/TextAppearance.AppCompat.Display2 = 0x7f1301c0
com.dhass.myapp:dimen/test_navigation_bar_item_max_width = 0x7f07026c
com.dhass.myapp:attr/extendedFloatingActionButtonStyle = 0x7f0401bd
com.dhass.myapp:id/check_list = 0x7f0a0089
com.dhass.myapp:attr/expandedTitleTextAppearance = 0x7f0401b8
com.dhass.myapp:attr/expandedTitleMarginTop = 0x7f0401b7
com.dhass.myapp:dimen/mtrl_progress_circular_inset_medium = 0x7f07021e
com.dhass.myapp:anim/rns_no_animation_350 = 0x7f010038
com.dhass.myapp:styleable/MaterialAutoCompleteTextView = 0x7f140053
com.dhass.myapp:color/common_google_signin_btn_text_dark_disabled = 0x7f06003a
com.dhass.myapp:attr/expandedTitleMarginEnd = 0x7f0401b5
com.dhass.myapp:dimen/compat_control_corner_material = 0x7f07005e
com.dhass.myapp:attr/colorBackgroundFloating = 0x7f0400e3
com.dhass.myapp:style/ShapeAppearanceOverlay.TopLeftCut = 0x7f1301a7
com.dhass.myapp:attr/colorOnError = 0x7f0400ed
com.dhass.myapp:id/split_action_bar = 0x7f0a0217
com.dhass.myapp:attr/expandedHintEnabled = 0x7f0401b1
com.dhass.myapp:attr/bottomSheetDialogTheme = 0x7f040072
com.dhass.myapp:attr/errorTextAppearance = 0x7f0401ad
com.dhass.myapp:color/m3_dynamic_default_color_secondary_text = 0x7f06009b
com.dhass.myapp:attr/errorEnabled = 0x7f0401a9
com.dhass.myapp:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0701cf
com.dhass.myapp:style/MaterialAlertDialog.Material3.Body.Text = 0x7f130144
com.dhass.myapp:attr/simpleItems = 0x7f0403ae
com.dhass.myapp:attr/fragmentMode = 0x7f0401fc
com.dhass.myapp:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f070197
com.dhass.myapp:id/toggle = 0x7f0a0256
com.dhass.myapp:attr/layout_goneMarginStart = 0x7f040298
com.dhass.myapp:attr/boxStrokeErrorColor = 0x7f04007c
com.dhass.myapp:dimen/abc_switch_padding = 0x7f07003e
com.dhass.myapp:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1302ba
com.dhass.myapp:attr/saturation = 0x7f040381
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f07015c
com.dhass.myapp:attr/helperText = 0x7f040206
com.dhass.myapp:attr/showTitle = 0x7f0403a1
com.dhass.myapp:attr/bottomAppBarStyle = 0x7f04006f
com.dhass.myapp:color/primary_text_disabled_material_light = 0x7f06025f
com.dhass.myapp:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1302c6
com.dhass.myapp:attr/buyButtonWidth = 0x7f040092
com.dhass.myapp:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f13028e
com.dhass.myapp:color/mtrl_navigation_bar_colored_item_tint = 0x7f06023d
com.dhass.myapp:string/common_google_play_services_updating_text = 0x7f120057
com.dhass.myapp:attr/editTextStyle = 0x7f040197
com.dhass.myapp:color/material_grey_800 = 0x7f060206
com.dhass.myapp:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0d005b
com.dhass.myapp:attr/itemIconPadding = 0x7f040238
com.dhass.myapp:id/exo_ad_overlay = 0x7f0a00d8
com.dhass.myapp:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1303dd
com.dhass.myapp:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f130105
com.dhass.myapp:attr/behavior_expandedOffset = 0x7f040065
com.dhass.myapp:attr/editTextBackground = 0x7f040195
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070140
com.dhass.myapp:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1303ed
com.dhass.myapp:attr/dynamicColorThemeOverlay = 0x7f040194
com.dhass.myapp:attr/flow_firstVerticalBias = 0x7f0401dd
com.dhass.myapp:string/mtrl_picker_navigate_to_year_description = 0x7f1200f3
com.dhass.myapp:attr/duration = 0x7f040193
com.dhass.myapp:attr/chipIcon = 0x7f0400b6
com.dhass.myapp:dimen/test_navigation_bar_item_min_width = 0x7f07026d
com.dhass.myapp:string/m3_ref_typeface_plain_medium = 0x7f1200be
com.dhass.myapp:attr/dropdownListPreferredItemHeight = 0x7f040192
com.dhass.myapp:id/scrollable = 0x7f0a01f5
com.dhass.myapp:dimen/abc_action_bar_elevation_material = 0x7f070005
com.dhass.myapp:attr/layout_constraintHeight_max = 0x7f040278
com.dhass.myapp:attr/imageAspectRatioAdjust = 0x7f040223
com.dhass.myapp:attr/materialCircleRadius = 0x7f0402e3
com.dhass.myapp:style/Widget.Material3.MaterialDivider = 0x7f1303ce
com.dhass.myapp:attr/boxStrokeColor = 0x7f04007b
com.dhass.myapp:attr/auto_show = 0x7f040044
com.dhass.myapp:attr/circleRadius = 0x7f0400c7
com.dhass.myapp:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f130337
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f130206
com.dhass.myapp:color/primary_text_disabled_material_dark = 0x7f06025e
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f130041
com.dhass.myapp:attr/buttonTint = 0x7f04008d
com.dhass.myapp:string/material_minute_selection = 0x7f1200cf
com.dhass.myapp:attr/mock_label = 0x7f0402fe
com.dhass.myapp:attr/colorPrimaryInverse = 0x7f0400fd
com.dhass.myapp:attr/actionLayout = 0x7f04000d
com.dhass.myapp:attr/layout_constraintVertical_weight = 0x7f04028c
com.dhass.myapp:style/Widget.Material3.BottomAppBar = 0x7f130377
com.dhass.myapp:color/material_dynamic_neutral_variant70 = 0x7f0601d6
com.dhass.myapp:attr/drawableTintMode = 0x7f04018c
com.dhass.myapp:attr/counterOverflowTextAppearance = 0x7f040132
com.dhass.myapp:attr/tint = 0x7f04044b
com.dhass.myapp:attr/drawableLeftCompat = 0x7f040187
com.dhass.myapp:attr/drawableEndCompat = 0x7f040186
com.dhass.myapp:integer/abc_config_activityShortDur = 0x7f0b0001
com.dhass.myapp:attr/checkedIconVisible = 0x7f0400b0
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f13003a
com.dhass.myapp:attr/telltales_tailColor = 0x7f040402
com.dhass.myapp:drawable/abc_list_divider_material = 0x7f08002a
com.dhass.myapp:style/Widget.AppCompat.ListView = 0x7f130345
com.dhass.myapp:layout/mtrl_calendar_horizontal = 0x7f0d0060
com.dhass.myapp:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.dhass.myapp:attr/drawPath = 0x7f040184
com.dhass.myapp:style/TextAppearance.Design.Hint = 0x7f1301f9
com.dhass.myapp:attr/dragDirection = 0x7f040181
com.dhass.myapp:dimen/hint_pressed_alpha_material_light = 0x7f0700c1
com.dhass.myapp:attr/fastScrollVerticalThumbDrawable = 0x7f0401ce
com.dhass.myapp:id/icon = 0x7f0a0145
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0600e5
com.dhass.myapp:attr/cornerFamily = 0x7f040124
com.dhass.myapp:id/aligned = 0x7f0a0055
com.dhass.myapp:attr/passwordToggleTintMode = 0x7f04033c
com.dhass.myapp:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.dhass.myapp:attr/actionModeWebSearchDrawable = 0x7f04001e
com.dhass.myapp:attr/default_artwork = 0x7f040171
com.dhass.myapp:attr/defaultState = 0x7f040170
com.dhass.myapp:attr/buttonStyleSmall = 0x7f04008c
com.dhass.myapp:dimen/mtrl_fab_translation_z_pressed = 0x7f070200
com.dhass.myapp:color/material_dynamic_neutral30 = 0x7f0601c5
com.dhass.myapp:attr/defaultQueryHint = 0x7f04016f
com.dhass.myapp:attr/homeLayout = 0x7f040215
com.dhass.myapp:drawable/exo_notification_previous = 0x7f0800c4
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1302ed
com.dhass.myapp:attr/defaultDuration = 0x7f04016e
com.dhass.myapp:attr/dayStyle = 0x7f04016c
com.dhass.myapp:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f0403e0
com.dhass.myapp:dimen/m3_btn_disabled_elevation = 0x7f0700e7
com.dhass.myapp:attr/daySelectedStyle = 0x7f04016b
com.dhass.myapp:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1300cb
com.dhass.myapp:color/secondary_text_default_material_dark = 0x7f060263
com.dhass.myapp:drawable/m3_tabs_line_indicator = 0x7f0800ff
com.dhass.myapp:attr/dayInvalidStyle = 0x7f04016a
com.dhass.myapp:attr/titleEnabled = 0x7f040450
com.dhass.myapp:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f130330
com.dhass.myapp:attr/elevationOverlayColor = 0x7f04019a
com.dhass.myapp:drawable/abc_text_cursor_material = 0x7f08004b
com.dhass.myapp:attr/reverseLayout = 0x7f04036f
com.dhass.myapp:attr/customPixelDimension = 0x7f040167
com.dhass.myapp:attr/placeholderTextAppearance = 0x7f040348
com.dhass.myapp:attr/actionModeBackground = 0x7f040010
com.dhass.myapp:attr/customIntegerValue = 0x7f040165
com.dhass.myapp:layout/abc_select_dialog_material = 0x7f0d001a
com.dhass.myapp:id/dropdown_menu = 0x7f0a00c7
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Caption = 0x7f13001a
com.dhass.myapp:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0701f4
com.dhass.myapp:attr/drawerLayoutStyle = 0x7f040190
com.dhass.myapp:attr/customColorDrawableValue = 0x7f040161
com.dhass.myapp:attr/customBoolean = 0x7f040160
com.dhass.myapp:attr/textAppearanceBodySmall = 0x7f04040a
com.dhass.myapp:id/disableHome = 0x7f0a00ba
com.dhass.myapp:string/pick_image_chooser_title = 0x7f12010e
com.dhass.myapp:drawable/btn_checkbox_unchecked_mtrl = 0x7f080070
com.dhass.myapp:attr/cropperLabelTextColor = 0x7f04015b
com.dhass.myapp:layout/text_view_with_theme_line_height = 0x7f0d009f
com.dhass.myapp:color/material_dynamic_neutral_variant60 = 0x7f0601d5
com.dhass.myapp:attr/behavior_peekHeight = 0x7f04006a
com.dhass.myapp:attr/closeIconVisible = 0x7f0400d4
com.dhass.myapp:string/exo_track_role_alternate = 0x7f12008e
com.dhass.myapp:id/showHome = 0x7f0a0207
com.dhass.myapp:attr/cropTouchRadius = 0x7f040159
com.dhass.myapp:attr/controlBackground = 0x7f040121
com.dhass.myapp:color/bright_foreground_disabled_material_light = 0x7f060022
com.dhass.myapp:style/Theme.FullScreenDialog = 0x7f130259
com.dhass.myapp:drawable/exo_notification_stop = 0x7f0800c7
com.dhass.myapp:dimen/mtrl_calendar_day_corner = 0x7f0701bc
com.dhass.myapp:style/Widget.MaterialComponents.BottomSheet = 0x7f13040a
com.dhass.myapp:attr/cornerSizeTopLeft = 0x7f04012e
com.dhass.myapp:anim/rns_default_enter_in = 0x7f010026
com.dhass.myapp:id/google_wallet_monochrome = 0x7f0a012e
com.dhass.myapp:drawable/abc_list_pressed_holo_dark = 0x7f08002e
com.dhass.myapp:drawable/exo_styled_controls_repeat_all = 0x7f0800d4
com.dhass.myapp:attr/cropShowCropOverlay = 0x7f040155
com.dhass.myapp:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f1300fc
com.dhass.myapp:layout/material_clockface_textview = 0x7f0d004d
com.dhass.myapp:attr/yearSelectedStyle = 0x7f0404a5
com.dhass.myapp:attr/cropMinCropResultHeightPX = 0x7f04014d
com.dhass.myapp:attr/lineSpacing = 0x7f0402a4
com.dhass.myapp:attr/colorButtonNormal = 0x7f0400e4
com.dhass.myapp:id/material_timepicker_container = 0x7f0a0174
com.dhass.myapp:attr/layout_constraintBottom_toTopOf = 0x7f04026d
com.dhass.myapp:attr/checkMarkTint = 0x7f0400a5
com.dhass.myapp:id/withText = 0x7f0a027a
com.dhass.myapp:attr/flow_horizontalGap = 0x7f0401e1
com.dhass.myapp:id/sin = 0x7f0a0209
com.dhass.myapp:attr/materialCalendarDayOfWeekLabel = 0x7f0402d1
com.dhass.myapp:attr/layout_constraintEnd_toStartOf = 0x7f040273
com.dhass.myapp:dimen/design_fab_translation_z_hovered_focused = 0x7f070078
com.dhass.myapp:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f060160
com.dhass.myapp:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.dhass.myapp:attr/actionProviderClass = 0x7f040021
com.dhass.myapp:style/Widget.AppCompat.ActionBar.Solid = 0x7f130314
com.dhass.myapp:id/clip_horizontal = 0x7f0a0095
com.dhass.myapp:dimen/mtrl_btn_text_size = 0x7f0701b5
com.dhass.myapp:attr/navigationContentDescription = 0x7f040319
com.dhass.myapp:attr/textAppearanceLineHeightEnabled = 0x7f04041d
com.dhass.myapp:drawable/abc_tab_indicator_material = 0x7f080049
com.dhass.myapp:string/exo_download_downloading = 0x7f120083
com.dhass.myapp:attr/cropGuidelinesColor = 0x7f040147
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f13016d
com.dhass.myapp:id/fitXY = 0x7f0a011b
com.dhass.myapp:style/Base.V23.Theme.AppCompat.Light = 0x7f1300a6
com.dhass.myapp:attr/alertDialogCenterButtons = 0x7f04002b
com.dhass.myapp:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.dhass.myapp:drawable/exo_ic_audiotrack = 0x7f08009f
com.dhass.myapp:color/m3_sys_color_dynamic_light_surface_variant = 0x7f060188
com.dhass.myapp:attr/actionBarSize = 0x7f040003
com.dhass.myapp:attr/cropCenterMoveEnabled = 0x7f040140
com.dhass.myapp:dimen/test_navigation_bar_label_padding = 0x7f07026e
com.dhass.myapp:attr/cropBorderLineThickness = 0x7f04013f
com.dhass.myapp:anim/design_bottom_sheet_slide_out = 0x7f01001f
com.dhass.myapp:attr/cropBorderLineColor = 0x7f04013e
com.dhass.myapp:style/TextAppearance.AppCompat.Display4 = 0x7f1301c2
com.dhass.myapp:drawable/exo_notification_small_icon = 0x7f0800c6
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f130279
com.dhass.myapp:attr/percentX = 0x7f040341
com.dhass.myapp:dimen/exo_icon_text_size = 0x7f07009e
com.dhass.myapp:attr/tabMaxWidth = 0x7f0403f2
com.dhass.myapp:attr/cropBorderCornerOffset = 0x7f04013c
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f130174
com.dhass.myapp:attr/layout_constraintBottom_creator = 0x7f04026b
com.dhass.myapp:dimen/mtrl_calendar_content_padding = 0x7f0701bb
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f130438
com.dhass.myapp:id/fitBottomStart = 0x7f0a0116
com.dhass.myapp:attr/cropBorderCornerColor = 0x7f04013a
com.dhass.myapp:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1303cf
com.dhass.myapp:id/action_mode_bar_stub = 0x7f0a004c
com.dhass.myapp:styleable/CollapsingToolbarLayout = 0x7f140023
com.dhass.myapp:attr/cropBackgroundColor = 0x7f040139
com.dhass.myapp:attr/counterEnabled = 0x7f040130
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f130443
com.dhass.myapp:color/m3_ref_palette_primary95 = 0x7f060120
com.dhass.myapp:attr/height = 0x7f040205
com.dhass.myapp:attr/layout_constraintLeft_toLeftOf = 0x7f04027f
com.dhass.myapp:dimen/mtrl_extended_fab_end_padding = 0x7f0701f1
com.dhass.myapp:attr/cornerSizeBottomLeft = 0x7f04012c
com.dhass.myapp:color/dim_foreground_disabled_material_light = 0x7f06006a
com.dhass.myapp:styleable/AnimatedStateListDrawableItem = 0x7f140008
com.dhass.myapp:style/TextAppearance.AppCompat.Menu = 0x7f1301cd
com.dhass.myapp:dimen/design_bottom_navigation_text_size = 0x7f07006f
com.dhass.myapp:attr/thumbRadius = 0x7f04043d
com.dhass.myapp:color/dim_foreground_material_dark = 0x7f06006b
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f13030c
com.dhass.myapp:color/material_harmonized_color_error_container = 0x7f06020a
com.dhass.myapp:dimen/mtrl_btn_text_btn_padding_right = 0x7f0701b4
com.dhass.myapp:attr/cornerFamilyBottomRight = 0x7f040126
com.dhass.myapp:id/graph = 0x7f0a012f
com.dhass.myapp:attr/cropperLabelText = 0x7f04015a
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome6_regular = 0x7f11000b
com.dhass.myapp:attr/panelMenuListWidth = 0x7f040337
com.dhass.myapp:styleable/PlayerView = 0x7f140071
com.dhass.myapp:style/Theme.Design = 0x7f130253
com.dhass.myapp:attr/contentPaddingTop = 0x7f04011e
com.dhass.myapp:id/material_hour_text_input = 0x7f0a016d
com.dhass.myapp:id/exo_ffwd = 0x7f0a00e8
com.dhass.myapp:attr/scaleType = 0x7f040382
com.dhass.myapp:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700cc
com.dhass.myapp:attr/badgeWidePadding = 0x7f04005a
com.dhass.myapp:attr/contentPadding = 0x7f040118
com.dhass.myapp:dimen/exo_icon_size = 0x7f07009d
com.dhass.myapp:id/media_actions = 0x7f0a017b
com.dhass.myapp:attr/contentInsetStartWithNavigation = 0x7f040117
com.dhass.myapp:attr/centerIfNoTextEnabled = 0x7f0400a2
com.dhass.myapp:string/abc_capital_off = 0x7f120006
com.dhass.myapp:id/dragUp = 0x7f0a00c5
com.dhass.myapp:attr/forceApplySystemWindowInsetTop = 0x7f0401fa
com.dhass.myapp:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1303d6
com.dhass.myapp:id/tag_transition_group = 0x7f0a0236
com.dhass.myapp:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f07019a
com.dhass.myapp:attr/deriveConstraintsFrom = 0x7f040174
com.dhass.myapp:color/m3_popupmenu_overlay_color = 0x7f0600a9
com.dhass.myapp:attr/behavior_hideable = 0x7f040068
com.dhass.myapp:anim/rns_ios_from_right_background_open = 0x7f010033
com.dhass.myapp:dimen/exo_small_icon_horizontal_margin = 0x7f0700a9
com.dhass.myapp:color/design_fab_shadow_mid_color = 0x7f060061
com.dhass.myapp:id/hide_ime_id = 0x7f0a0137
com.dhass.myapp:color/common_google_signin_btn_text_dark = 0x7f060038
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Button = 0x7f130019
com.dhass.myapp:attr/actionBarTabTextStyle = 0x7f040008
com.dhass.myapp:attr/cropAspectRatioX = 0x7f040136
com.dhass.myapp:drawable/exo_ic_speed = 0x7f0800ad
com.dhass.myapp:attr/cropShowLabel = 0x7f040156
com.dhass.myapp:style/Widget.AppCompat.ListPopupWindow = 0x7f130344
com.dhass.myapp:attr/layout_constraintCircleAngle = 0x7f04026f
com.dhass.myapp:attr/contentDescription = 0x7f040111
com.dhass.myapp:color/m3_ref_palette_neutral70 = 0x7f060103
com.dhass.myapp:attr/actionModePasteDrawable = 0x7f040017
com.dhass.myapp:attr/mapType = 0x7f0402bb
com.dhass.myapp:dimen/mtrl_snackbar_padding_horizontal = 0x7f07023c
com.dhass.myapp:id/snapMargins = 0x7f0a0211
com.dhass.myapp:attr/colorTertiaryContainer = 0x7f040109
com.dhass.myapp:animator/fragment_fade_exit = 0x7f020006
com.dhass.myapp:style/ShapeAppearance.MaterialComponents = 0x7f13018e
com.dhass.myapp:attr/motionEasingDecelerated = 0x7f04030b
com.dhass.myapp:attr/colorSurfaceVariant = 0x7f040106
com.dhass.myapp:dimen/m3_sys_elevation_level2 = 0x7f070136
com.dhass.myapp:string/mtrl_picker_invalid_format_example = 0x7f1200f0
com.dhass.myapp:attr/colorSecondaryVariant = 0x7f040103
com.dhass.myapp:dimen/mtrl_progress_circular_radius = 0x7f070220
com.dhass.myapp:string/google_app_id = 0x7f1200a9
com.dhass.myapp:attr/cardPreventCornerOverlap = 0x7f04009f
com.dhass.myapp:color/design_error = 0x7f06005f
com.dhass.myapp:attr/colorSurfaceInverse = 0x7f040105
com.dhass.myapp:color/m3_button_outline_color_selector = 0x7f060081
com.dhass.myapp:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1302ca
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_more_horiz = 0x7f080063
com.dhass.myapp:attr/haloRadius = 0x7f040203
com.dhass.myapp:drawable/common_full_open_on_phone = 0x7f080076
com.dhass.myapp:style/Widget.Material3.PopupMenu = 0x7f1303dc
com.dhass.myapp:attr/constraintSetEnd = 0x7f04010c
com.dhass.myapp:attr/chipStyle = 0x7f0400c4
com.dhass.myapp:id/coordinator = 0x7f0a00a2
com.dhass.myapp:id/top = 0x7f0a0257
com.dhass.myapp:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_closeicon = 0x7f080122
com.dhass.myapp:color/material_dynamic_tertiary20 = 0x7f0601f8
com.dhass.myapp:animator/fragment_close_exit = 0x7f020004
com.dhass.myapp:id/exo_artwork = 0x7f0a00d9
com.dhass.myapp:styleable/AspectRatioFrameLayout = 0x7f140013
com.dhass.myapp:dimen/splashscreen_icon_mask_size_no_background = 0x7f07025d
com.dhass.myapp:style/Animation.AppCompat.Tooltip = 0x7f130005
com.dhass.myapp:string/common_google_play_services_notification_channel_name = 0x7f120050
com.dhass.myapp:attr/colorScheme = 0x7f040100
com.dhass.myapp:attr/colorPrimarySurface = 0x7f0400fe
com.dhass.myapp:attr/colorOutline = 0x7f0400f9
com.dhass.myapp:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f130153
com.dhass.myapp:dimen/mtrl_tooltip_minHeight = 0x7f07024a
com.dhass.myapp:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f060162
com.dhass.myapp:style/ThemeOverlay.AppCompat.Dialog = 0x7f1302b1
com.dhass.myapp:attr/cornerFamilyBottomLeft = 0x7f040125
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Headline = 0x7f13001f
com.dhass.myapp:attr/layout_constraintGuide_percent = 0x7f040276
com.dhass.myapp:attr/cropBorderCornerThickness = 0x7f04013d
com.dhass.myapp:id/right_side = 0x7f0a01d9
com.dhass.myapp:color/abc_decor_view_status_guard = 0x7f060005
com.dhass.myapp:attr/measureWithLargestChild = 0x7f0402f5
com.dhass.myapp:attr/tabIndicatorColor = 0x7f0403ed
com.dhass.myapp:color/material_dynamic_neutral60 = 0x7f0601c8
com.dhass.myapp:attr/radioButtonStyle = 0x7f040361
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f130167
com.dhass.myapp:attr/colorOnSecondaryContainer = 0x7f0400f3
com.dhass.myapp:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f13019c
com.dhass.myapp:attr/textColorSearchUrl = 0x7f04042c
com.dhass.myapp:id/collapseActionView = 0x7f0a0098
com.dhass.myapp:drawable/rzp_logo = 0x7f08013c
com.dhass.myapp:attr/surface_type = 0x7f0403df
com.dhass.myapp:id/checked = 0x7f0a008b
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.Button = 0x7f1301e0
com.dhass.myapp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f07013b
com.dhass.myapp:layout/abc_screen_toolbar = 0x7f0d0017
com.dhass.myapp:dimen/mtrl_navigation_rail_icon_margin = 0x7f070217
com.dhass.myapp:style/TextAppearance.MaterialComponents.Chip = 0x7f130225
com.dhass.myapp:attr/chipEndPadding = 0x7f0400b4
com.dhass.myapp:style/Platform.ThemeOverlay.AppCompat = 0x7f13015d
com.dhass.myapp:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.dhass.myapp:attr/endIconDrawable = 0x7f0401a0
com.dhass.myapp:color/m3_navigation_item_icon_tint = 0x7f0600a6
com.dhass.myapp:drawable/assets_profileimg_user = 0x7f08006a
com.dhass.myapp:attr/cropGuidelines = 0x7f040146
com.dhass.myapp:color/m3_dynamic_highlighted_text = 0x7f06009c
com.dhass.myapp:attr/tabSelectedTextColor = 0x7f0403fc
com.dhass.myapp:attr/trackColorActive = 0x7f04046b
com.dhass.myapp:string/catalyst_hot_reloading_auto_disable = 0x7f120033
com.dhass.myapp:anim/rns_ios_from_left_background_close = 0x7f01002e
com.dhass.myapp:color/abc_btn_colored_text_material = 0x7f060003
com.dhass.myapp:layout/material_timepicker = 0x7f0d0053
com.dhass.myapp:attr/roundTopRight = 0x7f04037a
com.dhass.myapp:style/Widget.Material3.Button.ElevatedButton = 0x7f13037d
com.dhass.myapp:attr/suffixTextColor = 0x7f0403dd
com.dhass.myapp:attr/onPositiveCross = 0x7f040327
com.dhass.myapp:id/exo_bottom_bar = 0x7f0a00dc
com.dhass.myapp:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f130063
com.dhass.myapp:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080013
com.dhass.myapp:string/catalyst_reload = 0x7f12003b
com.dhass.myapp:attr/actionBarItemBackground = 0x7f040001
com.dhass.myapp:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0600e2
com.dhass.myapp:style/Widget.Material3.Toolbar.Surface = 0x7f1303f5
com.dhass.myapp:style/Theme.MaterialComponents.Bridge = 0x7f130276
com.dhass.myapp:style/SpinnerTimePickerStyle = 0x7f1301ae
com.dhass.myapp:integer/m3_btn_anim_delay_ms = 0x7f0b000d
com.dhass.myapp:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f07025f
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Light = 0x7f1302f8
com.dhass.myapp:attr/iconStartPadding = 0x7f04021e
com.dhass.myapp:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13044c
com.dhass.myapp:attr/colorTertiary = 0x7f040108
com.dhass.myapp:color/design_dark_default_color_surface = 0x7f060051
com.dhass.myapp:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700e3
com.dhass.myapp:string/expo_splash_screen_status_bar_translucent = 0x7f12009e
com.dhass.myapp:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.dhass.myapp:layout/exo_styled_player_control_ffwd_button = 0x7f0d0038
com.dhass.myapp:attr/actionModeFindDrawable = 0x7f040016
com.dhass.myapp:dimen/design_snackbar_min_width = 0x7f070089
com.dhass.myapp:attr/colorErrorContainer = 0x7f0400ea
com.dhass.myapp:attr/colorControlHighlight = 0x7f0400e7
com.dhass.myapp:attr/buyButtonHeight = 0x7f040090
com.dhass.myapp:drawable/abc_cab_background_internal_bg = 0x7f080015
com.dhass.myapp:id/surface_view = 0x7f0a022b
com.dhass.myapp:attr/scrubber_dragged_size = 0x7f040389
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f13016e
com.dhass.myapp:attr/fabCradleVerticalOffset = 0x7f0401c5
com.dhass.myapp:styleable/MaterialCalendar = 0x7f140056
com.dhass.myapp:attr/colorSecondary = 0x7f040101
com.dhass.myapp:color/mtrl_outlined_icon_tint = 0x7f060246
com.dhass.myapp:color/m3_sys_color_dark_outline = 0x7f060156
com.dhass.myapp:attr/collapsedTitleTextColor = 0x7f0400db
com.dhass.myapp:attr/colorControlNormal = 0x7f0400e8
com.dhass.myapp:attr/collapseIcon = 0x7f0400d7
com.dhass.myapp:attr/closeIcon = 0x7f0400ce
com.dhass.myapp:dimen/notification_subtext_size = 0x7f07025a
com.dhass.myapp:integer/material_motion_duration_short_1 = 0x7f0b0027
com.dhass.myapp:attr/layout_constraintRight_toRightOf = 0x7f040283
com.dhass.myapp:attr/trackColor = 0x7f04046a
com.dhass.myapp:attr/closeIconEnabled = 0x7f0400cf
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f130033
com.dhass.myapp:id/default_activity_button = 0x7f0a00b0
com.dhass.myapp:attr/roundPercent = 0x7f040377
com.dhass.myapp:attr/dividerInsetEnd = 0x7f04017c
com.dhass.myapp:anim/abc_slide_in_top = 0x7f010007
com.dhass.myapp:attr/tabPaddingBottom = 0x7f0403f6
com.dhass.myapp:drawable/material_ic_clear_black_24dp = 0x7f080104
com.dhass.myapp:integer/mtrl_btn_anim_duration_ms = 0x7f0b002c
com.dhass.myapp:attr/cameraZoom = 0x7f040099
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f07014d
com.dhass.myapp:color/m3_slider_thumb_color = 0x7f060142
com.dhass.myapp:style/Base.Widget.AppCompat.TextView = 0x7f1300ee
com.dhass.myapp:id/wide = 0x7f0a0278
com.dhass.myapp:attr/latLngBoundsSouthWestLatitude = 0x7f04025c
com.dhass.myapp:style/TextAppearance.MaterialComponents.Headline1 = 0x7f130226
com.dhass.myapp:attr/animationMode = 0x7f040034
com.dhass.myapp:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.dhass.myapp:string/mtrl_picker_toggle_to_day_selection = 0x7f120102
com.dhass.myapp:attr/menuGravity = 0x7f0402f7
com.dhass.myapp:attr/cropAspectRatioY = 0x7f040137
com.dhass.myapp:attr/badgeWithTextRadius = 0x7f04005b
com.dhass.myapp:dimen/abc_text_size_display_1_material = 0x7f070043
com.dhass.myapp:anim/design_snackbar_out = 0x7f010021
com.dhass.myapp:attr/pathMotionArc = 0x7f04033d
com.dhass.myapp:attr/waveVariesBy = 0x7f040495
com.dhass.myapp:dimen/mtrl_extended_fab_translation_z_base = 0x7f0701fa
com.dhass.myapp:color/m3_hint_foreground = 0x7f0600a1
com.dhass.myapp:anim/rns_default_enter_out = 0x7f010027
com.dhass.myapp:raw/rzp_config_checkout = 0x7f110016
com.dhass.myapp:attr/roundTopEnd = 0x7f040378
com.dhass.myapp:string/fab_transformation_scrim_behavior = 0x7f1200a1
com.dhass.myapp:color/m3_text_button_background_color_selector = 0x7f0601a7
com.dhass.myapp:anim/rns_slide_out_to_left = 0x7f01003e
com.dhass.myapp:styleable/OnClick = 0x7f14006e
com.dhass.myapp:attr/roundingBorderPadding = 0x7f04037f
com.dhass.myapp:attr/navigationRailStyle = 0x7f04031d
com.dhass.myapp:attr/circularProgressIndicatorStyle = 0x7f0400c8
com.dhass.myapp:id/chip_group = 0x7f0a0090
com.dhass.myapp:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f130374
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Medium = 0x7f130025
com.dhass.myapp:attr/materialCardViewFilledStyle = 0x7f0402e0
com.dhass.myapp:attr/checkboxStyle = 0x7f0400a7
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1303c0
com.dhass.myapp:attr/ratingBarStyleIndicator = 0x7f040364
com.dhass.myapp:attr/cameraBearing = 0x7f040093
com.dhass.myapp:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f130207
com.dhass.myapp:id/auto = 0x7f0a0063
com.dhass.myapp:attr/constraints = 0x7f04010f
com.dhass.myapp:dimen/m3_extended_fab_top_padding = 0x7f070113
com.dhass.myapp:attr/windowFixedHeightMajor = 0x7f040499
com.dhass.myapp:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1303fa
com.dhass.myapp:animator/design_appbar_state_list_animator = 0x7f020000
com.dhass.myapp:animator/fragment_fade_enter = 0x7f020005
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_entypo = 0x7f110003
com.dhass.myapp:attr/chipStrokeColor = 0x7f0400c2
com.dhass.myapp:color/call_notification_decline_color = 0x7f06002e
com.dhass.myapp:attr/hintTextAppearance = 0x7f040212
com.dhass.myapp:attr/chipStandaloneStyle = 0x7f0400c0
com.dhass.myapp:attr/actionBarWidgetTheme = 0x7f04000a
com.dhass.myapp:attr/customThemeStyle = 0x7f040169
com.dhass.myapp:style/ShapeAppearance.Material3.SmallComponent = 0x7f13018c
com.dhass.myapp:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.dhass.myapp:color/m3_ref_palette_primary30 = 0x7f060119
com.dhass.myapp:color/material_on_primary_emphasis_high_type = 0x7f060211
com.dhass.myapp:attr/chipIconVisible = 0x7f0400ba
com.dhass.myapp:dimen/mtrl_fab_elevation = 0x7f0701fd
com.dhass.myapp:dimen/design_bottom_navigation_margin = 0x7f07006d
com.dhass.myapp:style/TextAppearance.MaterialComponents.Headline4 = 0x7f130229
com.dhass.myapp:attr/chipIconTint = 0x7f0400b9
com.dhass.myapp:attr/textAllCaps = 0x7f040405
com.dhass.myapp:string/m3_sys_motion_easing_standard = 0x7f1200c8
com.dhass.myapp:attr/closeIconTint = 0x7f0400d3
com.dhass.myapp:color/design_dark_default_color_background = 0x7f060045
com.dhass.myapp:attr/textAppearanceButton = 0x7f04040b
com.dhass.myapp:id/mtrl_picker_title_text = 0x7f0a019e
com.dhass.myapp:attr/colorOnSurfaceVariant = 0x7f0400f6
com.dhass.myapp:attr/materialCalendarTheme = 0x7f0402dd
com.dhass.myapp:styleable/MaterialAlertDialog = 0x7f140051
com.dhass.myapp:dimen/mtrl_navigation_elevation = 0x7f07020d
com.dhass.myapp:attr/enableEdgeToEdge = 0x7f04019d
com.dhass.myapp:attr/badgeGravity = 0x7f040056
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1301a5
com.dhass.myapp:attr/chipCornerRadius = 0x7f0400b3
com.dhass.myapp:color/m3_ref_palette_error10 = 0x7f0600ef
com.dhass.myapp:color/material_harmonized_color_error = 0x7f060209
com.dhass.myapp:color/abc_search_url_text = 0x7f06000d
com.dhass.myapp:attr/hintAnimationEnabled = 0x7f040210
com.dhass.myapp:id/exo_settings = 0x7f0a00fe
com.dhass.myapp:color/m3_ref_palette_dynamic_primary40 = 0x7f0600cc
com.dhass.myapp:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.dhass.myapp:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f130104
com.dhass.myapp:string/common_google_play_services_unsupported_text = 0x7f120053
com.dhass.myapp:attr/drawableRightCompat = 0x7f040188
com.dhass.myapp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f130034
com.dhass.myapp:anim/abc_slide_out_bottom = 0x7f010008
com.dhass.myapp:dimen/material_cursor_inset_bottom = 0x7f070170
com.dhass.myapp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f13045e
com.dhass.myapp:integer/google_play_services_version = 0x7f0b000b
com.dhass.myapp:color/mtrl_filled_background_color = 0x7f060239
com.dhass.myapp:color/m3_dynamic_dark_primary_text_disable_only = 0x7f060099
com.dhass.myapp:animator/linear_indeterminate_line1_head_interpolator = 0x7f020009
com.dhass.myapp:attr/show_fastforward_button = 0x7f0403a3
com.dhass.myapp:attr/dividerThickness = 0x7f04017f
com.dhass.myapp:style/Widget.AppCompat.Button = 0x7f13031e
com.dhass.myapp:attr/checkedIconSize = 0x7f0400ae
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600ba
com.dhass.myapp:style/Theme.MaterialComponents.Light.Bridge = 0x7f130294
com.dhass.myapp:drawable/notification_bg_low_normal = 0x7f080127
com.dhass.myapp:color/m3_sys_color_dark_primary_container = 0x7f060158
com.dhass.myapp:id/reverseSawtooth = 0x7f0a01d4
com.dhass.myapp:attr/roundBottomStart = 0x7f040376
com.dhass.myapp:attr/layout_constraintLeft_creator = 0x7f04027e
com.dhass.myapp:color/m3_dynamic_primary_text_disable_only = 0x7f06009e
com.dhass.myapp:style/WalletFragmentDefaultButtonTextAppearance = 0x7f13030f
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_surface = 0x7f060168
com.dhass.myapp:attr/isAutofillInlineSuggestionTheme = 0x7f04022f
com.dhass.myapp:id/fast_forward_button = 0x7f0a010c
com.dhass.myapp:string/fallback_menu_item_copy_link = 0x7f1200a3
com.dhass.myapp:dimen/design_fab_elevation = 0x7f070074
com.dhass.myapp:attr/checkedIconMargin = 0x7f0400ad
com.dhass.myapp:color/m3_calendar_item_stroke_color = 0x7f060085
com.dhass.myapp:color/m3_sys_color_light_inverse_primary = 0x7f06018f
com.dhass.myapp:attr/actionBarTabBarStyle = 0x7f040006
com.dhass.myapp:style/TextAppearance.Material3.LabelLarge = 0x7f130219
com.dhass.myapp:color/design_dark_default_color_error = 0x7f060046
com.dhass.myapp:attr/layout_constraintBaseline_creator = 0x7f040269
com.dhass.myapp:attr/errorIconTintMode = 0x7f0401ac
com.dhass.myapp:attr/show_next_button = 0x7f0403a4
com.dhass.myapp:id/SHOW_PROGRESS = 0x7f0a000d
com.dhass.myapp:attr/alertDialogButtonGroupStyle = 0x7f04002a
com.dhass.myapp:attr/roundWithOverlayColor = 0x7f04037c
com.dhass.myapp:color/wallet_bright_foreground_holo_dark = 0x7f060276
com.dhass.myapp:attr/cardMaxElevation = 0x7f04009e
com.dhass.myapp:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080075
com.dhass.myapp:style/ThemeOverlay.AppCompat.DayNight = 0x7f1302af
com.dhass.myapp:attr/actionModeShareDrawable = 0x7f04001a
com.dhass.myapp:attr/barrierMargin = 0x7f040061
com.dhass.myapp:attr/logoAdjustViewBounds = 0x7f0402b7
com.dhass.myapp:attr/customNavigationLayout = 0x7f040166
com.dhass.myapp:id/exo_buffering = 0x7f0a00dd
com.dhass.myapp:id/navigation_header_container = 0x7f0a01a7
com.dhass.myapp:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.dhass.myapp:style/Widget.Material3.NavigationView = 0x7f1303db
com.dhass.myapp:string/google_crash_reporting_api_key = 0x7f1200aa
com.dhass.myapp:attr/itemShapeFillColor = 0x7f040243
com.dhass.myapp:attr/marginTopSystemWindowInsets = 0x7f0402bf
com.dhass.myapp:animator/linear_indeterminate_line1_tail_interpolator = 0x7f02000a
com.dhass.myapp:attr/buttonCompat = 0x7f040086
com.dhass.myapp:styleable/PlayerControlView = 0x7f140070
com.dhass.myapp:style/Base.ThemeOverlay.AppCompat = 0x7f130074
com.dhass.myapp:string/call_notification_ongoing_text = 0x7f120026
com.dhass.myapp:attr/environment = 0x7f0401a7
com.dhass.myapp:attr/bar_gravity = 0x7f04005d
com.dhass.myapp:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1302d9
com.dhass.myapp:drawable/test_level_drawable = 0x7f080143
com.dhass.myapp:attr/backgroundInsetBottom = 0x7f04004d
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_whatsappincpng = 0x7f080069
com.dhass.myapp:style/ExoStyledControls.Button.Center = 0x7f130136
com.dhass.myapp:attr/clockNumberTextColor = 0x7f0400cd
com.dhass.myapp:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080052
com.dhass.myapp:xml/file_provider_path_checkout = 0x7f150000
com.dhass.myapp:attr/layout_constraintHorizontal_weight = 0x7f04027d
com.dhass.myapp:attr/initialActivityCount = 0x7f04022d
com.dhass.myapp:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1301ec
com.dhass.myapp:attr/counterTextColor = 0x7f040135
com.dhass.myapp:layout/design_navigation_item_subheader = 0x7f0d002f
com.dhass.myapp:attr/contentPaddingBottom = 0x7f040119
com.dhass.myapp:attr/buyButtonText = 0x7f040091
com.dhass.myapp:attr/layout_anchorGravity = 0x7f040263
com.dhass.myapp:style/EmptyTheme = 0x7f130120
com.dhass.myapp:color/material_slider_active_tick_marks_color = 0x7f060217
com.dhass.myapp:color/mtrl_navigation_bar_ripple_color = 0x7f060240
com.dhass.myapp:attr/cropScaleType = 0x7f040153
com.dhass.myapp:attr/buyButtonAppearance = 0x7f04008f
com.dhass.myapp:color/m3_sys_color_light_on_tertiary_container = 0x7f06019b
com.dhass.myapp:attr/actionModeCutDrawable = 0x7f040015
com.dhass.myapp:styleable/RangeSlider = 0x7f140077
com.dhass.myapp:color/foreground_material_light = 0x7f060078
com.dhass.myapp:dimen/m3_fab_translation_z_pressed = 0x7f070117
com.dhass.myapp:attr/boxStrokeWidthFocused = 0x7f04007e
com.dhass.myapp:id/consume_window_insets_tag = 0x7f0a009d
com.dhass.myapp:attr/boxBackgroundColor = 0x7f040074
com.dhass.myapp:attr/buttonPanelSideLayout = 0x7f040089
com.dhass.myapp:attr/layoutDuringTransition = 0x7f040260
com.dhass.myapp:dimen/mtrl_btn_hovered_z = 0x7f0701a5
com.dhass.myapp:attr/colorPrimary = 0x7f0400fa
com.dhass.myapp:style/ExoMediaButton.FastForward = 0x7f130122
com.dhass.myapp:string/navigation_menu = 0x7f120106
com.dhass.myapp:id/exo_controller = 0x7f0a00e1
com.dhass.myapp:attr/layout_scrollEffect = 0x7f04029d
com.dhass.myapp:styleable/AnimatedStateListDrawableTransition = 0x7f140009
com.dhass.myapp:integer/m3_sys_motion_duration_100 = 0x7f0b0012
com.dhass.myapp:attr/buttonBarPositiveButtonStyle = 0x7f040084
com.dhass.myapp:attr/tabIndicatorFullWidth = 0x7f0403ee
com.dhass.myapp:dimen/browser_actions_context_menu_min_padding = 0x7f070055
com.dhass.myapp:color/browser_actions_divider_color = 0x7f060028
com.dhass.myapp:id/exo_sub_text = 0x7f0a0102
com.dhass.myapp:string/menuitem_description = 0x7f1200e1
com.dhass.myapp:id/androidx_compose_ui_view_composition_context = 0x7f0a005d
com.dhass.myapp:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.dhass.myapp:attr/materialCalendarHeaderConfirmButton = 0x7f0402d4
com.dhass.myapp:drawable/assets_profileimg_bottomsheet_notionpng = 0x7f080064
com.dhass.myapp:string/icon_content_description = 0x7f1200b3
com.dhass.myapp:drawable/abc_list_focused_holo = 0x7f08002c
com.dhass.myapp:id/scrollView = 0x7f0a01f4
com.dhass.myapp:attr/boxCornerRadiusBottomStart = 0x7f040078
com.dhass.myapp:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f06016a
com.dhass.myapp:attr/lastItemDecorated = 0x7f040259
com.dhass.myapp:attr/enforceTextAppearance = 0x7f0401a5
com.dhass.myapp:attr/boxBackgroundMode = 0x7f040075
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1301d9
com.dhass.myapp:attr/attributeName = 0x7f04003c
com.dhass.myapp:attr/progressBarImageScaleType = 0x7f04035b
com.dhass.myapp:attr/coordinatorLayoutStyle = 0x7f040123
com.dhass.myapp:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f13019e
com.dhass.myapp:attr/textAppearanceHeadlineSmall = 0x7f040418
com.dhass.myapp:attr/fontVariationSettings = 0x7f0401f8
com.dhass.myapp:attr/bottomNavigationStyle = 0x7f040071
com.dhass.myapp:attr/bottomInsetScrimEnabled = 0x7f040070
com.dhass.myapp:styleable/Fragment = 0x7f14003b
com.dhass.myapp:attr/elevationOverlayEnabled = 0x7f04019b
com.dhass.myapp:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_feather = 0x7f110005
com.dhass.myapp:color/m3_card_ripple_color = 0x7f060087
com.dhass.myapp:dimen/mtrl_btn_disabled_z = 0x7f0701a2
com.dhass.myapp:color/material_dynamic_neutral_variant20 = 0x7f0601d1
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600af
com.dhass.myapp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f13016a
com.dhass.myapp:dimen/design_bottom_navigation_active_text_size = 0x7f070066
com.dhass.myapp:attr/behavior_skipCollapsed = 0x7f04006c
com.dhass.myapp:anim/abc_slide_in_bottom = 0x7f010006
com.dhass.myapp:attr/buttonIconDimen = 0x7f040088
com.dhass.myapp:id/rectangle = 0x7f0a01cf
com.dhass.myapp:attr/constraintSet = 0x7f04010b
com.dhass.myapp:drawable/ic_tick_mark = 0x7f0800f8
com.dhass.myapp:attr/tabTextColor = 0x7f0403ff
com.dhass.myapp:attr/textInputLayoutFocusedRectEnabled = 0x7f040431
com.dhass.myapp:id/activity_chooser_view_content = 0x7f0a0050
com.dhass.myapp:id/unlabeled = 0x7f0a0268
com.dhass.myapp:dimen/mtrl_btn_elevation = 0x7f0701a3
com.dhass.myapp:anim/catalyst_fade_in = 0x7f010018
com.dhass.myapp:attr/percentWidth = 0x7f040340
com.dhass.myapp:attr/layout_constraintHeight_default = 0x7f040277
com.dhass.myapp:drawable/amu_bubble_shadow = 0x7f080056
com.dhass.myapp:attr/ad_marker_width = 0x7f040029
com.dhass.myapp:attr/behavior_fitToContents = 0x7f040066
com.dhass.myapp:attr/progressBarAutoRotateInterval = 0x7f040359
com.dhass.myapp:attr/textAppearanceCaption = 0x7f04040c
com.dhass.myapp:id/checkbox = 0x7f0a008a
com.dhass.myapp:drawable/assets_images_group1321316367 = 0x7f080058
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600b2
com.dhass.myapp:drawable/abc_dialog_material_background = 0x7f080019
com.dhass.myapp:dimen/mtrl_calendar_year_corner = 0x7f0701dc
com.dhass.myapp:styleable/LinearLayoutCompat_Layout = 0x7f14004c
com.dhass.myapp:style/ExoStyledControls.Button.Center.Previous = 0x7f13013a
com.dhass.myapp:attr/boxCollapsedPaddingTop = 0x7f040076
com.dhass.myapp:color/material_slider_inactive_tick_marks_color = 0x7f06021a
com.dhass.myapp:attr/applyMotionScene = 0x7f040038
com.dhass.myapp:attr/barrierAllowsGoneWidgets = 0x7f04005f
com.dhass.myapp:attr/flow_horizontalAlign = 0x7f0401df
com.dhass.myapp:id/fragment_container_view_tag = 0x7f0a0124
com.dhass.myapp:attr/touchRegionId = 0x7f040467
com.dhass.myapp:attr/badgeTextColor = 0x7f040059
com.dhass.myapp:id/leftToRight = 0x7f0a015b
com.dhass.myapp:attr/counterTextAppearance = 0x7f040134
com.dhass.myapp:dimen/notification_small_icon_size_as_large = 0x7f070259
com.dhass.myapp:color/design_default_color_background = 0x7f060052
com.dhass.myapp:color/m3_navigation_item_text_color = 0x7f0600a8
com.dhass.myapp:dimen/m3_badge_with_text_vertical_offset = 0x7f0700d9
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f130285
com.dhass.myapp:attr/actionModeStyle = 0x7f04001c
com.dhass.myapp:color/m3_sys_color_light_outline = 0x7f06019c
com.dhass.myapp:style/Widget.AppCompat.ActivityChooserView = 0x7f13031c
com.dhass.myapp:drawable/ic_mtrl_chip_checked_circle = 0x7f0800f3
com.dhass.myapp:attr/imageButtonStyle = 0x7f040224
com.dhass.myapp:attr/cropCornerRadius = 0x7f040142
com.dhass.myapp:dimen/abc_search_view_preferred_width = 0x7f070037
com.dhass.myapp:attr/backgroundInsetStart = 0x7f04004f
com.dhass.myapp:color/common_google_signin_btn_tint = 0x7f060042
com.dhass.myapp:attr/backgroundStacked = 0x7f040053
com.dhass.myapp:color/design_dark_default_color_on_background = 0x7f060047
com.dhass.myapp:styleable/AppCompatEmojiHelper = 0x7f14000d
com.dhass.myapp:id/fitToContents = 0x7f0a011a
com.dhass.myapp:id/tag_window_insets_animation_callback = 0x7f0a0239
com.dhass.myapp:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070161
com.dhass.myapp:anim/rns_fade_in = 0x7f01002b
com.dhass.myapp:anim/rns_ios_from_left_foreground_open = 0x7f010031
com.dhass.myapp:style/Base.v21.Theme.SplashScreen.Light = 0x7f130114
com.dhass.myapp:color/m3_ref_palette_primary40 = 0x7f06011a
com.dhass.myapp:attr/boxCornerRadiusBottomEnd = 0x7f040077
com.dhass.myapp:animator/linear_indeterminate_line2_head_interpolator = 0x7f02000b
com.dhass.myapp:attr/thumbColor = 0x7f04043b
com.dhass.myapp:string/range_start = 0x7f120114
com.dhass.myapp:attr/cropSaveBitmapToInstanceState = 0x7f040152
com.dhass.myapp:attr/behavior_draggable = 0x7f040064
com.dhass.myapp:attr/textAppearanceDisplayMedium = 0x7f04040e
com.dhass.myapp:attr/barLength = 0x7f04005c
com.dhass.myapp:attr/fastScrollEnabled = 0x7f0401cb
com.dhass.myapp:attr/autoSizeMinTextSize = 0x7f04003f
com.dhass.myapp:anim/abc_tooltip_exit = 0x7f01000b
com.dhass.myapp:styleable/NavigationBarView = 0x7f14006b
com.dhass.myapp:attr/drawableSize = 0x7f040189
com.dhass.myapp:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1300e1
com.dhass.myapp:attr/minWidth = 0x7f0402fc
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall.Top = 0x7f13017a
com.dhass.myapp:attr/animation_enabled = 0x7f040035
com.dhass.myapp:color/design_default_color_on_background = 0x7f060054
com.dhass.myapp:attr/cropAutoZoomEnabled = 0x7f040138
com.dhass.myapp:attr/roundTopLeft = 0x7f040379
com.dhass.myapp:anim/rns_slide_in_from_bottom = 0x7f01003a
com.dhass.myapp:id/autofill_inline_suggestion_end_icon = 0x7f0a0067
com.dhass.myapp:dimen/design_snackbar_background_corner_radius = 0x7f070085
com.dhass.myapp:attr/expandedTitleGravity = 0x7f0401b2
com.dhass.myapp:dimen/mtrl_slider_halo_radius = 0x7f07022d
com.dhass.myapp:string/catalyst_copy_button = 0x7f120029
com.dhass.myapp:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600c6
com.dhass.myapp:attr/animate_relativeTo = 0x7f040033
com.dhass.myapp:anim/rns_no_animation_20 = 0x7f010036
com.dhass.myapp:attr/dividerPadding = 0x7f04017e
com.dhass.myapp:style/Base.Widget.AppCompat.Spinner = 0x7f1300ec
com.dhass.myapp:attr/collapsedTitleGravity = 0x7f0400d9
com.dhass.myapp:drawable/ic_keyboard_black_24dp = 0x7f0800ec
com.dhass.myapp:attr/unplayed_color = 0x7f040483
com.dhass.myapp:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f140034
com.dhass.myapp:attr/cropMaxCropResultHeightPX = 0x7f04014a
com.dhass.myapp:dimen/m3_sys_elevation_level4 = 0x7f070138
com.dhass.myapp:attr/autofillInlineSuggestionTitle = 0x7f040049
com.dhass.myapp:id/action_mode_close_button = 0x7f0a004d
com.dhass.myapp:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080036
com.dhass.myapp:attr/arrowHeadLength = 0x7f04003a
com.dhass.myapp:attr/dividerInsetStart = 0x7f04017d
com.dhass.myapp:dimen/design_snackbar_padding_vertical = 0x7f07008b
com.dhass.myapp:attr/cardForegroundColor = 0x7f04009d
com.dhass.myapp:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1301e6
com.dhass.myapp:drawable/$avd_show_password__2 = 0x7f080005
com.dhass.myapp:attr/ad_marker_color = 0x7f040028
com.dhass.myapp:layout/exo_player_control_view = 0x7f0d0036
com.dhass.myapp:color/m3_ref_palette_neutral30 = 0x7f0600ff
com.dhass.myapp:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1300c4
com.dhass.myapp:id/edittext_dropdown_noneditable = 0x7f0a00cf
com.dhass.myapp:attr/headerLayout = 0x7f040204
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600da
com.dhass.myapp:color/abc_hint_foreground_material_light = 0x7f060008
com.dhass.myapp:attr/checkedIconEnabled = 0x7f0400ab
com.dhass.myapp:attr/roundBottomRight = 0x7f040375
com.dhass.myapp:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080014
com.dhass.myapp:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070151
com.dhass.myapp:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f130349
com.dhass.myapp:attr/dialogPreferredPadding = 0x7f040176
com.dhass.myapp:anim/rns_default_exit_in = 0x7f010028
com.dhass.myapp:color/m3_ref_palette_dynamic_primary99 = 0x7f0600d3
com.dhass.myapp:style/Widget.MaterialComponents.Snackbar = 0x7f130452
com.dhass.myapp:string/item_view_role_description = 0x7f1200b9
com.dhass.myapp:drawable/exo_styled_controls_next = 0x7f0800ce
com.dhass.myapp:attr/layout = 0x7f04025e
com.dhass.myapp:attr/actionModeCloseContentDescription = 0x7f040012
com.dhass.myapp:color/material_dynamic_tertiary100 = 0x7f0601f7
com.dhass.myapp:attr/closeIconStartPadding = 0x7f0400d2
com.dhass.myapp:color/material_deep_teal_200 = 0x7f0601be
com.dhass.myapp:attr/alertDialogTheme = 0x7f04002d
com.dhass.myapp:color/mtrl_btn_text_color_selector = 0x7f060228
com.dhass.myapp:layout/abc_tooltip = 0x7f0d001b
com.dhass.myapp:attr/colorOnSurfaceInverse = 0x7f0400f5
com.dhass.myapp:attr/roundBottomEnd = 0x7f040373
com.dhass.myapp:string/mtrl_picker_text_input_month_abbr = 0x7f1200ff
com.dhass.myapp:attr/actionModeCloseDrawable = 0x7f040013
com.dhass.myapp:attr/latLngBoundsNorthEastLatitude = 0x7f04025a
com.dhass.myapp:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080023
com.dhass.myapp:attr/layout_goneMarginRight = 0x7f040297
com.dhass.myapp:attr/backgroundTintMode = 0x7f040055
com.dhass.myapp:attr/transitionFlags = 0x7f040474
com.dhass.myapp:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f130422
com.dhass.myapp:color/wallet_link_text_light = 0x7f06027f
com.dhass.myapp:attr/fontProviderCerts = 0x7f0401f1
com.dhass.myapp:drawable/notify_panel_notification_icon_bg = 0x7f080130
com.dhass.myapp:drawable/exo_styled_controls_pause = 0x7f0800d1
com.dhass.myapp:dimen/mtrl_slider_thumb_elevation = 0x7f070231
com.dhass.myapp:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400df
com.dhass.myapp:attr/bar_height = 0x7f04005e
com.dhass.myapp:color/mtrl_navigation_bar_colored_ripple_color = 0x7f06023e
com.dhass.myapp:attr/actionButtonStyle = 0x7f04000b
com.dhass.myapp:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f070149
com.dhass.myapp:id/textinput_suffix_text = 0x7f0a0250
com.dhass.myapp:color/bright_foreground_material_dark = 0x7f060025
com.dhass.myapp:attr/mock_showLabel = 0x7f040302
com.dhass.myapp:color/m3_ref_palette_error20 = 0x7f0600f1
com.dhass.myapp:styleable/Autofill.InlineSuggestion = 0x7f140014
com.dhass.myapp:id/catalyst_redbox_title = 0x7f0a0081
com.dhass.myapp:color/m3_ref_palette_secondary99 = 0x7f06012e
com.dhass.myapp:attr/checkedIcon = 0x7f0400aa
com.dhass.myapp:attr/materialCalendarMonthNavigationButton = 0x7f0402db
com.dhass.myapp:string/catalyst_report_button = 0x7f12003e
com.dhass.myapp:attr/colorOnPrimarySurface = 0x7f0400f1
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary90 = 0x7f0600de
com.dhass.myapp:attr/minHideDelay = 0x7f0402f9
com.dhass.myapp:integer/m3_sys_motion_duration_600 = 0x7f0b001e
com.dhass.myapp:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.dhass.myapp:attr/itemSpacing = 0x7f040248
com.dhass.myapp:string/path_password_eye_mask_strike_through = 0x7f12010a
com.dhass.myapp:attr/expandActivityOverflowButtonDrawable = 0x7f0401af
com.dhass.myapp:color/m3_ref_palette_tertiary50 = 0x7f060135
com.dhass.myapp:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.dhass.myapp:dimen/m3_btn_dialog_btn_min_width = 0x7f0700e5
com.dhass.myapp:attr/cardUseCompatPadding = 0x7f0400a0
com.dhass.myapp:attr/textAppearanceBodyLarge = 0x7f040408
com.dhass.myapp:drawable/exo_ic_subtitle_off = 0x7f0800ae
com.dhass.myapp:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f020018
com.dhass.myapp:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f130433
com.dhass.myapp:attr/textAppearanceListItemSmall = 0x7f040420
com.dhass.myapp:id/exo_progress = 0x7f0a00f9
com.dhass.myapp:attr/splitTrack = 0x7f0403bc
com.dhass.myapp:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020017
com.dhass.myapp:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.dhass.myapp:attr/chipSurfaceColor = 0x7f0400c5
com.dhass.myapp:style/Base.Widget.Material3.CardView = 0x7f1300f5
com.dhass.myapp:attr/chipSpacingHorizontal = 0x7f0400be
com.dhass.myapp:color/m3_ref_palette_error100 = 0x7f0600f0
com.dhass.myapp:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f13046a
com.dhass.myapp:attr/selectorSize = 0x7f040393
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130302
com.dhass.myapp:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.dhass.myapp:drawable/ic_call_answer = 0x7f0800e2
com.dhass.myapp:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f130421
com.dhass.myapp:animator/mtrl_btn_state_list_anim = 0x7f020013
com.dhass.myapp:attr/keyPositionType = 0x7f040251
com.dhass.myapp:attr/uiScrollGestures = 0x7f04047e
com.dhass.myapp:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f13030b
com.dhass.myapp:dimen/material_clock_period_toggle_height = 0x7f07016c
com.dhass.myapp:attr/cornerFamilyTopRight = 0x7f040128
com.dhass.myapp:style/TextAppearance.Compat.Notification.Media = 0x7f1301ef
com.dhass.myapp:id/material_timepicker_edit_text = 0x7f0a0175
com.dhass.myapp:attr/itemIconTint = 0x7f04023a
com.dhass.myapp:style/Test.Theme.MaterialComponents.MaterialCalendar = 0x7f1301b0
com.dhass.myapp:dimen/notification_right_icon_size = 0x7f070256
com.dhass.myapp:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f070156
com.dhass.myapp:color/m3_navigation_bar_ripple_color_selector = 0x7f0600a4
com.dhass.myapp:style/Base.Widget.AppCompat.RatingBar = 0x7f1300e5
com.dhass.myapp:drawable/ic_fullscreen_32dp = 0x7f0800ea
com.dhass.myapp:attr/logoDescription = 0x7f0402b8
com.dhass.myapp:drawable/common_google_signin_btn_text_light_focused = 0x7f080086
com.dhass.myapp:attr/clockFaceBackgroundColor = 0x7f0400ca
com.dhass.myapp:style/ThemeOverlay.AppCompat.Dark = 0x7f1302ad
com.dhass.myapp:style/SpinnerTimePickerDialog = 0x7f1301ac
com.dhass.myapp:attr/layout_constraintBaseline_toBaselineOf = 0x7f04026a
com.dhass.myapp:dimen/mtrl_badge_text_size = 0x7f070195
com.dhass.myapp:color/design_default_color_surface = 0x7f06005e
com.dhass.myapp:attr/state_dragged = 0x7f0403c8
com.dhass.myapp:attr/uiTiltGestures = 0x7f040480
com.dhass.myapp:attr/activityChooserViewStyle = 0x7f040024
com.dhass.myapp:anim/abc_fade_in = 0x7f010000
com.dhass.myapp:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f07017f
com.dhass.myapp:attr/appTheme = 0x7f040037
com.dhass.myapp:id/decelerate = 0x7f0a00ad
com.dhass.myapp:attr/paddingLeftSystemWindowInsets = 0x7f040330
com.dhass.myapp:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1303e8
com.dhass.myapp:dimen/exo_styled_progress_layout_height = 0x7f0700b5
com.dhass.myapp:id/is_pooling_container_tag = 0x7f0a0151
com.dhass.myapp:animator/mtrl_extended_fab_hide_motion_spec = 0x7f020019
com.dhass.myapp:attr/contentPaddingStart = 0x7f04011d
com.dhass.myapp:drawable/rzp_secured_by_bg = 0x7f08013f
com.dhass.myapp:anim/mtrl_bottom_sheet_slide_in = 0x7f010023
com.dhass.myapp:anim/rns_slide_in_from_left = 0x7f01003b
com.dhass.myapp:id/visible_removing_fragment_view_tag = 0x7f0a0275
com.dhass.myapp:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f070237
com.dhass.myapp:attr/checkMarkTintMode = 0x7f0400a6
com.dhass.myapp:dimen/mtrl_slider_label_square_side = 0x7f070230
com.dhass.myapp:attr/buffered_color = 0x7f040080
com.dhass.myapp:animator/fragment_open_exit = 0x7f020008
com.dhass.myapp:layout/ime_base_split_test_activity = 0x7f0d0042
com.dhass.myapp:attr/errorTextColor = 0x7f0401ae
com.dhass.myapp:drawable/assets_img_carouselimg = 0x7f08005d
com.dhass.myapp:attr/collapsedTitleTextAppearance = 0x7f0400da
com.dhass.myapp:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f130281
com.dhass.myapp:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.dhass.myapp:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f13033d
com.dhass.myapp:dimen/mtrl_btn_text_btn_icon_padding = 0x7f0701b2
com.dhass.myapp:attr/colorPrimaryVariant = 0x7f0400ff
com.dhass.myapp:anim/rns_fade_out = 0x7f01002c
com.dhass.myapp:id/transform = 0x7f0a025a
com.dhass.myapp:drawable/exo_styled_controls_check = 0x7f0800ca
com.dhass.myapp:attr/buttonBarStyle = 0x7f040085
com.dhass.myapp:id/textSpacerNoTitle = 0x7f0a0244
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600d8
com.dhass.myapp:attr/layout_scrollInterpolator = 0x7f04029f
com.dhass.myapp:id/design_menu_item_action_area_stub = 0x7f0a00b4
com.dhass.myapp:style/TextAppearance.MaterialComponents.Button = 0x7f130223
com.dhass.myapp:dimen/mtrl_high_ripple_hovered_alpha = 0x7f070203
com.dhass.myapp:dimen/design_fab_image_size = 0x7f070075
com.dhass.myapp:attr/customFloatValue = 0x7f040164
com.dhass.myapp:attr/background = 0x7f04004a
com.dhass.myapp:color/m3_ref_palette_tertiary100 = 0x7f060131
com.dhass.myapp:id/tag_accessibility_pane_title = 0x7f0a0230
com.dhass.myapp:attr/hideOnContentScroll = 0x7f04020c
com.dhass.myapp:dimen/mtrl_snackbar_margin = 0x7f07023a
com.dhass.myapp:attr/alphabeticModifiers = 0x7f040030
com.dhass.myapp:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1303cc
com.dhass.myapp:anim/design_bottom_sheet_slide_in = 0x7f01001e
com.dhass.myapp:drawable/ic_mtrl_chip_checked_black = 0x7f0800f2
com.dhass.myapp:anim/mtrl_card_lowers_interpolator = 0x7f010025
com.dhass.myapp:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600d9
com.dhass.myapp:styleable/FloatingActionButton_Behavior_Layout = 0x7f140036
com.dhass.myapp:attr/cardElevation = 0x7f04009c
com.dhass.myapp:attr/thumbTintMode = 0x7f040442
com.dhass.myapp:id/transition_position = 0x7f0a0260
com.dhass.myapp:animator/fragment_close_enter = 0x7f020003
com.dhass.myapp:dimen/m3_chip_icon_size = 0x7f07010b
com.dhass.myapp:attr/cropperLabelTextSize = 0x7f04015c
com.dhass.myapp:color/m3_ref_palette_primary50 = 0x7f06011b
com.dhass.myapp:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.dhass.myapp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f130179
com.dhass.myapp:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005
com.dhass.myapp:attr/marginLeftSystemWindowInsets = 0x7f0402bd
com.dhass.myapp:attr/boxCornerRadiusTopEnd = 0x7f040079
com.dhass.myapp:color/material_grey_300 = 0x7f060203
com.dhass.myapp:attr/tabPaddingStart = 0x7f0403f8
com.dhass.myapp:animator/mtrl_chip_state_list_anim = 0x7f020016
com.dhass.myapp:color/design_dark_default_color_primary_variant = 0x7f06004e
com.dhass.myapp:style/Theme.Catalyst.RedBox = 0x7f130252
com.dhass.myapp:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f130176
com.dhass.myapp:attr/drawableTint = 0x7f04018b
com.dhass.myapp:anim/catalyst_slide_up = 0x7f01001d
com.dhass.myapp:style/TestStyleWithThemeLineHeightAttribute = 0x7f1301b6
com.dhass.myapp:id/accessibility_custom_action_11 = 0x7f0a0019
com.dhass.myapp:attr/autofillInlineSuggestionChip = 0x7f040045
com.dhass.myapp:dimen/mtrl_slider_track_side_padding = 0x7f070234
com.dhass.myapp:id/path = 0x7f0a01bd
com.dhass.myapp:color/secondary_text_default_material_light = 0x7f060264
com.dhass.myapp:attr/autofillInlineSuggestionEndIconStyle = 0x7f040046
com.dhass.myapp:style/Widget.MaterialComponents.Button.TextButton = 0x7f130410
com.dhass.myapp:animator/mtrl_fab_hide_motion_spec = 0x7f02001c
com.dhass.myapp:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.dhass.myapp:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f130009
com.dhass.myapp:id/progress_circular = 0x7f0a01ca
com.dhass.myapp:attr/colorPrimaryContainer = 0x7f0400fb
com.dhass.myapp:attr/itemBackground = 0x7f040234
com.dhass.myapp:attr/verticalOffset = 0x7f04048a
com.dhass.myapp:id/fixed = 0x7f0a011c
com.dhass.myapp:attr/brightness = 0x7f04007f
