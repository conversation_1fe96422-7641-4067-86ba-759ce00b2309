[{"merged": "expo.modules.mymodule.my-module-release-53:/animator/fragment_open_enter.xml", "source": "expo.modules.mymodule.my-module-fragment-1.3.6-6:/animator/fragment_open_enter.xml"}, {"merged": "expo.modules.mymodule.my-module-release-53:/animator/fragment_close_enter.xml", "source": "expo.modules.mymodule.my-module-fragment-1.3.6-6:/animator/fragment_close_enter.xml"}, {"merged": "expo.modules.mymodule.my-module-release-53:/animator/fragment_fade_exit.xml", "source": "expo.modules.mymodule.my-module-fragment-1.3.6-6:/animator/fragment_fade_exit.xml"}, {"merged": "expo.modules.mymodule.my-module-release-53:/animator/fragment_close_exit.xml", "source": "expo.modules.mymodule.my-module-fragment-1.3.6-6:/animator/fragment_close_exit.xml"}, {"merged": "expo.modules.mymodule.my-module-release-53:/animator/fragment_fade_enter.xml", "source": "expo.modules.mymodule.my-module-fragment-1.3.6-6:/animator/fragment_fade_enter.xml"}, {"merged": "expo.modules.mymodule.my-module-release-53:/animator/fragment_open_exit.xml", "source": "expo.modules.mymodule.my-module-fragment-1.3.6-6:/animator/fragment_open_exit.xml"}]