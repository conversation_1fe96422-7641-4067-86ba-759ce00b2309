import React from "react";
import { Text, View } from "react-native";

const ColumnComponent = ({
  text,
  style,
  icon,
  containerstyle,
  extratext,
}: {
  text: string;
  style: string;
  icon?: React.ReactNode;
  containerstyle?: any;
  extratext?: React.ReactNode;
}) => {
  return (
    <View className="flex-1 items-center justify-center ml-2" style={containerstyle}>
      <View className="flex-row items-center gap-1">
        {icon}
        <Text className={style}>{text}</Text>
      </View>
      {extratext}
    </View>
  );
};

export default ColumnComponent;
