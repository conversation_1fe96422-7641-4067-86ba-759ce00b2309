<dependencies>
  <compile
      roots=":@@:expo::release,:@@:react-native-screens::release,com.facebook.react:react-android:0.76.7:release@aar,:@@:expo-av::release,:@@:expo-file-system::release,com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.google.android.exoplayer:extension-okhttp:2.18.1@aar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,:@@:expo-modules-core::release,:@@:expo-camera::release,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,:@@:expo-device::release,:@@:expo-sharing::release,androidx.legacy:legacy-support-v4:1.0.0@aar,:@@:expo-image-loader::release,com.github.bumptech.glide:glide:4.16.0@aar,:@@:expo-location::release,com.google.android.gms:play-services-location:21.0.1@aar,com.google.android.gms:play-services-base:18.3.0@aar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.activity:activity:1.7.2@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,:@@:expo-web-browser::release,androidx.browser:browser:1.6.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,com.google.android.exoplayer:exoplayer:2.18.1@aar,com.google.android.exoplayer:exoplayer-ui:2.18.1@aar,androidx.media:media:1.4.3@aar,androidx.media:media:1.4.3@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.core:core-ktx:1.13.1@aar,androidx.savedstate:savedstate:1.2.1@aar,com.facebook.fresco:fresco:3.2.0@aar,com.facebook.fresco:middleware:3.2.0@aar,com.facebook.fresco:ui-common:3.2.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.facebook.fresco:fbcore:3.2.0@aar,com.facebook.fresco:imagepipeline:3.2.0@aar,com.facebook.fresco:imagepipeline-base:3.2.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,:@@:expo-constants::release,:@@:expo-document-picker::release,:@@:expo-haptics::release,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.collection:collection-jvm:1.4.4@jar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar,:@@:react-native-async-storage_async-storage::release,:@@:react-native-community_datetimepicker::release,:@@:react-native-gesture-handler::release,:@@:react-native-get-random-values::release,:@@:react-native-html-to-pdf::release,:@@:react-native-maps::release,:@@:react-native-razorpay::release,:@@:react-native-reanimated::release,:@@:react-native-safe-area-context::release,:@@:react-native-svg::release,:@@:react-native-view-shot::release,:@@:react-native-webview::release,com.facebook.fresco:animated-gif:3.2.0@aar,com.facebook.fresco:webpsupport:3.2.0@aar,com.facebook.react:hermes-android:0.76.7:release@aar,org.jetbrains:annotations:23.0.0@jar,com.google.android.exoplayer:exoplayer-core:2.18.1@aar,com.google.android.exoplayer:exoplayer-common:2.18.1@aar,com.google.guava:guava:31.0.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.tracing:tracing:1.2.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.6.0@aar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.soloader:nativeloader:0.12.1@jar,com.facebook.soloader:annotation:0.12.1@jar,com.facebook.fresco:drawee:3.2.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:3.2.0@aar,com.facebook.fresco:memory-type-ashmem:3.2.0@aar,com.facebook.fresco:memory-type-native:3.2.0@aar,com.facebook.fresco:memory-type-java:3.2.0@aar,com.facebook.fresco:nativeimagefilters:3.2.0@aar,com.facebook.fresco:nativeimagetranscoder:3.2.0@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,javax.inject:javax.inject:1@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.android.exoplayer:exoplayer-database:2.18.1@aar,com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar,com.google.android.exoplayer:exoplayer-dash:2.18.1@aar,com.google.android.exoplayer:exoplayer-hls:2.18.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar,:@@:expo-media-library::release,androidx.exifinterface:exifinterface:1.3.7@aar,commons-io:commons-io:2.6@jar,com.facebook.device.yearclass:yearclass:2.1.0@jar,commons-codec:commons-codec:1.10@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,:@@:expo-application::release,:@@:expo-asset::release,:@@:expo-font::release,:@@:expo-image-picker::release,:@@:expo-keep-awake::release,:@@:expo-linear-gradient::release,:@@:expo-linking::release,:@@:expo-notifications::release,:@@:expo-print::release,:@@:expo-splash-screen::release,:@@:expo-system-ui::release,:@@:my-module::release">
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="SellerApp:react-native-screens"/>
    <dependency
        name="com.facebook.react:react-android:0.76.7:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name=":@@:expo-av::release"
        simpleName="host.exp.exponent:expo-av"/>
    <dependency
        name=":@@:expo-file-system::release"
        simpleName="host.exp.exponent:expo-file-system"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.google.android.exoplayer:extension-okhttp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:extension-okhttp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name=":@@:expo-camera::release"
        simpleName="host.exp.exponent:expo-camera"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name=":@@:expo-device::release"
        simpleName="host.exp.exponent:expo-device"/>
    <dependency
        name=":@@:expo-sharing::release"
        simpleName="host.exp.exponent:expo-sharing"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name=":@@:expo-image-loader::release"
        simpleName="host.exp.exponent:expo-image-loader"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name=":@@:expo-location::release"
        simpleName="host.exp.exponent:expo-location"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.0.1@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.7.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name=":@@:expo-web-browser::release"
        simpleName="host.exp.exponent:expo-web-browser"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.media:media:1.4.3@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="com.facebook.fresco:fresco:3.2.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:middleware:3.2.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.2.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.2.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name=":@@:expo-document-picker::release"
        simpleName="host.exp.exponent:expo-document-picker"/>
    <dependency
        name=":@@:expo-haptics::release"
        simpleName="host.exp.exponent:expo-haptics"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="SellerApp:react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:react-native-community_datetimepicker::release"
        simpleName="SellerApp:react-native-community_datetimepicker"/>
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="SellerApp:react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-get-random-values::release"
        simpleName="SellerApp:react-native-get-random-values"/>
    <dependency
        name=":@@:react-native-html-to-pdf::release"
        simpleName="SellerApp:react-native-html-to-pdf"/>
    <dependency
        name=":@@:react-native-maps::release"
        simpleName="SellerApp:react-native-maps"/>
    <dependency
        name=":@@:react-native-razorpay::release"
        simpleName="SellerApp:react-native-razorpay"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="SellerApp:react-native-reanimated"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="SellerApp:react-native-safe-area-context"/>
    <dependency
        name=":@@:react-native-svg::release"
        simpleName="SellerApp:react-native-svg"/>
    <dependency
        name=":@@:react-native-view-shot::release"
        simpleName="SellerApp:react-native-view-shot"/>
    <dependency
        name=":@@:react-native-webview::release"
        simpleName="SellerApp:react-native-webview"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.2.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.2.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.react:hermes-android:0.76.7:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="com.google.guava:guava:31.0.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.6.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.fresco:drawee:3.2.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name=":@@:expo-media-library::release"
        simpleName="host.exp.exponent:expo-media-library"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
        simpleName="com.facebook.device.yearclass:yearclass"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name=":@@:expo-application::release"
        simpleName="host.exp.exponent:expo-application"/>
    <dependency
        name=":@@:expo-asset::release"
        simpleName="expo.modules.asset:expo-asset"/>
    <dependency
        name=":@@:expo-font::release"
        simpleName="host.exp.exponent:expo-font"/>
    <dependency
        name=":@@:expo-image-picker::release"
        simpleName="host.exp.exponent:expo-image-picker"/>
    <dependency
        name=":@@:expo-keep-awake::release"
        simpleName="host.exp.exponent:expo-keep-awake"/>
    <dependency
        name=":@@:expo-linear-gradient::release"
        simpleName="host.exp.exponent:expo-linear-gradient"/>
    <dependency
        name=":@@:expo-linking::release"
        simpleName="expo.modules.linking:expo-linking"/>
    <dependency
        name=":@@:expo-notifications::release"
        simpleName="host.exp.exponent:expo-notifications"/>
    <dependency
        name=":@@:expo-print::release"
        simpleName="host.exp.exponent:expo-print"/>
    <dependency
        name=":@@:expo-splash-screen::release"
        simpleName="host.exp.exponent:expo-splash-screen"/>
    <dependency
        name=":@@:expo-system-ui::release"
        simpleName="host.exp.exponent:expo-system-ui"/>
    <dependency
        name=":@@:my-module::release"
        simpleName="expo.modules.mymodule:my-module"/>
  </compile>
  <package
      roots=":@@:expo::release,:@@:react-native-gesture-handler::release,:@@:react-native-safe-area-context::release,:@@:react-native-screens::release,:@@:react-native-webview::release,:@@:react-native-async-storage_async-storage::release,:@@:react-native-community_datetimepicker::release,:@@:react-native-get-random-values::release,:@@:react-native-html-to-pdf::release,:@@:react-native-maps::release,:@@:react-native-razorpay::release,:@@:react-native-reanimated::release,:@@:react-native-svg::release,:@@:react-native-view-shot::release,:@@:expo-application::release,:@@:expo-asset::release,:@@:expo-av::release,:@@:expo-camera::release,:@@:expo-constants::release,:@@:expo-device::release,:@@:expo-document-picker::release,:@@:expo-file-system::release,:@@:expo-font::release,:@@:expo-haptics::release,:@@:expo-image-loader::release,:@@:expo-image-picker::release,:@@:expo-keep-awake::release,:@@:expo-linear-gradient::release,:@@:expo-linking::release,:@@:expo-location::release,:@@:expo-media-library::release,:@@:expo-notifications::release,:@@:expo-print::release,:@@:expo-sharing::release,:@@:expo-splash-screen::release,:@@:expo-system-ui::release,:@@:expo-web-browser::release,:@@:my-module::release,:@@:expo-modules-core::release,com.facebook.react:react-android:0.76.7:release@aar,com.google.maps.android:android-maps-utils:3.8.2@aar,com.razorpay:checkout:1.6.41@aar,com.razorpay:standard-core:1.6.49@aar,com.google.android.gms:play-services-wallet:18.1.3@aar,com.google.android.gms:play-services-maps:18.2.0@aar,com.google.android.material:material:1.6.1@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.camera:camera-mlkit-vision:1.4.1@aar,androidx.camera:camera-extensions:1.4.1@aar,androidx.camera:camera-video:1.4.1@aar,androidx.camera:camera-lifecycle:1.4.1@aar,androidx.camera:camera-camera2:1.4.1@aar,androidx.camera:camera-core:1.4.1@aar,androidx.camera:camera-view:1.4.1@aar,com.github.CanHub:Android-Image-Cropper:4.3.1@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.core:core-splashscreen:1.2.0-alpha02@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.google.android.gms:play-services-auth:21.1.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,com.google.android.gms:play-services-location:21.0.1@aar,com.google.firebase:firebase-messaging:24.0.1@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.mlkit:barcode-scanning:17.2.0@aar,com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0@aar,com.google.mlkit:barcode-scanning-common:17.0.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.9.0@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:20.0.1@aar,com.google.android.gms:play-services-identity:17.0.0@aar,com.google.android.gms:play-services-base:18.3.0@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.mlkit:vision-interfaces:16.2.0@aar,androidx.compose.material:material-android:1.7.8@aar,androidx.compose.material:material-ripple-android:1.7.8@aar,androidx.compose.foundation:foundation-layout-android:1.7.8@aar,androidx.compose.foundation:foundation-android:1.7.8@aar,androidx.compose.animation:animation-core-android:1.7.8@aar,androidx.compose.animation:animation-android:1.7.8@aar,androidx.compose.ui:ui-util-android:1.7.8@aar,androidx.compose.ui:ui-unit-android:1.7.8@aar,androidx.compose.ui:ui-text-android:1.7.8@aar,androidx.compose.ui:ui-graphics-android:1.7.8@aar,androidx.compose.ui:ui-geometry-android:1.7.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar,androidx.compose.material:material-icons-extended:1.3.1@aar,androidx.compose.material:material-icons-core:1.3.1@aar,androidx.compose.ui:ui-android:1.7.8@aar,androidx.compose.runtime:runtime-saveable-android:1.7.8@aar,androidx.work:work-runtime:2.7.1@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.1.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.activity:activity-ktx:1.7.2@aar,androidx.activity:activity:1.7.2@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.webkit:webkit:1.4.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fresco:animated-gif:3.2.0@aar,com.facebook.fresco:webpsupport:3.2.0@aar,com.facebook.fresco:fresco:3.2.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar,com.facebook.fresco:animated-base:3.2.0@aar,com.facebook.fresco:animated-drawable:3.2.0@aar,com.facebook.fresco:vito-options:3.2.0@aar,com.facebook.fresco:drawee:3.2.0@aar,com.facebook.fresco:nativeimagefilters:3.2.0@aar,com.facebook.fresco:memory-type-native:3.2.0@aar,com.facebook.fresco:memory-type-java:3.2.0@aar,com.facebook.fresco:imagepipeline-native:3.2.0@aar,com.facebook.fresco:memory-type-ashmem:3.2.0@aar,com.facebook.fresco:imagepipeline:3.2.0@aar,com.facebook.fresco:nativeimagetranscoder:3.2.0@aar,com.facebook.fresco:imagepipeline-base:3.2.0@aar,com.facebook.fresco:middleware:3.2.0@aar,com.facebook.fresco:ui-common:3.2.0@aar,com.facebook.fresco:soloader:3.2.0@aar,com.facebook.fresco:fbcore:3.2.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.browser:browser:1.6.0@aar,androidx.transition:transition:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,com.google.android.exoplayer:exoplayer:2.18.1@aar,com.google.android.exoplayer:exoplayer-dash:2.18.1@aar,com.google.android.exoplayer:exoplayer-hls:2.18.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar,com.google.android.exoplayer:exoplayer-core:2.18.1@aar,androidx.viewpager:viewpager:1.0.0@aar,com.google.android.exoplayer:exoplayer-ui:2.18.1@aar,androidx.recyclerview:recyclerview:1.2.1@aar,androidx.recyclerview:recyclerview:1.2.1@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.media:media:1.4.3@aar,androidx.media:media:1.4.3@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.lifecycle:lifecycle-service:2.8.3@aar,androidx.lifecycle:lifecycle-process:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-common-java8:2.8.3@jar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar,androidx.compose.runtime:runtime-android:1.7.8@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.concurrent:concurrent-futures-ktx:1.1.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment-ktx:1.6.1@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.google.android.exoplayer:extension-okhttp:2.18.1@aar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,org.jetbrains.kotlin:kotlin-reflect:1.9.25@jar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,com.facebook.fresco:vito-renderer:3.2.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar,com.facebook.react:hermes-android:0.76.7:release@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.exifinterface:exifinterface:1.3.7@aar,androidx.room:room-runtime:2.2.5@aar,androidx.sqlite:sqlite-framework:2.1.0@aar,androidx.sqlite:sqlite:2.1.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar,com.google.android.exoplayer:exoplayer-database:2.18.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar,com.google.android.exoplayer:exoplayer-common:2.18.1@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.databinding:viewbinding:7.2.1@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.room:room-common:2.2.5@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.collection:collection-ktx:1.4.4@jar,androidx.collection:collection-jvm:1.4.4@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar,org.jetbrains:annotations:23.0.0@jar,com.tom-roush:pdfbox-android:1.8.10.3@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.facebook.fbjni:fbjni:0.6.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.soloader:soloader:0.12.1@aar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.parse.bolts:bolts-tasks:1.4.0@jar,com.facebook.soloader:nativeloader:0.12.1@jar,com.android.installreferrer:installreferrer:2.2@aar,commons-io:commons-io:2.6@jar,com.facebook.device.yearclass:yearclass:2.1.0@jar,commons-codec:commons-codec:1.10@jar,:@@:expo-location$io.nlopez.smartlocation-jetified-aar,me.leolin:ShortcutBadger:1.1.22@aar,org.bouncycastle:bcpkix-jdk15to18:1.68@jar,org.bouncycastle:bcprov-jdk15to18:1.68@jar,com.google.guava:guava:31.0.1-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.12.1@jar,com.google.auto.value:auto-value-annotations:1.6.3@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.guava:failureaccess:1.0.1@jar">
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="SellerApp:react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="SellerApp:react-native-safe-area-context"/>
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="SellerApp:react-native-screens"/>
    <dependency
        name=":@@:react-native-webview::release"
        simpleName="SellerApp:react-native-webview"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="SellerApp:react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:react-native-community_datetimepicker::release"
        simpleName="SellerApp:react-native-community_datetimepicker"/>
    <dependency
        name=":@@:react-native-get-random-values::release"
        simpleName="SellerApp:react-native-get-random-values"/>
    <dependency
        name=":@@:react-native-html-to-pdf::release"
        simpleName="SellerApp:react-native-html-to-pdf"/>
    <dependency
        name=":@@:react-native-maps::release"
        simpleName="SellerApp:react-native-maps"/>
    <dependency
        name=":@@:react-native-razorpay::release"
        simpleName="SellerApp:react-native-razorpay"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="SellerApp:react-native-reanimated"/>
    <dependency
        name=":@@:react-native-svg::release"
        simpleName="SellerApp:react-native-svg"/>
    <dependency
        name=":@@:react-native-view-shot::release"
        simpleName="SellerApp:react-native-view-shot"/>
    <dependency
        name=":@@:expo-application::release"
        simpleName="host.exp.exponent:expo-application"/>
    <dependency
        name=":@@:expo-asset::release"
        simpleName="expo.modules.asset:expo-asset"/>
    <dependency
        name=":@@:expo-av::release"
        simpleName="host.exp.exponent:expo-av"/>
    <dependency
        name=":@@:expo-camera::release"
        simpleName="host.exp.exponent:expo-camera"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name=":@@:expo-device::release"
        simpleName="host.exp.exponent:expo-device"/>
    <dependency
        name=":@@:expo-document-picker::release"
        simpleName="host.exp.exponent:expo-document-picker"/>
    <dependency
        name=":@@:expo-file-system::release"
        simpleName="host.exp.exponent:expo-file-system"/>
    <dependency
        name=":@@:expo-font::release"
        simpleName="host.exp.exponent:expo-font"/>
    <dependency
        name=":@@:expo-haptics::release"
        simpleName="host.exp.exponent:expo-haptics"/>
    <dependency
        name=":@@:expo-image-loader::release"
        simpleName="host.exp.exponent:expo-image-loader"/>
    <dependency
        name=":@@:expo-image-picker::release"
        simpleName="host.exp.exponent:expo-image-picker"/>
    <dependency
        name=":@@:expo-keep-awake::release"
        simpleName="host.exp.exponent:expo-keep-awake"/>
    <dependency
        name=":@@:expo-linear-gradient::release"
        simpleName="host.exp.exponent:expo-linear-gradient"/>
    <dependency
        name=":@@:expo-linking::release"
        simpleName="expo.modules.linking:expo-linking"/>
    <dependency
        name=":@@:expo-location::release"
        simpleName="host.exp.exponent:expo-location"/>
    <dependency
        name=":@@:expo-media-library::release"
        simpleName="host.exp.exponent:expo-media-library"/>
    <dependency
        name=":@@:expo-notifications::release"
        simpleName="host.exp.exponent:expo-notifications"/>
    <dependency
        name=":@@:expo-print::release"
        simpleName="host.exp.exponent:expo-print"/>
    <dependency
        name=":@@:expo-sharing::release"
        simpleName="host.exp.exponent:expo-sharing"/>
    <dependency
        name=":@@:expo-splash-screen::release"
        simpleName="host.exp.exponent:expo-splash-screen"/>
    <dependency
        name=":@@:expo-system-ui::release"
        simpleName="host.exp.exponent:expo-system-ui"/>
    <dependency
        name=":@@:expo-web-browser::release"
        simpleName="host.exp.exponent:expo-web-browser"/>
    <dependency
        name=":@@:my-module::release"
        simpleName="expo.modules.mymodule:my-module"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name="com.facebook.react:react-android:0.76.7:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.google.maps.android:android-maps-utils:3.8.2@aar"
        simpleName="com.google.maps.android:android-maps-utils"/>
    <dependency
        name="com.razorpay:checkout:1.6.41@aar"
        simpleName="com.razorpay:checkout"/>
    <dependency
        name="com.razorpay:standard-core:1.6.49@aar"
        simpleName="com.razorpay:standard-core"/>
    <dependency
        name="com.google.android.gms:play-services-wallet:18.1.3@aar"
        simpleName="com.google.android.gms:play-services-wallet"/>
    <dependency
        name="com.google.android.gms:play-services-maps:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-maps"/>
    <dependency
        name="com.google.android.material:material:1.6.1@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.camera:camera-mlkit-vision:1.4.1@aar"
        simpleName="androidx.camera:camera-mlkit-vision"/>
    <dependency
        name="androidx.camera:camera-extensions:1.4.1@aar"
        simpleName="androidx.camera:camera-extensions"/>
    <dependency
        name="androidx.camera:camera-video:1.4.1@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.4.1@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-camera2:1.4.1@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.4.1@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="androidx.camera:camera-view:1.4.1@aar"
        simpleName="androidx.camera:camera-view"/>
    <dependency
        name="com.github.CanHub:Android-Image-Cropper:4.3.1@aar"
        simpleName="com.github.CanHub:Android-Image-Cropper"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.core:core-splashscreen:1.2.0-alpha02@aar"
        simpleName="androidx.core:core-splashscreen"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.1.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.0.1@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.0.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.mlkit:barcode-scanning:17.2.0@aar"
        simpleName="com.google.mlkit:barcode-scanning"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-barcode-scanning"/>
    <dependency
        name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
        simpleName="com.google.mlkit:barcode-scanning-common"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.9.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-identity:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-identity"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.2.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended:1.3.1@aar"
        simpleName="androidx.compose.material:material-icons-extended"/>
    <dependency
        name="androidx.compose.material:material-icons-core:1.3.1@aar"
        simpleName="androidx.compose.material:material-icons-core"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.work:work-runtime:2.7.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.activity:activity-ktx:1.7.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.7.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.webkit:webkit:1.4.0@aar"
        simpleName="androidx.webkit:webkit"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.2.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.2.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.fresco:fresco:3.2.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:animated-base:3.2.0@aar"
        simpleName="com.facebook.fresco:animated-base"/>
    <dependency
        name="com.facebook.fresco:animated-drawable:3.2.0@aar"
        simpleName="com.facebook.fresco:animated-drawable"/>
    <dependency
        name="com.facebook.fresco:vito-options:3.2.0@aar"
        simpleName="com.facebook.fresco:vito-options"/>
    <dependency
        name="com.facebook.fresco:drawee:3.2.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.fresco:middleware:3.2.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.2.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:soloader:3.2.0@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.2.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.2.1@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.media:media:1.4.3@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.google.android.exoplayer:extension-okhttp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:extension-okhttp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="com.facebook.fresco:vito-renderer:3.2.0@aar"
        simpleName="com.facebook.fresco:vito-renderer"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="com.facebook.react:hermes-android:0.76.7:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.7@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.room:room-runtime:2.2.5@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.1.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.databinding:viewbinding:7.2.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.room:room-common:2.2.5@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.4@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.tom-roush:pdfbox-android:1.8.10.3@aar"
        simpleName="com.tom-roush:pdfbox-android"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.6.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.android.installreferrer:installreferrer:2.2@aar"
        simpleName="com.android.installreferrer:installreferrer"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
        simpleName="com.facebook.device.yearclass:yearclass"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name=":@@:expo-location$io.nlopez.smartlocation-jetified-aar"
        simpleName="artifacts::expo-location$io.nlopez.smartlocation-jetified-aar"/>
    <dependency
        name="me.leolin:ShortcutBadger:1.1.22@aar"
        simpleName="me.leolin:ShortcutBadger"/>
    <dependency
        name="org.bouncycastle:bcpkix-jdk15to18:1.68@jar"
        simpleName="org.bouncycastle:bcpkix-jdk15to18"/>
    <dependency
        name="org.bouncycastle:bcprov-jdk15to18:1.68@jar"
        simpleName="org.bouncycastle:bcprov-jdk15to18"/>
    <dependency
        name="com.google.guava:guava:31.0.1-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
  </package>
</dependencies>
