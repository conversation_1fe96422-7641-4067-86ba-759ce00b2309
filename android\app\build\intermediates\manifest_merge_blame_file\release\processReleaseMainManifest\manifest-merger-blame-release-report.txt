1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dhass.myapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
11-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:3:3-79
11-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:3:20-76
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:4:3-77
12-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:4:20-74
13    <uses-permission android:name="android.permission.CAMERA" />
13-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:5:3-63
13-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:5:20-60
14    <uses-permission android:name="android.permission.INTERNET" />
14-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:6:3-65
14-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:6:20-62
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:7:3-78
15-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:7:20-75
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:8:3-78
16-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:8:20-75
17    <uses-permission android:name="android.permission.RECORD_AUDIO" />
17-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:9:3-69
17-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:9:20-66
18    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
18-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:10:3-76
18-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:10:20-73
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:11:3-64
19-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:11:20-61
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:12:3-79
20-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:12:20-76
21
22    <queries>
22-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:13:3-19:13
23        <intent>
23-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:14:5-18:14
24            <action android:name="android.intent.action.VIEW" />
24-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:7-59
24-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:15-56
25
26            <category android:name="android.intent.category.BROWSABLE" />
26-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:16:7-68
26-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:16:17-65
27
28            <data android:scheme="https" />
28-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
28-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:13-35
29        </intent>
30        <!-- Query open documents -->
31        <intent>
31-->[:expo-document-picker] F:\Apps\Seller\my-app\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-17:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT" />
32-->[:expo-document-picker] F:\Apps\Seller\my-app\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
32-->[:expo-document-picker] F:\Apps\Seller\my-app\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-71
33
34            <category android:name="android.intent.category.DEFAULT" />
34-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:44:9-68
34-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:44:19-65
35            <category android:name="android.intent.category.OPENABLE" />
35-->[:expo-document-picker] F:\Apps\Seller\my-app\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-73
35-->[:expo-document-picker] F:\Apps\Seller\my-app\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:23-70
36
37            <data android:mimeType="*/*" />
37-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
38        </intent> <!-- Query open documents -->
39        <intent>
39-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
40            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
40-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
40-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
41        </intent>
42        <intent>
42-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-19:18
43
44            <!-- Required for picking images from the camera roll if targeting API 30 -->
45            <action android:name="android.media.action.IMAGE_CAPTURE" />
45-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-73
45-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:21-70
46        </intent>
47        <intent>
47-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-24:18
48
49            <!-- Required for picking images from the camera if targeting API 30 -->
50            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
50-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-80
50-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:21-77
51        </intent>
52        <intent>
52-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-14:18
53
54            <!-- Required for file sharing if targeting API 30 -->
55            <action android:name="android.intent.action.SEND" />
55-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-65
55-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-62
56
57            <data android:mimeType="*/*" />
57-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
58        </intent>
59        <intent>
59-->[:expo-web-browser] F:\Apps\Seller\my-app\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
60
61            <!-- Required for opening tabs if targeting API 30 -->
62            <action android:name="android.support.customtabs.action.CustomTabsService" />
62-->[:expo-web-browser] F:\Apps\Seller\my-app\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-90
62-->[:expo-web-browser] F:\Apps\Seller\my-app\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-87
63        </intent>
64        <intent>
64-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:11:9-17:18
65            <action android:name="android.intent.action.VIEW" />
65-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:7-59
65-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:15-56
66
67            <data
67-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
68                android:mimeType="*/*"
69                android:scheme="*" />
69-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:13-35
70        </intent>
71        <intent>
71-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:18:9-27:18
72            <action android:name="android.intent.action.VIEW" />
72-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:7-59
72-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:15-56
73
74            <category android:name="android.intent.category.BROWSABLE" />
74-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:16:7-68
74-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:16:17-65
75
76            <data
76-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
77                android:host="pay"
78                android:mimeType="*/*"
79                android:scheme="upi" />
79-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:13-35
80        </intent>
81        <intent>
81-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:28:9-30:18
82            <action android:name="android.intent.action.MAIN" />
82-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:39:9-61
82-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:39:17-58
83        </intent>
84        <intent>
84-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:36:9-38:18
85            <action android:name="rzp.device_token.share" />
85-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:37:13-61
85-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:37:21-58
86        </intent> <!-- Needs to be explicitly declared on Android R+ -->
87        <package android:name="com.google.android.apps.maps" />
87-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
87-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
88
89        <intent>
89-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
90            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
90-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
90-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
91        </intent>
92        <intent>
92-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
93            <action android:name="android.intent.action.GET_CONTENT" />
93-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
93-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
94
95            <category android:name="android.intent.category.OPENABLE" />
95-->[:expo-document-picker] F:\Apps\Seller\my-app\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-73
95-->[:expo-document-picker] F:\Apps\Seller\my-app\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:23-70
96
97            <data android:mimeType="*/*" />
97-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
98        </intent>
99    </queries>
100
101    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
101-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-76
101-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-73
102    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
102-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-75
102-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-72
103    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
103-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-75
103-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-72
104    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
104-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-90
104-->[:expo-media-library] F:\Apps\Seller\my-app\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-87
105    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
105-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
105-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
106    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Include required permissions for Google Maps API to run. -->
106-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
106-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
107    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
107-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
107-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
108
109    <uses-feature
109-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
110        android:glEsVersion="0x00020000"
110-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
111        android:required="true" />
111-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
112
113    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
113-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
113-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
114    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
114-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
114-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
115    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
115-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
115-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
116
117    <permission
117-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
118        android:name="com.dhass.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
118-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
119        android:protectionLevel="signature" />
119-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
120
121    <uses-permission android:name="com.dhass.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
121-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
121-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
122    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
122-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61dfe7560f9e3a6572f85e66e02165eb\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
122-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61dfe7560f9e3a6572f85e66e02165eb\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
123    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
123-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:5-98
123-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:22-95
124    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" /> <!-- for android -->
124-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:5-94
124-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:22-91
125    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
126    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
127    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
128    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
129    <!-- for Samsung -->
130    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
130-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
130-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
131    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
131-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
131-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
132    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
132-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
132-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
133    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
133-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
133-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
134    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
134-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
134-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
135    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
136    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
137    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
138    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
139    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
140    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
141    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
142    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
143    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
144    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
145    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
146
147    <application
147-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:20:3-50:17
148        android:name="com.dhass.myapp.MainApplication"
148-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:20:16-47
149        android:allowBackup="false"
149-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:22:5-32
150        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
150-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
151        android:extractNativeLibs="false"
152        android:fullBackupContent="false"
152-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:22:33-66
153        android:icon="@mipmap/ic_launcher"
153-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:21:5-39
154        android:label="@string/app_name"
154-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:20:48-80
155        android:requestLegacyExternalStorage="true"
155-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:24:5-48
156        android:roundIcon="@mipmap/ic_launcher_round"
156-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:21:40-85
157        android:supportsRtl="true"
157-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:23:5-31
158        android:theme="@style/AppTheme"
158-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:22:67-98
159        android:usesCleartextTraffic="true" >
159-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:24:49-84
160        <meta-data
160-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:25:5-27:65
161            android:name="com.google.android.geo.API_KEY"
161-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:26:7-52
162            android:value="AIzaSyCg62vT6rJj3WMmQIBcI2iXPwXdmk6QLU4" />
162-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:27:7-62
163        <meta-data
163-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:29:5-84
164            android:name="expo.modules.updates.ENABLED"
164-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:29:16-59
165            android:value="false" />
165-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:29:60-81
166        <meta-data
166-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:30:5-31:32
167            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
167-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:30:16-80
168            android:value="ALWAYS" />
168-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:31:7-29
169        <meta-data
169-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:32:5-100
170            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
170-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:32:16-79
171            android:value="0" />
171-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:32:80-97
172
173        <activity
173-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:33:5-49:16
174            android:name="com.dhass.myapp.MainActivity"
174-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:33:15-43
175            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
175-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:34:7-97
176            android:exported="true"
176-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:36:53-76
177            android:launchMode="singleTask"
177-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:35:7-38
178            android:screenOrientation="portrait"
178-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:37:7-43
179            android:theme="@style/Theme.App.SplashScreen"
179-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:36:7-52
180            android:windowSoftInputMode="adjustResize" >
180-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:35:39-81
181            <intent-filter>
181-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:38:7-41:23
182                <action android:name="android.intent.action.MAIN" />
182-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:39:9-61
182-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:39:17-58
183
184                <category android:name="android.intent.category.LAUNCHER" />
184-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:40:9-69
184-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:40:19-66
185            </intent-filter>
186            <intent-filter>
186-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:42:7-48:23
187                <action android:name="android.intent.action.VIEW" />
187-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:7-59
187-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:15:15-56
188
189                <category android:name="android.intent.category.DEFAULT" />
189-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:44:9-68
189-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:44:19-65
190                <category android:name="android.intent.category.BROWSABLE" />
190-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:16:7-68
190-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:16:17-65
191
192                <data android:scheme="myapp" />
192-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
192-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:13-35
193                <data android:scheme="com.dhass.myapp" />
193-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:7-38
193-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:17:13-35
194            </intent-filter>
195        </activity>
196
197        <provider
197-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
198            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
198-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-83
199            android:authorities="com.dhass.myapp.fileprovider"
199-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-64
200            android:exported="false"
200-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
201            android:grantUriPermissions="true" >
201-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
202            <meta-data
202-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
203                android:name="android.support.FILE_PROVIDER_PATHS"
203-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
204                android:resource="@xml/file_provider_paths" />
204-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
205        </provider>
206
207        <activity
207-->[:react-native-razorpay] F:\Apps\Seller\my-app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:86
208            android:name="com.razorpay.CheckoutActivity"
208-->[:react-native-razorpay] F:\Apps\Seller\my-app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-57
209            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
209-->[:react-native-razorpay] F:\Apps\Seller\my-app\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-83
210            android:exported="false"
210-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:54:13-37
211            android:theme="@style/CheckoutTheme" >
211-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:55:13-49
212            <intent-filter>
212-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:56:13-58:29
213                <action android:name="android.intent.action.MAIN" />
213-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:39:9-61
213-->F:\Apps\Seller\my-app\android\app\src\main\AndroidManifest.xml:39:17-58
214            </intent-filter>
215        </activity>
216
217        <provider
217-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
218            android:name="expo.modules.filesystem.FileSystemFileProvider"
218-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
219            android:authorities="com.dhass.myapp.FileSystemFileProvider"
219-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
220            android:exported="false"
220-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
221            android:grantUriPermissions="true" >
221-->[:expo-file-system] F:\Apps\Seller\my-app\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
222            <meta-data
222-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
223                android:name="android.support.FILE_PROVIDER_PATHS"
223-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
224                android:resource="@xml/file_system_provider_paths" />
224-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
225        </provider>
226
227        <service
227-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-40:19
228            android:name="com.google.android.gms.metadata.ModuleDependencies"
228-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-78
229            android:enabled="false"
229-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-36
230            android:exported="false" >
230-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-37
231            <intent-filter>
231-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-35:29
232                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
232-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:17-94
232-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:25-91
233            </intent-filter>
234
235            <meta-data
235-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:36
236                android:name="photopicker_activity:0:required"
236-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-63
237                android:value="" />
237-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:17-33
238        </service>
239
240        <activity
240-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:59
241            android:name="com.canhub.cropper.CropImageActivity"
241-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-64
242            android:exported="true"
242-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
243            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
243-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-56
244        <provider
244-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-54:20
245            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
245-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-89
246            android:authorities="com.dhass.myapp.ImagePickerFileProvider"
246-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-75
247            android:exported="false"
247-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-37
248            android:grantUriPermissions="true" >
248-->[:expo-image-picker] F:\Apps\Seller\my-app\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-47
249            <meta-data
249-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
250                android:name="android.support.FILE_PROVIDER_PATHS"
250-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
251                android:resource="@xml/image_picker_provider_paths" />
251-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
252        </provider>
253
254        <service
254-->[:expo-location] F:\Apps\Seller\my-app\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-14:56
255            android:name="expo.modules.location.services.LocationTaskService"
255-->[:expo-location] F:\Apps\Seller\my-app\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-78
256            android:exported="false"
256-->[:expo-location] F:\Apps\Seller\my-app\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
257            android:foregroundServiceType="location" />
257-->[:expo-location] F:\Apps\Seller\my-app\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-53
258        <service
258-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
259            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
259-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
260            android:exported="false" >
260-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
261            <intent-filter android:priority="-1" >
261-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
261-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
262                <action android:name="com.google.firebase.MESSAGING_EVENT" />
262-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
262-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
263            </intent-filter>
264        </service>
265
266        <receiver
266-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
267            android:name="expo.modules.notifications.service.NotificationsService"
267-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
268            android:enabled="true"
268-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
269            android:exported="false" >
269-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
270            <intent-filter android:priority="-1" >
270-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
270-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
271                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
271-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
271-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
272                <action android:name="android.intent.action.BOOT_COMPLETED" />
272-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
272-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
273                <action android:name="android.intent.action.REBOOT" />
273-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
273-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
274                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
274-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
274-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
275                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
275-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
275-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
276                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
276-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
276-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
277            </intent-filter>
278        </receiver>
279
280        <activity
280-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
281            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
281-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
282            android:excludeFromRecents="true"
282-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
283            android:exported="false"
283-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
284            android:launchMode="standard"
284-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
285            android:noHistory="true"
285-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
286            android:taskAffinity=""
286-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
287            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
287-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
288
289        <provider
289-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-26:20
290            android:name="expo.modules.sharing.SharingFileProvider"
290-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-68
291            android:authorities="com.dhass.myapp.SharingFileProvider"
291-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-71
292            android:exported="false"
292-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
293            android:grantUriPermissions="true" >
293-->[:expo-sharing] F:\Apps\Seller\my-app\node_modules\expo-sharing\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-47
294            <meta-data
294-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
295                android:name="android.support.FILE_PROVIDER_PATHS"
295-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
296                android:resource="@xml/sharing_provider_paths" />
296-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
297        </provider>
298
299        <meta-data
299-->[:expo-modules-core] F:\Apps\Seller\my-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
300            android:name="org.unimodules.core.AppLoader#react-native-headless"
300-->[:expo-modules-core] F:\Apps\Seller\my-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
301            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
301-->[:expo-modules-core] F:\Apps\Seller\my-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
302        <meta-data
302-->[:expo-modules-core] F:\Apps\Seller\my-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
303            android:name="com.facebook.soloader.enabled"
303-->[:expo-modules-core] F:\Apps\Seller\my-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
304            android:value="true" />
304-->[:expo-modules-core] F:\Apps\Seller\my-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
305        <meta-data
305-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c95cb72c41fea165ef66e03e2027abf\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
306            android:name="com.google.android.gms.version"
306-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c95cb72c41fea165ef66e03e2027abf\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
307            android:value="@integer/google_play_services_version" />
307-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c95cb72c41fea165ef66e03e2027abf\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
308
309        <receiver
309-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:42:9-49:20
310            android:name="com.razorpay.RzpTokenReceiver"
310-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:43:13-57
311            android:exported="true" >
311-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:44:13-36
312            <intent-filter>
312-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:46:13-48:29
313                <action android:name="rzp.device_token.share" />
313-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:37:13-61
313-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:37:21-58
314            </intent-filter>
315        </receiver>
316
317        <provider
317-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:61:9-69:20
318            android:name="androidx.startup.InitializationProvider"
318-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:62:13-67
319            android:authorities="com.dhass.myapp.androidx-startup"
319-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:63:13-68
320            android:exported="false" >
320-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:64:13-37
321            <meta-data
321-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:66:13-68:52
322                android:name="com.razorpay.RazorpayInitializer"
322-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:67:17-64
323                android:value="androidx.startup" />
323-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:68:17-49
324            <meta-data
324-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
325                android:name="androidx.work.WorkManagerInitializer"
325-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
326                android:value="androidx.startup" />
326-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
327            <meta-data
327-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
328                android:name="androidx.emoji2.text.EmojiCompatInitializer"
328-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
329                android:value="androidx.startup" />
329-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
330            <meta-data
330-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943471b0f40b86fe6327d29c4f536927\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
331                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
331-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943471b0f40b86fe6327d29c4f536927\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
332                android:value="androidx.startup" />
332-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943471b0f40b86fe6327d29c4f536927\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
333            <meta-data
333-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
334                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
334-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
335                android:value="androidx.startup" />
335-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
336        </provider>
337
338        <activity
338-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:71:9-74:75
339            android:name="com.razorpay.MagicXActivity"
339-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:72:13-55
340            android:exported="false"
340-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:73:13-37
341            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
341-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:74:13-72
342
343        <meta-data
343-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:76:9-78:58
344            android:name="com.razorpay.plugin.googlepay_all"
344-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:77:13-61
345            android:value="com.razorpay.RzpGpayMerged" /> <!-- Needs to be explicitly declared on P+ -->
345-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\AndroidManifest.xml:78:13-55
346        <uses-library
346-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
347            android:name="org.apache.http.legacy"
347-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
348            android:required="false" />
348-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15c52ba8da6d8731c13a73322f2291d6\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
349        <uses-library
349-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
350            android:name="androidx.camera.extensions.impl"
350-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
351            android:required="false" />
351-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
352
353        <service
353-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
354            android:name="androidx.camera.core.impl.MetadataHolderService"
354-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
355            android:enabled="false"
355-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
356            android:exported="false" >
356-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
357            <meta-data
357-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
358                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
358-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
359                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
359-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
360        </service>
361
362        <provider
362-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
363            android:name="com.canhub.cropper.CropFileProvider"
363-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
364            android:authorities="com.dhass.myapp.cropper.fileprovider"
364-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
365            android:exported="false"
365-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
366            android:grantUriPermissions="true" >
366-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
367            <meta-data
367-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:63
368                android:name="android.support.FILE_PROVIDER_PATHS"
368-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-67
369                android:resource="@xml/library_file_paths" />
369-->[:react-native-webview] F:\Apps\Seller\my-app\node_modules\react-native-webview\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-60
370        </provider>
371
372        <activity
372-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
373            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
373-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
374            android:excludeFromRecents="true"
374-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
375            android:exported="false"
375-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
376            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
376-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
377        <!--
378            Service handling Google Sign-In user revocation. For apps that do not integrate with
379            Google Sign-In, this service will never be started.
380        -->
381        <service
381-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
382            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
382-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
383            android:exported="true"
383-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
384            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
384-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
385            android:visibleToInstantApps="true" />
385-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
386
387        <receiver
387-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
388            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
388-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
389            android:exported="true"
389-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
390            android:permission="com.google.android.c2dm.permission.SEND" >
390-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
391            <intent-filter>
391-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
392                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
392-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
392-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
393            </intent-filter>
394
395            <meta-data
395-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
396                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
396-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
397                android:value="true" />
397-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
398        </receiver>
399        <!--
400             FirebaseMessagingService performs security checks at runtime,
401             but set to not exported to explicitly avoid allowing another app to call it.
402        -->
403        <service
403-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
404            android:name="com.google.firebase.messaging.FirebaseMessagingService"
404-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
405            android:directBootAware="true"
405-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
406            android:exported="false" >
406-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
407            <intent-filter android:priority="-500" >
407-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
407-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
408                <action android:name="com.google.firebase.MESSAGING_EVENT" />
408-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
408-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
409            </intent-filter>
410        </service>
411        <service
411-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
412            android:name="com.google.firebase.components.ComponentDiscoveryService"
412-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
413            android:directBootAware="true"
413-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
414            android:exported="false" >
414-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
415            <meta-data
415-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
416                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
416-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
417                android:value="com.google.firebase.components.ComponentRegistrar" />
417-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
418            <meta-data
418-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
419                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
419-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
420                android:value="com.google.firebase.components.ComponentRegistrar" />
420-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
421            <meta-data
421-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
422                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
422-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
423                android:value="com.google.firebase.components.ComponentRegistrar" />
423-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
424            <meta-data
424-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
425                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
425-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
426                android:value="com.google.firebase.components.ComponentRegistrar" />
426-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
427            <meta-data
427-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a236b19643a27143e8e909f6de03f9a9\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
428                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
428-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a236b19643a27143e8e909f6de03f9a9\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
429                android:value="com.google.firebase.components.ComponentRegistrar" />
429-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a236b19643a27143e8e909f6de03f9a9\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
430            <meta-data
430-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
431                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
431-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
432                android:value="com.google.firebase.components.ComponentRegistrar" />
432-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
433            <meta-data
433-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d67bec726af004a41316ce88006c28\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
434                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
434-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d67bec726af004a41316ce88006c28\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
435                android:value="com.google.firebase.components.ComponentRegistrar" />
435-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d67bec726af004a41316ce88006c28\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
436        </service>
437        <service
437-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
438            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
438-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
439            android:directBootAware="true"
439-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
440            android:exported="false" >
440-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
441            <meta-data
441-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
442                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
442-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
443                android:value="com.google.firebase.components.ComponentRegistrar" />
443-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
444            <meta-data
444-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67b00141bd7072ce6b29945fe7bd3e1e\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
445                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
445-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67b00141bd7072ce6b29945fe7bd3e1e\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
446                android:value="com.google.firebase.components.ComponentRegistrar" />
446-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67b00141bd7072ce6b29945fe7bd3e1e\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
447            <meta-data
447-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
448                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
448-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
449                android:value="com.google.firebase.components.ComponentRegistrar" />
449-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
450        </service>
451
452        <provider
452-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
453            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
453-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
454            android:authorities="com.dhass.myapp.mlkitinitprovider"
454-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
455            android:exported="false"
455-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
456            android:initOrder="99" />
456-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
457
458        <activity
458-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5d2036565c74c4e638e3487ea5ab118\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
459            android:name="com.google.android.gms.common.api.GoogleApiActivity"
459-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5d2036565c74c4e638e3487ea5ab118\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
460            android:exported="false"
460-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5d2036565c74c4e638e3487ea5ab118\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
461            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
461-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5d2036565c74c4e638e3487ea5ab118\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
462
463        <provider
463-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
464            android:name="com.google.firebase.provider.FirebaseInitProvider"
464-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
465            android:authorities="com.dhass.myapp.firebaseinitprovider"
465-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
466            android:directBootAware="true"
466-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
467            android:exported="false"
467-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
468            android:initOrder="100" />
468-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
469
470        <service
470-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
471            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
471-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
472            android:directBootAware="false"
472-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
473            android:enabled="@bool/enable_system_alarm_service_default"
473-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
474            android:exported="false" />
474-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
475        <service
475-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
476            android:name="androidx.work.impl.background.systemjob.SystemJobService"
476-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
477            android:directBootAware="false"
477-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
478            android:enabled="@bool/enable_system_job_service_default"
478-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
479            android:exported="true"
479-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
480            android:permission="android.permission.BIND_JOB_SERVICE" />
480-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
481        <service
481-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
482            android:name="androidx.work.impl.foreground.SystemForegroundService"
482-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
483            android:directBootAware="false"
483-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
484            android:enabled="@bool/enable_system_foreground_service_default"
484-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
485            android:exported="false" />
485-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
486
487        <receiver
487-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
488            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
488-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
489            android:directBootAware="false"
489-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
490            android:enabled="true"
490-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
491            android:exported="false" />
491-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
492        <receiver
492-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
493            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
493-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
494            android:directBootAware="false"
494-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
495            android:enabled="false"
495-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
496            android:exported="false" >
496-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
497            <intent-filter>
497-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
498                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
498-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
498-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
499                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
499-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
499-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
500            </intent-filter>
501        </receiver>
502        <receiver
502-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
503            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
503-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
504            android:directBootAware="false"
504-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
505            android:enabled="false"
505-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
506            android:exported="false" >
506-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
507            <intent-filter>
507-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
508                <action android:name="android.intent.action.BATTERY_OKAY" />
508-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
508-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
509                <action android:name="android.intent.action.BATTERY_LOW" />
509-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
509-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
510            </intent-filter>
511        </receiver>
512        <receiver
512-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
513            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
513-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
514            android:directBootAware="false"
514-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
515            android:enabled="false"
515-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
516            android:exported="false" >
516-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
517            <intent-filter>
517-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
518                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
518-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
518-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
519                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
519-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
519-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
520            </intent-filter>
521        </receiver>
522        <receiver
522-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
523            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
523-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
524            android:directBootAware="false"
524-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
525            android:enabled="false"
525-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
526            android:exported="false" >
526-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
527            <intent-filter>
527-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
528                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
528-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
528-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
529            </intent-filter>
530        </receiver>
531        <receiver
531-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
532            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
532-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
533            android:directBootAware="false"
533-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
534            android:enabled="false"
534-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
535            android:exported="false" >
535-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
536            <intent-filter>
536-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
537                <action android:name="android.intent.action.BOOT_COMPLETED" />
537-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
537-->[:expo-notifications] F:\Apps\Seller\my-app\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
538                <action android:name="android.intent.action.TIME_SET" />
538-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
538-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
539                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
539-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
539-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
540            </intent-filter>
541        </receiver>
542        <receiver
542-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
543            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
543-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
544            android:directBootAware="false"
544-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
545            android:enabled="@bool/enable_system_alarm_service_default"
545-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
546            android:exported="false" >
546-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
547            <intent-filter>
547-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
548                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
548-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
548-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
549            </intent-filter>
550        </receiver>
551        <receiver
551-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
552            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
552-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
553            android:directBootAware="false"
553-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
554            android:enabled="true"
554-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
555            android:exported="true"
555-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
556            android:permission="android.permission.DUMP" >
556-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
557            <intent-filter>
557-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
558                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
558-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
558-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
559            </intent-filter>
560        </receiver>
561        <receiver
561-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
562            android:name="androidx.profileinstaller.ProfileInstallReceiver"
562-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
563            android:directBootAware="false"
563-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
564            android:enabled="true"
564-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
565            android:exported="true"
565-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
566            android:permission="android.permission.DUMP" >
566-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
567            <intent-filter>
567-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
568                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
568-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
568-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
569            </intent-filter>
570            <intent-filter>
570-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
571                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
571-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
571-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
572            </intent-filter>
573            <intent-filter>
573-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
574                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
574-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
574-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
575            </intent-filter>
576            <intent-filter>
576-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
577                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
577-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
577-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
578            </intent-filter>
579        </receiver>
580
581        <service
581-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e4932253ff5ff77949c81309e9f693\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
582            android:name="androidx.room.MultiInstanceInvalidationService"
582-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e4932253ff5ff77949c81309e9f693\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
583            android:directBootAware="true"
583-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e4932253ff5ff77949c81309e9f693\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
584            android:exported="false" />
584-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e4932253ff5ff77949c81309e9f693\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
585        <service
585-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
586            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
586-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
587            android:exported="false" >
587-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
588            <meta-data
588-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
589                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
589-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
590                android:value="cct" />
590-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
591        </service>
592        <service
592-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
593            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
593-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
594            android:exported="false"
594-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
595            android:permission="android.permission.BIND_JOB_SERVICE" >
595-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
596        </service>
597
598        <receiver
598-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
599            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
599-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
600            android:exported="false" />
600-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
601
602        <service
602-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:19:9-21:40
603            android:name="io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService"
603-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:20:13-132
604            android:exported="false" />
604-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:21:13-37
605        <service
605-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:22:9-24:40
606            android:name="io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService"
606-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:23:13-127
607            android:exported="false" />
607-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:24:13-37
608        <service
608-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:25:9-27:40
609            android:name="io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService"
609-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:26:13-120
610            android:exported="false" />
610-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:27:13-37
611    </application>
612
613</manifest>
