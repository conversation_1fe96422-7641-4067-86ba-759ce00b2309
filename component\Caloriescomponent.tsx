import PIcon from "../assets/icons/%.svg";
import React, { useEffect, useState } from "react";
import SmallPressable from "./SmallPressable";
import useSetApiData from "../hooks/useSetApiData";
import useTenStack from "@/hook/TenStackHook/useTenStack";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import { Controller, useForm } from "react-hook-form";
import { Text, View } from "react-native";
import { TextInput } from "react-native";
import { NutritionalComponent } from "../app/home/<USER>/product/page2";

const CaloriesComponent = (props: any) => {
  const {mutate: AddproductNutritions} = useTenStackMutateD({
    endpoint: "auth/productNutritions",
    invalidateQueriesKey: ["productDetails"],
  });
  const [NutritionalList, setNutritionalList] = useState([]);
  const {data, isLoading} = useTenStack({
    endpoint: "/auth/productDetails",
    pram: {product_id: props.id.id},
    key: "productDetails",
    id: [props.id.id.toString()],
    refetch: true,
  });
  console.log(JSON.stringify(data, null, 2));
  console.log(NutritionalList);
  useEffect(() => {
    if (!isLoading) {
      data?.data?.product_nutritions?.map((items: any, index: number) => {
        setNutritionalList((state) => {
          return [
            ...state,
            <NutritionalComponent
              control={props?.control}
              errors={props?.errors}
              name1={`nutrient_${index}`}
              name2={`quantity_${index}`}
            />,
          ];
        });
        props.setValue("kcal", items?.no_kcal);
        props.setValue(`nutrient_${index}`, items.nutrient);
        props.setValue(`quantity_${index}`, items.quantity);
      });
    }
  }, [isLoading]);

  useEffect(() => {
    return () => {
      props.handleSubmit((data: any) => {
        console.log(JSON.stringify(data, null, 2));
        console.log(NutritionalList.length, "length");
        const list: {
          product_id: any;
          neutrition_data: {no_kcal: any; nutrient: any; quantity: any}[];
        } = {product_id: props.id.id, neutrition_data: []};
        NutritionalList.map((items, index) => {
          list.neutrition_data.push({
            no_kcal: data.kcal,
            nutrient: data[`nutrient_${index}`],
            quantity: data[`quantity_${index}`],
          });
        });
        console.log(JSON.stringify(list, null, 2));
        AddproductNutritions(list);
      })();
    };
  }, [NutritionalList]);

  return (
    <View className="mt-4">
      <Text>Calories</Text>
      <View className="flex-row items-center">
        <Controller
          name="kcal"
          rules={{
            pattern: {
              value: /^\d+(\.\d{1,2})?$/,
              message: "Please enter quantity in format XX.X (e.g., 25.5)",
            },
          }}
          control={props?.control}
          render={({field: {onChange, value}}) => {
            return (
              <>
                <TextInput
                  onChangeText={onChange}
                  value={value}
                  keyboardType="numeric"
                  placeholder="Enter Number of Calories in the product"
                  placeholderTextColor={"#B3B3B3"}
                  className="border-[1px] border-[#ACB9D5] flex-1 rounded-[4px] pl-2 mt-2 h-[40px]"
                />
              </>
            );
          }}
        />
        <View className="ml-2">
          <Text>Kcal </Text>
        </View>
      </View>
      {props?.errors?.kcal && <Text className="text-red-500">{props?.errors?.kcal?.message}</Text>}
      <View className="flex-row space-x-4 items-center"></View>
      {NutritionalList.map((items) => {
        return items;
      })}
      <View className="mt-4">
        <SmallPressable
          style={{width: 260}}
          text={"Add Nutritional Information"}
          pressfun={() => {
            setNutritionalList((state) => {
              return [
                ...state,
                <NutritionalComponent
                  control={props?.control}
                  key={NutritionalList.length}
                  name1={`nutrient_${NutritionalList.length}`}
                  name2={`quantity_${NutritionalList.length}`}
                />,
              ];
            });
          }}
        />
      </View>
    </View>
  );
};

export default CaloriesComponent;
