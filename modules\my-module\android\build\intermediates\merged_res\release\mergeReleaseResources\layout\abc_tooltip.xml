<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <TextView
        android:id="@+id/message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/tooltip_margin"
        android:paddingLeft="@dimen/tooltip_horizontal_padding"
        android:paddingStart="@dimen/tooltip_horizontal_padding"
        android:paddingRight="@dimen/tooltip_horizontal_padding"
        android:paddingEnd="@dimen/tooltip_horizontal_padding"
        android:paddingTop="@dimen/tooltip_vertical_padding"
        android:paddingBottom="@dimen/tooltip_vertical_padding"
        android:maxWidth="256dp"
        android:background="?attr/tooltipFrameBackground"
        android:textAppearance="@style/TextAppearance.AppCompat.Tooltip"
        android:textColor="?attr/tooltipForegroundColor"
        android:maxLines="3"
        android:ellipsize="end"
    />

</LinearLayout>
