@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HF:\\Apps\\Seller\\my-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=x86" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\Apps\\Seller\\my-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\4n1l656s\\obj\\x86" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\Apps\\Seller\\my-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\4n1l656s\\obj\\x86" ^
  "-DCMAKE_BUILD_TYPE=RelWithDebInfo" ^
  "-DCMAKE_FIND_ROOT_PATH=F:\\Apps\\Seller\\my-app\\android\\app\\.cxx\\RelWithDebInfo\\4n1l656s\\prefab\\x86\\prefab" ^
  "-BF:\\Apps\\Seller\\my-app\\android\\app\\.cxx\\RelWithDebInfo\\4n1l656s\\x86" ^
  -GNinja ^
  "-DPROJECT_BUILD_DIR=F:\\Apps\\Seller\\my-app\\android\\app\\build" ^
  "-DREACT_ANDROID_DIR=F:\\Apps\\Seller\\my-app\\node_modules\\react-native\\ReactAndroid" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON"
