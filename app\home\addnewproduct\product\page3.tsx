import AddproductField from "../../../../component/AddproductField";
import Checkbox from "expo-checkbox";
import CustomFiled from "../../../../component/CustomFiled";
import DropDownComponent from "../../../../component/DropDownComponent";
import OnOffButton from "../../../../component/OnOffButton";
import React, {useContext, useEffect, useState} from "react";
import TextFieldwithDrop from "../../../../component/TextFieldwithDrop";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import {useRoute} from "@react-navigation/native";
import {router, useLocalSearchParams} from "expo-router";
import {Controller, useForm} from "react-hook-form";
import {Alert, Text, TextInput, TouchableOpacity, View} from "react-native";
import {useUpdateUserProfileMsg} from "../../../../store/Massage";
import {GlobalPagenumber} from "./_layout";

// import RsIcon from "@/assets/icons/₹Icon.svg";

const page3 = () => {
  const {id} = useLocalSearchParams();
  const {page, setpage} = useContext(GlobalPagenumber);

  const [IsSlash, setIsSlash] = useState(false);
  const [ActualAmount, setActualAmount] = useState("400");
  const [SlashAmount, setSlashAmount] = useState("350");

  const [DoYouAcceptReturn, setDoYouAcceptReturn] = useState(false);

  const [discount, setdiscount] = useState(false);
  const [isFreeDelivery, setIsFreeDelivery] = useState(false);

  const [select1, setselect1] = useState(false);
  const [select2, setselect2] = useState(false);
  const [select3, setselect3] = useState(false);

  const [Return, setReturn] = useState(false);
  const [Refund, setRefund] = useState(false);
  const [Replacement, setReplacement] = useState(false);
  const [ReplaceType, setReplaceType] = useState("Return");

  const {control, setValue, handleSubmit} = useForm();

  const {setMassage, setShowMassage} = useUpdateUserProfileMsg((state) => state);

  const {mutate: productPromotions} = useTenStackMutateD({
    endpoint: "auth/productPromotions",
    invalidateQueriesKey: [],
  });
  const route = useRoute();

  useEffect(() => {
    setpage(route.name);
  }, []);

  const changeState = (name) => {
    setReplaceType(name);
  };
  return (
    <View className="mt-4">
      <View>
        <Text className="font-[400] text-[18px]">Offers and Discounts</Text>
      </View>

      <View className="flex-row items-center justify-between mt-3">
        <View className="flex-shrink">
          <Text className="font-[400] text-[16px] leading-[20px]">Add free delivery ?</Text>
        </View>
        <View className="">
          <OnOffButton setTaxables={setIsFreeDelivery} disabled={true} />
        </View>
      </View>
      {isFreeDelivery && (
        <>
          <View className="mt-4">
            <DeliveryComponent control={control} />
          </View>
        </>
      )}
      <View className="mt-7">
        <Text className="font-[400] text-[18px] text-[#001A03] leading-[27px]">
          Return and Refund Policy
        </Text>
      </View>
      {/* <View className="flex-row items-center justify-between mt-5">
        <View className="flex-shrink">
          <Text className="font-[400] text-[16px] leading-[20px]">
            Do you accept return and refund for this product?
          </Text>
        </View>
        <View className="">
          <OnOffButton
            val={DoYouAcceptReturn}
            setTaxables={setDoYouAcceptReturn}
          />
        </View>
      </View> */}
      {/* {DoYouAcceptReturn ? ( */}
      {/* <> */}
      <TouchableOpacity
        onPress={() => {
          setReturn((staet) => !staet);
          setReplaceType("Return");
        }}
        className="flex-row items-center space-x-2 mt-2"
      >
        <Checkbox
          value={ReplaceType === "Return" ? true : false}
          style={{borderRadius: 100}}
          onValueChange={(val) => {
            setReturn(val);
            setReplaceType("Return");
          }}
        />
        <Text className="font-[400] text-[16px] leading-[24px] text-[#627164]">Return</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          setRefund((staet) => !staet);
          setReplaceType("Refund");
        }}
        className="flex-row items-center space-x-2 mt-2"
      >
        <Checkbox
          value={ReplaceType === "Refund" ? true : false}
          style={{borderRadius: 100}}
          onValueChange={(val) => {
            setRefund(val);
            setReplaceType("Refund");
          }}
        />
        <Text className="font-[400] text-[16px] leading-[24px] text-[#627164]">Refund</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          setReplacement((staet) => !staet);
          setReplaceType("Replacement");
        }}
        className="flex-row items-center space-x-2 mt-2"
      >
        <Checkbox
          value={ReplaceType === "Replacement" ? true : false}
          style={{borderRadius: 100}}
          onValueChange={(val) => {
            setReplacement(val);
            setReplaceType("Replacement");
          }}
        />
        <Text className="font-[400] text-[16px] leading-[24px] text-[#627164]">Replacement</Text>
      </TouchableOpacity>
      {/* </> */}
      {/* ) : (
        <></>
      )} */}

      <View>
        <DropDownComponent
          control={control}
          setvaluefun={setValue}
          name={"coversdelivery"}
          data={[
            {label: "OrdaLane", value: "1"},
            // {label: "Seller", value: "2"},
          ]}
          text={"Who covers delivery for you ?"}
          placeholder={"Select Options"}
          defaul={"OrdaLane"}
        />
      </View>
      <View>
        <DropDownComponent
          control={control}
          setvaluefun={setValue}
          name={"gateway"}
          data={[
            {label: "Customer", value: "1"},
            {label: "Seller", value: "2"},
          ]}
          text={"Payment GateWay charges paid by ?"}
          placeholder={"Select Options"}
          defaul={"Customer"}
        />
      </View>
      <View>
        <DropDownComponent
          control={control}
          setvaluefun={setValue}
          name={"platformfees"}
          data={[
            {label: "Customer", value: "1"},
            {label: "Seller", value: "2"},
          ]}
          text={"platform fees will be covered by ?"}
          placeholder={"Select Options"}
          defaul={"Customer"}
        />
      </View>
      {/* <View>
        <CustomFiled
          text={"What is the time duration for a refund ?"}
          placeholder={"Enter time"}
          unitsplace={"Select"}
          data={[
            { label: "min", value: "1" },
            { label: "hours", value: "2" },
          ]}
          control={control}
        />
      </View> */}
      <View className="mt-4">
        <Text className="font-[400] text-[16px] text-[#001A03] leading-[24px]">
          What are the conditions that must be met to issue a refund ?{" "}
        </Text>
      </View>
      <View className="mt-2 pl-1">
        <TouchableOpacity
          onPress={() => {
            setselect1(!select1);
          }}
          className="flex-row items-center space-x-2"
        >
          <Checkbox
            value={select1}
            onValueChange={(val) => {
              setselect1(val);
            }}
          />
          <Text className="font-[400] text-[16px] leading-[24px] text-[#627164]">
            Product must be returned in its original packaging
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            setselect2(!select2);
          }}
          className="flex-row items-center space-x-2 mt-2"
        >
          <Checkbox
            value={select2}
            onValueChange={(val) => {
              setselect2(val);
            }}
          />
          <Text className="font-[400] text-[16px] leading-[24px] text-[#627164]">
            Product isn’t used or damaged
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            setselect3(!select3);
          }}
          className="flex-row items-center space-x-2 mt-2"
        >
          <Checkbox
            value={select3}
            onValueChange={(val) => {
              setselect3(val);
            }}
          />
          <Text className="font-[400] text-[16px] leading-[24px] text-[#627164]">
            Product must have the receipt or proof of purchase
          </Text>
        </TouchableOpacity>
      </View>
      <View className="mt-10">
        <TouchableOpacity
          className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
          onPress={handleSubmit((datas) => {
            productPromotions(
              {
                product_id: Number(id),
                is_free_delivery: isFreeDelivery ? 1 : 0,
                upto_km: datas?.kms ? Number(datas?.kms) : 0,
                accept_return_policy: ReplaceType === "Return" ? 1 : 0,
                who_cover_return_policy: ReplaceType === "Return" ? "seller" : "",
                return_duration_in_minute: ReplaceType === "Return" ? 1380 : 0,
                delivery_cover_by: datas.coversdelivery === "OrdaLane" ? 1 : 2,
                gateway_chrg_paid_by: datas.gateway === "Ordalane" ? 1 : 2,
                platform_fees_paid_by: datas.platformfees === "Ordalane" ? 1 : 2,
                refund_condition_1: Number(select1),
                refund_condition_2: Number(select2),
                refund_condition_3: Number(select3),
                return_refund_status:
                  ReplaceType === "Return" ? 2 : ReplaceType === "Refund" ? 3 : 4,
              },
              {
                onError: (val) => {
                  Alert.alert("Faild", val.message);
                },
                onSuccess: (val) => {
                  if (val?.status === 0) {
                    Alert.alert("Faild", val?.msg);
                    return;
                  }
                  router.replace("/home/<USER>");
                  setpage(0);
                },
              },
            );
          })}
        >
          <Text className="font-[400] text-[#fff] text-[16px]">Submit</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export const DeliveryComponent = ({control}) => {
  return (
    <View className="flex-row items-center space-x-2">
      <View>
        <Text className="font-[400] text-[16px] text-[#B3B3B3]">Free delivery upto</Text>
      </View>
      <View>
        <Controller
          name="kms"
          control={control}
          render={({field: {onChange, value}}) => {
            return (
              <>
                <TextInput
                  value={value}
                  onChangeText={onChange}
                  keyboardType="numeric"
                  className="border-[1px] border-[#627164] rounded-[4px] h-[40px] min-w-[58px] pl-2"
                />
              </>
            );
          }}
        />
      </View>
      <View>
        <Text className="font-[400] text-[16px] text-[#B3B3B3]">km</Text>
      </View>
    </View>
  );
};

export default page3;
