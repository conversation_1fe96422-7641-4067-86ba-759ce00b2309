import Catalogue from "../../../assets/icons/TabsIcon/stack.svg";
import Close from "../../../assets/icons/Close.svg";
import Learn from "../../../assets/icons/TabsIcon/lear.svg";
import Orders from "../../../assets/icons/TabsIcon/clipboard.svg";
import Profile from "../../../assets/icons/TabsIcon/profile.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { Dispatch, SetStateAction, createContext, useEffect, useRef, useState } from "react";
import Wallet from "../../../assets/icons/TabsIcon/wallet.svg";
import useCatalogueActionsHook from "@/hook/useCatalogueActionsHook/useCatalogueActionsHook";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import { Tabs } from "expo-router";
import { Text, TouchableOpacity, View } from "react-native";
import { useSelector } from "react-redux";
import { selectCanShowBottomactions, selectCanShowSaveBtn } from "@/redux/Catalogue/meno";

// Define RBSheet ref type
type RBSheetRefType = {
  open: () => void;
  close: () => void;
};

const QuickEditGlobalContext = createContext<{
  productid: number | null;
  setproductid: Dispatch<SetStateAction<number | null>>;
  price: number;
  setprice: Dispatch<SetStateAction<number>>;
  is_discounted_prd: number;
  setis_discounted_prd: Dispatch<SetStateAction<number>>;
  slash_price: number;
  setslash_price: Dispatch<SetStateAction<number>>;
  unit: string;
  setunit: Dispatch<SetStateAction<string>>;
  qty: number;
  setqty: Dispatch<SetStateAction<number>>;
}>({
  productid: null,
  setproductid: () => null,
  price: 0,
  setprice: () => null,
  is_discounted_prd: 0,
  setis_discounted_prd: () => null,
  slash_price: 0,
  setslash_price: () => null,
  unit: "",
  setunit: () => null,
  qty: 0,
  setqty: () => null,
});
const TabLayout = () => {
  const refRBSheet = useRef<RBSheetRefType>(null);

  const canShowBottomactions = useSelector(selectCanShowBottomactions);
  const CanShowSaveBtn = useSelector(selectCanShowSaveBtn);
  const {removeproducts, removeAllfromList, CancleAllQucikEdit, canShowSaveButton} =
    useCatalogueActionsHook();

  const {mutate: QuickEditFunction} = useTenStackMutateD({
    endpoint: "auth/productQuickEdit",
    invalidateQueriesKey: ["productDetails"],
  });

  const [productid, setproductid] = useState<number | null>(null);
  const [price, setprice] = useState<number>(0);
  const [qty, setqty] = useState<number>(0);
  const [is_discounted_prd, setis_discounted_prd] = useState<number>(0);
  const [slash_price, setslash_price] = useState<number>(0);
  const [unit, setunit] = useState<string>("");

  const QuickEdit = () => {
    QuickEditFunction({
      id: productid,
      price: price,
      is_discounted_prd: is_discounted_prd,
      slash_price: slash_price,
      unit: unit,
      qty: qty,
    });
  };

  useEffect(() => {
    canShowSaveButton();
  }, [canShowSaveButton]);
  return (
    <QuickEditGlobalContext.Provider
      value={{
        productid,
        setproductid,
        price,
        setprice,
        is_discounted_prd,
        setis_discounted_prd,
        slash_price,
        setslash_price,
        unit,
        setunit,
        qty,
        setqty,
      }}
    >
      <Tabs
        backBehavior="initialRoute"
        screenOptions={{
          tabBarActiveTintColor: "#00660A",
          tabBarLabelStyle: {fontSize: 12, fontWeight: 700},
          tabBarShowLabel: true,
          tabBarStyle: {
            backgroundColor: "#fff",
            justifyContent: "center",
            alignItems: "center",
          },
        }}
      >
        <Tabs.Screen
          name="order"
          options={{
            title: "Order",
            headerShown: false,
            tabBarIcon: ({focused}) => {
              return (
                <View className="justify-center items-center flex-1">
                  <Orders stroke={focused ? "#00660A" : "#939D94"} />
                </View>
              );
            },
          }}
        />
        <Tabs.Screen
          name="catalogue"
          options={{
            headerShown: false,
            title: "Catalogue",
            tabBarIcon: ({focused}) => {
              return (
                <View className="justify-center items-center flex-1">
                  <Catalogue stroke={focused ? "#00660A" : "#939D94"} />
                </View>
              );
            },
          }}
        />
        <Tabs.Screen
          name="wallet"
          options={{
            headerShown: false,
            title: "Wallet",
            tabBarIcon: ({focused}) => {
              return (
                <View className="justify-center items-center flex-1">
                  <Wallet stroke={focused ? "#00660A" : "#939D94"} />
                </View>
              );
            },
          }}
        />
        <Tabs.Screen
          name="learn"
          options={{
            headerShown: false,
            title: "Learn",
            tabBarIcon: ({focused}) => {
              return (
                <View className="justify-center items-center flex-1">
                  <Learn stroke={focused ? "#00660A" : "#939D94"} />
                </View>
              );
            },
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            headerShown: false,
            title: "Profile",

            tabBarIcon: ({focused}) => {
              return (
                <View className="justify-center items-center flex-1">
                  <Profile stroke={focused ? "#00660A" : "#939D94"} />
                </View>
              );
            },
          }}
        />
      </Tabs>
      {canShowBottomactions && (
        <View className="absolute bottom-0 w-full mb-4">
          <View className="h-[84px] bg-[#ffffff] w-full z-10 justify-center items-center flex-row space-x-[8px]">
            <TouchableOpacity
              onPress={() => {
                removeAllfromList();
              }}
              className="w-[162px] h-[44px] rounded-[4px] justify-center items-center border-[1.5px] border-[#00660A]"
            >
              <Text className="text-[#00660A] font-[400] text-[16px] leading-[24px]">Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => refRBSheet.current?.open()}
              className="w-[162px] h-[44px] rounded-[4px] justify-center items-center bg-[#00660A]"
            >
              <Text className="text-[#fff] font-[400] text-[16px] leading-[24px]">
                Bulk Actions
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
      {CanShowSaveBtn && (
        <View className="absolute bottom-0 w-full px-4 mb-4">
          <View className="h-[84px] bg-[#ffffff] w-full z-10 justify-center items-center flex-row space-x-[8px]">
            <TouchableOpacity
              onPress={() => {
                CancleAllQucikEdit();
                canShowSaveButton();
              }}
              className="h-[44px] flex-1  rounded-[4px] justify-center items-center border-[1.5px] border-[#00660A]"
            >
              <Text className="text-[#00660A] font-[400] text-[16px] leading-[24px]">Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                CancleAllQucikEdit();
                canShowSaveButton();
              }}
              className="h-[44px] flex-1 rounded-[4px] justify-center items-center bg-[#00660A]"
            >
              <Text className="text-[#fff] font-[400] text-[16px] leading-[24px]">
                Save Changes
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
      <RBSheet
        ref={refRBSheet}
        customStyles={{
          container: {
            borderRadius: 20,
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="px-6 py-6 justify-center">
          <View className="mt-4">
            <View className="flex-row justify-between">
              <View>
                <Text className="font-[600] text-[16px] leading-[19]">Bulk Action</Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  refRBSheet.current?.close();
                }}
              >
                <Close />
              </TouchableOpacity>
            </View>
            <View className="flex-row flex-wrap gap-[10px] mt-4">
              <TouchableOpacity
                onPress={() => {
                  removeproducts();
                  refRBSheet.current?.close();
                }}
                className="border-[1px] border-[#00660A] rounded-[5px] px-[10px] py-[8px]"
              >
                <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">
                  Remove Product
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </RBSheet>
    </QuickEditGlobalContext.Provider>
  );
};

export default TabLayout;
