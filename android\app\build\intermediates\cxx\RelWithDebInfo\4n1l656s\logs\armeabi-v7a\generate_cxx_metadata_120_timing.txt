# C/C++ build system timings
generate_cxx_metadata
  [gap of 140ms]
  create-invalidation-state 137ms
  generate-prefab-packages
    exec-prefab 478ms
    [gap of 417ms]
  generate-prefab-packages completed in 897ms
  execute-generate-process
    [gap of 25ms]
    exec-configure 874ms
    [gap of 398ms]
  execute-generate-process completed in 1297ms
  [gap of 12ms]
  remove-unexpected-so-files 24ms
  [gap of 93ms]
generate_cxx_metadata completed in 2622ms

