# C/C++ build system timings
generate_cxx_metadata
  [gap of 123ms]
  create-invalidation-state 367ms
  generate-prefab-packages
    [gap of 136ms]
    exec-prefab 741ms
    [gap of 100ms]
  generate-prefab-packages completed in 977ms
  execute-generate-process
    exec-configure 3410ms
    [gap of 551ms]
  execute-generate-process completed in 3962ms
  [gap of 63ms]
  remove-unexpected-so-files 96ms
  [gap of 138ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 5750ms

