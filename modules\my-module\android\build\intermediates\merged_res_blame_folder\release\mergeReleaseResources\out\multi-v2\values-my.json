{"logs": [{"outputFile": "expo.modules.mymodule.my-module-mergeReleaseResources-51:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\69a44a0644e0c0b6fca06fc8288a055a\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,5624", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,5705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac7076343e9dc5d8b75e982d33fd7fa0\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "30,31,32,33,34,35,36,73", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2940,3043,3147,3250,3352,3457,3563,6661", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3038,3142,3245,3347,3452,3558,3677,6757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46ed95a07aaabdffd864bffdea2e8e32\\transformed\\react-android-0.76.7-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,282,353,437,506,574,653,737,827,909,979,1071,1154,1236,1328,1412,1495,1567,1639,1724,1800,1877,1956", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "124,203,277,348,432,501,569,648,732,822,904,974,1066,1149,1231,1323,1407,1490,1562,1634,1719,1795,1872,1951,2031"}, "to": {"startLines": "29,39,43,44,45,48,49,50,51,54,55,58,59,62,63,64,66,67,69,71,72,74,77,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2866,3864,4234,4308,4379,4646,4715,4783,4862,5117,5207,5462,5532,5785,5868,5950,6119,6203,6359,6504,6576,6762,6985,7188,7267", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "2935,3938,4303,4374,4458,4710,4778,4857,4941,5202,5284,5527,5619,5863,5945,6037,6198,6281,6426,6571,6656,6833,7057,7262,7342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92b001d3df7e2cf8f19fe0630b26b2a0\\transformed\\foundation-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,107", "endOffsets": "159,267"}, "to": {"startLines": "81,82", "startColumns": "4,4", "startOffsets": "7347,7456", "endColumns": "108,107", "endOffsets": "7451,7559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45ec1cf61ca13cfcbcc6941817029dcc\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,1019,1105,1180,1257,1330,1403,1484,1550", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,1014,1100,1175,1252,1325,1398,1479,1545,1671"}, "to": {"startLines": "37,38,40,41,42,46,47,52,53,56,57,61,65,68,70,75,76,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3682,3776,3943,4047,4151,4463,4547,4946,5035,5289,5376,5710,6042,6286,6431,6838,6919,7062", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "3771,3859,4042,4146,4229,4542,4641,5030,5112,5371,5457,5780,6114,6354,6499,6914,6980,7183"}}]}, {"outputFile": "expo.modules.mymodule.my-module-release-53:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\69a44a0644e0c0b6fca06fc8288a055a\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,5624", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,5705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac7076343e9dc5d8b75e982d33fd7fa0\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "30,31,32,33,34,35,36,73", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2940,3043,3147,3250,3352,3457,3563,6661", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3038,3142,3245,3347,3452,3558,3677,6757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46ed95a07aaabdffd864bffdea2e8e32\\transformed\\react-android-0.76.7-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,282,353,437,506,574,653,737,827,909,979,1071,1154,1236,1328,1412,1495,1567,1639,1724,1800,1877,1956", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "124,203,277,348,432,501,569,648,732,822,904,974,1066,1149,1231,1323,1407,1490,1562,1634,1719,1795,1872,1951,2031"}, "to": {"startLines": "29,39,43,44,45,48,49,50,51,54,55,58,59,62,63,64,66,67,69,71,72,74,77,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2866,3864,4234,4308,4379,4646,4715,4783,4862,5117,5207,5462,5532,5785,5868,5950,6119,6203,6359,6504,6576,6762,6985,7188,7267", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "2935,3938,4303,4374,4458,4710,4778,4857,4941,5202,5284,5527,5619,5863,5945,6037,6198,6281,6426,6571,6656,6833,7057,7262,7342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92b001d3df7e2cf8f19fe0630b26b2a0\\transformed\\foundation-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,107", "endOffsets": "159,267"}, "to": {"startLines": "81,82", "startColumns": "4,4", "startOffsets": "7347,7456", "endColumns": "108,107", "endOffsets": "7451,7559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45ec1cf61ca13cfcbcc6941817029dcc\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,1019,1105,1180,1257,1330,1403,1484,1550", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,1014,1100,1175,1252,1325,1398,1479,1545,1671"}, "to": {"startLines": "37,38,40,41,42,46,47,52,53,56,57,61,65,68,70,75,76,78", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3682,3776,3943,4047,4151,4463,4547,4946,5035,5289,5376,5710,6042,6286,6431,6838,6919,7062", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "3771,3859,4042,4146,4229,4542,4641,5030,5112,5371,5457,5780,6114,6354,6499,6914,6980,7183"}}]}]}