import AddproductField from "../../../../component/AddproductField";
import CustomFiled from "../../../../component/CustomFiled";
import CustomFiled3 from "../../../../component/CustomFiled3";
import DropDownComponent from "../../../../component/DropDownComponent";
import FileUploder from "../../../../component/FileUploder";
import OnOffButton from "../../../../component/OnOffButton";
import PriceFieldComponent from "../../../../component/PriceFieldComponent";
import React, { useContext, useEffect, useState } from "react";
import RsIcon from "@/assets/icons/₹Icon.svg";
import SmallPressable, { SmallPressable2 } from "../../../../component/SmallPressable";
import TaxTabs from "../../../../component/TaxTabs";
import TextAreaComponent from "../../../../component/TextAreaComponent";
import useGetApiData from "../../../../hooks/useGetApiData";
import useGetApiDatawithParam from "../../../../hooks/useGetApiDatawithParam";
import useTenStack from "@/hook/TenStackHook/useTenStack";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import useUpdateWithFormData from "../../../../hooks/useUpdateWithFormData";
import { router, useLocalSearchParams } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Dropdown } from "react-native-element-dropdown";
import { useMyShopId } from "../../../../store/index";
import { OnOffButton3 } from "../../addnewproduct/product";
import { GlobalPagenumber } from "./_layout";

import {
  ActivityIndicator,
  Alert,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const index = () => {
  const Units = {
    KG: "1",
    LB: "2",
    OZ: "3",
    G: "4",
    LTR: "5",
  };
  const product_category = {
    grocery: 0,
    "Street Food": 1,
  };
  const [Taxable, setTaxable] = useState(false);
  const {page, setpage} = useContext(GlobalPagenumber);
  const {productImage, setproductImage} = useState(null);
  const {
    control,
    setValue,
    handleSubmit,
    watch,
    formState: {errors},
  } = useForm();
  const {id} = useLocalSearchParams();
  const [showProductSuggestion, setProductSuggestion] = useState(false);

  const [IsSlash, setIsSlash] = useState(false);
  const [ActualAmount, setActualAmount] = useState("400");
  const [SlashAmount, setSlashAmount] = useState("");
  const [isUnPackageProduct, setUnPackageProduct] = useState(false);

  const {
    data,
    isLoading,
    refetch: triggerreFresh,
  } = useTenStack({
    endpoint: "/auth/productDetails",
    pram: {product_id: id},
    key: "productDetails",
    id: [id.toString()],
    refetch: true,
  });
  console.log(JSON.stringify(data, null, 2));
  const {mutate: UpdateProduct} = useTenStackMutateD({
    invalidateQueriesKey: ["productDetails"],
    endpoint: "auth/productEdit",
  });

  const {data: ProductCategoryData, isLoading: ProductCategoryLoading} = useGetApiData({
    endpoint: "/productCategoryList",
  });

  useEffect(() => {
    setValue("productname", data?.data?.product_data.name);
    setValue("description", data?.data?.product_data.description);
    setValue("price", data?.data?.product_data.price);
    setValue("unit", data?.data?.product_data.unit);
    setValue("mode", data?.data?.product_data.delivery_mode);
    setValue("Prices", data?.data?.product_data.unpackage_prd_price);
    setValue("Units", data?.data?.product_data.unpackage_prd_unit);
    setUnPackageProduct(Boolean(data?.data?.product_data.is_unpackage_prd));
    setValue("qty", data?.data?.product_data.qty);
    setValue("productimage", data?.data?.product_data.image);
    setValue(
      "warranty/guaranty",
      data?.data?.product_data?.prd_cover_by === 3
        ? "guaranty"
        : data?.data?.product_data?.prd_cover_by === 2
          ? "warranty"
          : "none",
    );
    setValue("dur", data?.data?.product_data?.prd_cover_num.toString());
    setValue("warOrguarName", data?.data?.product_data?.prd_cover_duration.toString());
    setValue(
      "mode",
      data?.data?.product_data?.delivery_mode === 1
        ? "Normal"
        : data?.data?.product_data?.delivery_mode === 2
          ? "Heavy"
          : "Quick",
    );

    setIsSlash(Boolean(data?.data?.product_data.is_discounted_prd));
    setValue("actualamount", data?.data?.product_data.price);
    setValue("slashedamount", data?.data?.product_data.slash_price);
    setValue("category", data?.data?.product_data?.product_category_id);
    if (data?.data?.product_data?.is_taxable == 1) {
      setTaxable(true);
      setValue("CGST", String(data?.data?.product_data?.cgst));
      setValue("SGST", String(data?.data?.product_data?.sgst));
      setValue("CESS", String(data?.data?.product_data?.cess));
    }
  }, [isLoading, data]);
  const formdata = new FormData();

  const HandleSubmit = (datas: any) => {
    formdata.append("id", data?.data?.product_data?.id);
    formdata.append("shop_id", data?.data?.product_data?.shop_id);
    formdata.append("product_category_id", datas?.category);
    formdata.append("name", datas?.productname);
    formdata.append("description", datas?.description);
    formdata.append("is_discounted_prd", Number(IsSlash));

    formdata.append("is_unpackage_prd", Number(isUnPackageProduct));
    formdata.append("unpackage_prd_price", datas?.Prices);
    formdata.append("unpackage_prd_unit", datas?.Units);
    formdata.append("slash_price", IsSlash ? datas?.slashedamount : 0);
    formdata.append("qty", isUnPackageProduct ? 0 : datas?.qty);
    formdata.append("price", datas?.price ? datas?.price : datas?.Prices);
    formdata.append("unit", datas?.unit ? datas?.unit : datas?.Units);
    formdata.append("is_taxable", Number(Taxable));
    formdata.append("cgst", Taxable ? datas?.CGST : 0);
    formdata.append("sgst", Taxable ? datas?.SGST : 0);
    formdata.append("cess", Taxable ? datas?.CESS : 0);
    formdata.append("image", {
      uri: datas?.productimage,
      name: datas?.productimage,
      filename: datas?.productimage,
      type: datas?.type ? datas?.type : "image/jpeg",
    } as any);
    formdata.append(
      "delivery_mode",
      (watch("mode") === "Normal" ? 1 : watch("mode") === "Heavy" ? 2 : 3).toString(),
    );
    formdata.append(
      "prd_cover_by",
      (watch("warranty/guaranty") === "guaranty"
        ? 3
        : watch("warranty/guaranty") === "warranty"
          ? 2
          : 1
      ).toString(),
    );
    formdata.append(
      "prd_cover_num",
      (watch("warranty/guaranty") === "guaranty" || watch("warranty/guaranty") === "warranty"
        ? Number(watch("dur"))
        : 0
      ).toString(),
    );
    formdata.append(
      "prd_cover_duration",
      (watch("warranty/guaranty") === "guaranty" || watch("warranty/guaranty") === "warranty"
        ? Number(watch("warOrguarName"))
        : 0
      ).toString(),
    );
    console.log(JSON.stringify(formdata, null, 2));
    UpdateProduct(formdata, {
      onSuccess: () => {
        setpage(page + 1);
        router.push({
          pathname: "/home/<USER>/product/page1",
          params: {id: id},
        });
      },
      onError: (error) => {
        console.log(error);
        Alert.alert("Error", "An error occurred while submitting the form.");
      },
    });
  };

  const [open, setOpen] = useState(false);

  return (
    <>
      {isLoading ? (
        <>
          <ActivityIndicator />
        </>
      ) : (
        <>
          <View className="mt-4">
            <View>
              <Text className="font-[400] text-[18px]">Product Information</Text>
            </View>
            <View className="">
              <AddproductField
                neme={"productname"}
                control={control}
                text={"Product Name"}
                placeholder={"Enter Name of the Product"}
              />
            </View>
            <View>
              <TextAreaComponent
                text={"Description"}
                placeholder={"Enter Description"}
                name={"description"}
                control={control}
              />
            </View>
            <View className="mt-4">
              {ProductCategoryLoading ? (
                <></>
              ) : (
                <>
                  <View className="mt-2">
                    <Text className="font-[400] text-[16px] text-[#4D4D4D]">Product Category</Text>
                    <Controller
                      name={"category"}
                      control={control}
                      render={({field: {onChange, value}}) => {
                        return (
                          <>
                            <Dropdown
                              data={ProductCategoryData?.data}
                              placeholder={"Select type of request"}
                              containerStyle={{}}
                              placeholderStyle={{
                                color: "#B3B3B3",
                                textAlignVertical: "center",
                              }}
                              style={{
                                height: 40,
                              }}
                              labelField="name"
                              valueField="id"
                              value={value}
                              onChange={(item) => {
                                onChange(item.id);
                              }}
                              className="border-[1px] border-[#bec9e1] rounded-[4px] px-3 mt-2"
                            />
                          </>
                        );
                      }}
                    />
                  </View>
                </>
              )}
              <View className="mt-2">
                <SmallPressable2
                  pressfun={() => {
                    setProductSuggestion((value) => {
                      return !value;
                    });
                  }}
                  text={"Add Category suggestion"}
                  style={{width: 218}}
                  value={showProductSuggestion}
                />
              </View>
            </View>
            {showProductSuggestion ? (
              <>
                <View className="mt-2">
                  <AddproductField
                    neme={"ProductCategory"}
                    control={control}
                    text={"Enter Product Category"}
                    placeholder={"Enter Product Category"}
                  />
                </View>
                <View className="mt-4">
                  <TouchableOpacity
                    className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
                    onPress={() => {}}
                  >
                    <Text className="font-[400] text-[#fff] text-[16px]">Submit</Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <></>
            )}
            <View className="mt-2">
              <OnOffButton3
                val={isUnPackageProduct}
                setTaxables={setUnPackageProduct}
                btnText1={"PackageProduct"}
                btnText2={"UnPackageProduct"}
              />
            </View>

            {isUnPackageProduct ? (
              <>
                <CustomFiled3
                  text={"Enter Price"}
                  name1={"Prices"}
                  name2={"Units"}
                  placeholder={"Enter Price!"}
                  KeyType={"numeric"}
                  data={[
                    {label: "kg", value: "1"},
                    {label: "lt", value: "2"},
                  ]}
                  control={control}
                />
              </>
            ) : (
              <></>
            )}

            {!isUnPackageProduct && (
              <>
                <View className="mt-2">
                  <PriceFieldComponent
                    neme={"price"}
                    control={control}
                    text={"Enter price"}
                    placeholder={"Enter price"}
                    KeyType={"numeric"}
                  />
                </View>
              </>
            )}

            <View className=" my-3 mt-4">
              <View className="">
                <View className="flex-row items-center justify-between">
                  <View
                    style={{position: "relative"}}
                    className="flex-shrink flex-row space-x-1 items-center"
                  >
                    <Text className="font-[400] text-[16px] leading-[20px]">
                      Select Delivery Mode
                    </Text>
                    <View className="relative">
                      {open ? (
                        <>
                          <View className="absolute p-4 border-[1px] border-[#ccc] bg-black rounded-[10px] bottom-[13px]">
                            <Text className="text-white max-h-[200px] max-w-[200px] mb-1">
                              <Text className="font-[600]">Quick Delivery</Text>: Products that can
                              be prepared within 2 minutes by the seller.
                            </Text>
                            <Text className="text-white max-h-[200px] max-w-[200px] mb-1">
                              <Text className="font-[600]">Normal delivery</Text> : Products that
                              can be prepared within an hour by the seller.
                            </Text>
                            <Text className="text-white max-h-[200px] max-w-[200px]">
                              <Text className="font-[600]">Heavy vehicle delivery</Text>: Products
                              that require heave vehicle eg. - truck, etc. to fulfill the delivery.
                            </Text>
                          </View>
                        </>
                      ) : (
                        <></>
                      )}
                    </View>
                    <TouchableOpacity
                      onPress={() => {
                        setOpen(!open);
                      }}
                      className="w-[20px] h-[20px] rounded-full items-center justify-center bg-black"
                    >
                      <Text className="text-[#fff]">?</Text>
                    </TouchableOpacity>
                  </View>

                  <Controller
                    name="mode"
                    control={control}
                    defaultValue={"Normal"}
                    render={({field: {onChange, value}}) => {
                      return (
                        <Dropdown
                          style={{
                            minWidth: 100,
                            minHeight: 40,
                            borderColor: "#ACB9D5",
                            borderWidth: 1,
                            paddingLeft: 10,
                            borderRadius: 4,
                          }}
                          placeholder={"Options"}
                          placeholderStyle={{
                            color: "#B3B3B3",
                            height: 35,
                            textAlignVertical: "center",
                          }}
                          data={[
                            {
                              label: "Quick",
                              value: "Quick",
                            },
                            {
                              label: "Normal",
                              value: "Normal",
                            },
                            {
                              label: "Heavy",
                              value: "Heavy",
                            },
                          ]}
                          labelField="label"
                          valueField="value"
                          value={value}
                          onChange={(item) => {
                            onChange(item.value);
                          }}
                        />
                      );
                    }}
                  />
                </View>
              </View>
            </View>

            <View className="">
              <View className="">
                <DropDownComponent
                  control={control}
                  name={"warranty/guaranty"}
                  data={[
                    {label: "warranty", label: "warranty"},
                    {label: "guaranty", label: "guaranty"},
                    {label: "none", label: "none"},
                  ]}
                  setvaluefun={setValue}
                  defaul={"none"}
                  text={"Does the product has warranty/guaranty?(optional)"}
                />
              </View>
              {watch("warranty/guaranty") != "" &&
              watch("warranty/guaranty") != undefined &&
              watch("warranty/guaranty") != "none" ? (
                <>
                  <View>
                    <CustomFiled
                      text={"What is the time duration for a warranty or guarantee ?"}
                      firstfieldname={"warOrguarName"}
                      secoundfieldname={"dur"}
                      placeholder={"Enter time"}
                      unitsplace={"Select"}
                      data={[
                        {label: "Month", value: "1"},
                        {label: "Year", value: "2"},
                      ]}
                      control={control}
                    />
                  </View>
                </>
              ) : (
                <></>
              )}
            </View>

            {!isUnPackageProduct && (
              <>
                <CustomFiled3
                  text={"Enter qty"}
                  name1={"qty"}
                  name2={"unit"}
                  placeholder={"Enter qty!"}
                  KeyType={"numeric"}
                  rules1={{
                    required: {value: true, message: "Please Enter qty"},
                  }}
                  rules2={{
                    required: {value: true, message: "Please Enter qty"},
                  }}
                  data={[
                    {label: "Kg", value: "1"},
                    {label: "ltr", value: "2"},
                    {label: "pcs", value: "3"},
                  ]}
                  control={control}
                />
                <View className="flex-row space-x-4 justify-between">
                  {errors?.qty?.message ? (
                    <View className="relative">
                      <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                        {errors?.qty?.message}
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}
                  {errors?.unit?.message ? (
                    <View className="relative">
                      <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                        {errors?.unit?.message}
                      </Text>
                    </View>
                  ) : (
                    <></>
                  )}
                </View>
              </>
            )}

            <View className="flex-row items-center justify-between my-3 mt-4">
              <View className="flex-shrink">
                <Text className="font-[400] text-[16px] leading-[20px]">
                  Want to show some discount?
                </Text>
              </View>
              <View className="">
                <OnOffButton setTaxables={setIsSlash} val={IsSlash} />
              </View>
            </View>
            {IsSlash ? (
              <>
                <View className="flex-row items-center border-[1px] border-[#627164] justify-center p-4 mt-4 w-full space-x-5">
                  <View className="flex-1">
                    <Text className="font-[400] text-[16px] leading-[20px] text-[#4D4D4D]">
                      Discounted Amount
                    </Text>
                    <View className="flex-1 pl-2 rounded-[4px] flex-row items-center border-[1px] border-[#ACB9D5] h-[40px] mt-2">
                      <View>
                        <RsIcon />
                      </View>
                      <View className="flex-1">
                        <Controller
                          name="slashedamount"
                          control={control}
                          rules={{
                            required: {value: true, message: "Please Enter discounted amount"},
                            pattern: {
                              value: /^\d+(\.\d{1,2})?$/,
                              message: "Please enter quantity in format XX.XX (e.g., 25.55)",
                            },
                            validate: (value) => {
                              if (Number(value) >= Number(watch("price"))) {
                                return "Discounted amount should be less than the actual amount";
                              }
                            },
                          }}
                          defaultValue={SlashAmount}
                          render={({field: {onChange, value}}) => {
                            return (
                              <>
                                <TextInput
                                  className="pl-2 font-[500] text-[16px] leading-[24px] flex-1"
                                  value={value}
                                  onChangeText={onChange}
                                  editable={IsSlash}
                                  keyboardType="numeric"
                                />
                              </>
                            );
                          }}
                        />
                      </View>
                    </View>
                  </View>
                </View>
                {errors?.slashedamount?.message && (
                  <View className="relative">
                    <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                      {String(errors?.slashedamount?.message)}
                    </Text>
                  </View>
                )}
              </>
            ) : (
              <></>
            )}

            <View className="mt-4">
              <View className="flex-row items-center justify-between my-2">
                <Text>Is the Product Taxable?</Text>
                <View className="">
                  <OnOffButton setTaxables={setTaxable} val={Taxable} />
                </View>
              </View>
            </View>
            {Taxable ? <TaxTabs Taxable={Taxable} control={control} /> : <></>}
            <View className="">
              <FileUploder
                title={"Upload an Image"}
                text={"Browse Files"}
                setValue={setValue}
                images={data?.data?.product_data?.image}
              />
            </View>
            <View className="mt-5">
              <TouchableOpacity
                className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
                onPress={handleSubmit(HandleSubmit)}
                // onPress={HandleSubmit}
              >
                <Text className="font-[400] text-[#fff] text-[16px]">Next</Text>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )}
    </>
  );
};

export default index;
