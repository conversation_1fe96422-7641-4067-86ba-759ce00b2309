{"name": "SellerApp", "slug": "my-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon1.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/icon1.png", "backgroundColor": "#ffffff"}, "package": "com.dhass.myapp", "googleServicesFile": "./android/app/google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], ["expo-splash-screen", {"image": "./assets/icon1.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"BASE_URL": "http://13.235.24.176:7600/mobile-api/", "Map_API_KEY": "AIzaSyCg62vT6rJj3WMmQIBcI2iXPwXdmk6QLU4", "router": {"origin": false}, "eas": {"projectId": "8ae91adb-123c-4246-8d49-1613f09c24ca"}}, "sdkVersion": "52.0.0", "platforms": ["ios", "android", "web"], "androidStatusBar": {"backgroundColor": "#ffffff"}}