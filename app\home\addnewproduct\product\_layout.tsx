import BackArrow from "@/assets/icons/BackArrow.svg";
import React, { createContext, useState } from "react";
import { useRoute } from "@react-navigation/native";
import { Stack, router } from "expo-router";
import { Slot } from "expo-router";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export const GlobalPagenumber = createContext();
const _layout = () => {
  const [page, setpage] = useState("index");
  return (
    <SafeAreaView className="flex-1 bg-white">
      <ScrollView className="mb-4">
        {/* Title Area */}
        <GlobalPagenumber.Provider
          value={{
            page,
            setpage,
          }}
        >
          <View className="mt-7 px-5">
            <View className="flex-row relative items-center justify-start mt-4">
              <TouchableOpacity
                onPress={() => {
                  setpage(page - 1);
                  router.back();
                }}
                className="absolute"
              >
                <View>
                  <BackArrow />
                </View>
              </TouchableOpacity>
              <View className="flex-1 items-center justify-center">
                <Text className="font-[400] text-[26px] leading-[39px]">
                  {page == "page1"
                    ? "Variants and Options"
                    : page == "page2"
                    ? "Additional description (Optional)"
                    : page == "page3"
                    ? "Policy"
                    : "Add Product"}
                </Text>
              </View>
            </View>
            <View className="flex-row space-x-2 mt-7">
              <View
                className="flex-1 h-[4px] rounded-[13px]"
                style={{
                  backgroundColor:
                    page == "index" ||
                    page == "page1" ||
                    page == "page2" ||
                    page == "page3"
                      ? "#00660A"
                      : "#E6E6E6",
                }}
              />
              <View
                className="flex-1 h-[4px] rounded-[13px]"
                style={{
                  backgroundColor:
                    page == "page1" || page == "page2" || page == "page3"
                      ? "#00660A"
                      : "#E6E6E6",
                }}
              />
              <View
                className="flex-1 h-[4px] rounded-[13px]"
                style={{
                  backgroundColor:
                    page == "page2" || page == "page3" ? "#00660A" : "#E6E6E6",
                }}
              />
              <View
                className="flex-1 h-[4px] rounded-[13px]"
                style={{
                  backgroundColor: page == "page3" ? "#00660A" : "#E6E6E6",
                }}
              />
            </View>
            <Slot />
          </View>
        </GlobalPagenumber.Provider>
      </ScrollView>
    </SafeAreaView>
  );
};

export default _layout;
