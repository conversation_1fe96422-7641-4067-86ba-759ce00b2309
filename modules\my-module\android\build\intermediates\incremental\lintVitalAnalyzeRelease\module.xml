<lint-module
    format="1"
    dir="F:\Apps\Seller\my-app\modules\my-module\android"
    name=":my-module"
    type="LIBRARY"
    maven="expo.modules.mymodule:my-module:0.6.3"
    agpVersion="8.6.0"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
