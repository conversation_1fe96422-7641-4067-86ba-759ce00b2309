import * as Location from "expo-location";
import Constants from "expo-constants";
import LocationIcon from "../../../../assets/Profileimg/Location.svg";
import MapView from "react-native-maps";
import MinIcon from "../../../../assets/Profileimg/-.svg";
import PinIcon from "../../../../assets/icons/userprofileIcon/PinCion.svg";
import Plus from "../../../../assets/Profileimg/plus.svg";
import React, {useCallback, useEffect, useRef, useState} from "react";
import SearchIcon from "../../../../assets/Profileimg/SearchIcon.svg";
import {useLatitudeLongitude} from "../../../../store/BusinessProfileData";

import {
  ActivityIndicator,
  Alert,
  FlatList,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

// Define interface for place search results
interface PlaceSearchResult {
  id: string;
  name: string;
  description: string;
  placeId: string;
}

const index = () => {
  const [region, setRegion] = useState<{
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  } | null>(null);
  const mapRef = useRef<any>(null);
  const {setLatitudeLongitude} = useLatitudeLongitude((state) => state);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<PlaceSearchResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Function to search for places
  const searchPlaces = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        setShowResults(false);
        return;
      }

      try {
        setIsLoading(true);

        // Use the Google Places API directly
        const apiKey = Constants.expoConfig?.extra?.Map_API_KEY;
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
            query,
          )}&key=${apiKey}`,
        );

        const data = await response.json();

        if (data.status === "OK" && data.predictions) {
          const results = data.predictions.map((prediction: any) => ({
            id: prediction.place_id,
            name: prediction.structured_formatting?.main_text || prediction.description,
            description: prediction.description,
            placeId: prediction.place_id,
          }));

          setSearchResults(results);
          setShowResults(true);
        } else {
          console.error("Places API error:", data.status);
          setSearchResults([]);
          if (data.status === "REQUEST_DENIED") {
            Alert.alert(
              "API Error",
              "The Places API request was denied. Please check your API key.",
            );
          }
        }
      } catch (error) {
        console.error("Error searching places:", error);
        Alert.alert("Error", "Failed to search for places. Please try again.");
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading, setSearchResults, setShowResults],
  );

  // Function to get place details including coordinates
  const getPlaceDetails = useCallback(
    async (placeId: string) => {
      try {
        setIsLoading(true);

        const apiKey = Constants.expoConfig?.extra?.Map_API_KEY;
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/details/json?place_id=${encodeURIComponent(
            placeId,
          )}&fields=geometry&key=${apiKey}`,
        );

        const data = await response.json();

        if (data.status === "OK" && data.result && data.result.geometry) {
          const {lat, lng} = data.result.geometry.location;
          return {latitude: lat, longitude: lng};
        } else {
          console.error("Place details API error:", data.status);
          Alert.alert("Error", "Failed to get location details. Please try again.");
          return null;
        }
      } catch (error) {
        console.error("Error getting place details:", error);
        Alert.alert("Error", "Failed to get location details. Please try again.");
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading],
  );

  // Handle search input changes with debounce
  const handleSearchInputChange = useCallback(
    (text: string) => {
      setSearchQuery(text);

      // Clear any existing timeout
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      // Set a new timeout to search after typing stops
      if (text.trim()) {
        const timeout = setTimeout(() => {
          searchPlaces(text);
        }, 500); // 500ms debounce
        setSearchTimeout(timeout);
      } else {
        setShowResults(false);
        setSearchResults([]);
      }
    },
    [searchTimeout, searchPlaces, setSearchQuery, setShowResults, setSearchResults],
  );

  // Handle selecting a place from search results
  const handleSelectPlace = useCallback(
    async (place: PlaceSearchResult) => {
      try {
        const coordinates = await getPlaceDetails(place.placeId);

        if (coordinates) {
          const newRegion = {
            ...coordinates,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          };

          setRegion(newRegion);

          if (mapRef.current) {
            mapRef.current.animateToRegion(newRegion, 300);
          }

          setLatitudeLongitude(String(coordinates.latitude), String(coordinates.longitude));
          setSearchQuery(place.name);
          setShowResults(false);
        }
      } catch (error) {
        console.error("Error selecting place:", error);
        Alert.alert("Error", "Failed to get location coordinates. Please try again.");
      }
    },
    [getPlaceDetails, mapRef, setLatitudeLongitude, setRegion, setSearchQuery, setShowResults],
  );

  const requestLocationPermission = useCallback(async () => {
    let {status} = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission denied", "Allow location access to use this feature.");
      return;
    }

    try {
      setIsLoading(true);
      let location = await Location.getCurrentPositionAsync({});
      const {latitude, longitude} = location.coords;

      const newRegion = {
        latitude,
        longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };

      setRegion(newRegion);

      if (mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 300);
      }

      // Set the latitude and longitude in the store
      setLatitudeLongitude(String(latitude), String(longitude));
    } catch (error) {
      console.error("Error getting current location:", error);
      Alert.alert("Error", "Failed to get your current location");
    } finally {
      setIsLoading(false);
    }
  }, [setLatitudeLongitude]);

  const zoomIn = () => {
    if (region) {
      const newRegion = {
        ...region,
        latitudeDelta: region.latitudeDelta / 2,
        longitudeDelta: region.longitudeDelta / 2,
      };
      setRegion(newRegion);

      if (mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 300);
      }
    }
  };

  const zoomOut = () => {
    if (region) {
      const newRegion = {
        ...region,
        latitudeDelta: region.latitudeDelta * 2,
        longitudeDelta: region.longitudeDelta * 2,
      };
      setRegion(newRegion);

      if (mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 300);
      }
    }
  };
  useEffect(() => {
    requestLocationPermission();
  }, [requestLocationPermission]);
  return (
    <View className="flex-1 relative">
      <MapView
        style={StyleSheet.absoluteFill}
        region={region || undefined}
        provider={"google"}
        ref={mapRef}
        showsUserLocation
        zoomEnabled={true}
        onRegionChangeComplete={(val) => {
          setLatitudeLongitude(String(val.latitude), String(val.longitude));
        }}
        key={1}
      ></MapView>

      {/* Center Pin */}
      <View
        className="z-50 items-center absolute"
        style={{
          alignSelf: "center",
          top: "43%",
          shadowColor: "#00660A",
          shadowOffset: {width: 0, height: 4},
          shadowOpacity: 0.25,
          shadowRadius: 21,
          elevation: 8,
        }}
      >
        <PinIcon fill={"red"} height={60} width={60} />
      </View>

      {/* Search Bar */}
      <View className="px-4 mt-4 z-20">
        <View
          className="bg-white flex-row items-center space-x-2 px-2 rounded-[4px]"
          style={{
            shadowColor: "#314533",
            shadowOffset: {width: 0, height: 8},
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 8,
          }}
        >
          <SearchIcon />
          <TextInput
            placeholder="Search for area, street name..."
            placeholderTextColor={"#627164"}
            className="flex-1 h-[44px]"
            value={searchQuery}
            onChangeText={handleSearchInputChange}
            returnKeyType="search"
          />
          {isLoading && <ActivityIndicator size="small" color="#00660A" />}
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery("");
                setSearchResults([]);
                setShowResults(false);
              }}
              className="p-2"
            >
              <Text className="text-[#627164]">Clear</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Search Results */}
        {showResults && searchResults.length > 0 && (
          <View
            className="bg-white rounded-[4px] mt-1 z-30"
            style={{
              shadowColor: "#314533",
              shadowOffset: {width: 0, height: 4},
              shadowOpacity: 0.2,
              shadowRadius: 3,
              elevation: 5,
              maxHeight: 300,
            }}
          >
            <FlatList
              data={searchResults}
              keyExtractor={(item) => item.id}
              renderItem={({item}) => (
                <TouchableOpacity
                  className="p-3 border-b border-gray-200"
                  onPress={() => handleSelectPlace(item)}
                >
                  <Text className="text-[#00660A] font-[500] text-[16px]">{item.name}</Text>
                  <Text className="text-[#627164] text-[14px] mt-1" numberOfLines={2}>
                    {item.description}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        )}
      </View>

      {/* Current Location Button */}
      <TouchableOpacity
        onPress={() => {
          if (mapRef.current && region) {
            mapRef.current.animateToRegion(region, 0);
          } else {
            requestLocationPermission();
          }
        }}
        className="flex-row absolute bottom-[3%] bg-[#FFFFFF] left-[25%] space-x-2 py-1 px-4 border-[1px] border-[#00660A] rounded-[12px]"
      >
        <LocationIcon />
        <View>
          <Text className="font-[500] text-[16px] text-[#00660A]">Use current location</Text>
        </View>
      </TouchableOpacity>

      {/* Zoom Controls */}
      <View className="justify-center absolute bottom-2 right-6 bg-[#FFF] py-2 border-[1px] border-[#00660A]">
        <TouchableOpacity
          onPress={zoomOut}
          className="w-[24px] h-[24px] items-center justify-center mb-4"
        >
          <MinIcon />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={zoomIn}
          className="w-[24px] h-[24px] items-center justify-center"
        >
          <Plus />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default index;
