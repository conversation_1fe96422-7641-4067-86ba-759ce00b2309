<dependencies>
  <compile
      roots=":@@:expo-modules-core::release,com.facebook.react:react-android:0.76.7:release@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.7.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.compose.material:material-android:1.7.8@aar,androidx.compose.material:material-ripple-android:1.7.8@aar,androidx.compose.foundation:foundation-layout-android:1.7.8@aar,androidx.compose.foundation:foundation-android:1.7.8@aar,androidx.compose.animation:animation-core-android:1.7.8@aar,androidx.compose.animation:animation-android:1.7.8@aar,androidx.compose.ui:ui-util-android:1.7.8@aar,androidx.compose.ui:ui-unit-android:1.7.8@aar,androidx.compose.ui:ui-text-android:1.7.8@aar,androidx.compose.ui:ui-graphics-android:1.7.8@aar,androidx.compose.ui:ui-geometry-android:1.7.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar,androidx.compose.material:material-icons-extended:1.3.1@aar,androidx.compose.material:material-icons-core:1.3.1@aar,androidx.compose.ui:ui-android:1.7.8@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.core:core-ktx:1.13.1@aar,androidx.compose.runtime:runtime-saveable-android:1.7.8@aar,androidx.compose.runtime:runtime-android:1.7.8@aar,androidx.annotation:annotation-experimental:1.4.1@aar,com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.facebook.fresco:fresco:3.2.0@aar,com.facebook.fresco:middleware:3.2.0@aar,com.facebook.fresco:ui-common:3.2.0@aar,com.facebook.fresco:fbcore:3.2.0@aar,com.facebook.fresco:imagepipeline:3.2.0@aar,com.facebook.fresco:imagepipeline-base:3.2.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-jvm:1.4.4@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar,org.jetbrains:annotations:23.0.0@jar,androidx.tracing:tracing:1.2.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.6.0@aar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.soloader:nativeloader:0.12.1@jar,com.facebook.soloader:annotation:0.12.1@jar,com.facebook.fresco:drawee:3.2.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:3.2.0@aar,com.facebook.fresco:memory-type-ashmem:3.2.0@aar,com.facebook.fresco:memory-type-native:3.2.0@aar,com.facebook.fresco:memory-type-java:3.2.0@aar,com.facebook.fresco:nativeimagefilters:3.2.0@aar,com.facebook.fresco:nativeimagetranscoder:3.2.0@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,javax.inject:javax.inject:1@jar">
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="artifacts::expo-modules-core"/>
    <dependency
        name="com.facebook.react:react-android:0.76.7:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.7.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended:1.3.1@aar"
        simpleName="androidx.compose.material:material-icons-extended"/>
    <dependency
        name="androidx.compose.material:material-icons-core:1.3.1@aar"
        simpleName="androidx.compose.material:material-icons-core"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.facebook.fresco:fresco:3.2.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:middleware:3.2.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.2.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.2.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.6.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.fresco:drawee:3.2.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
  </compile>
  <package
      roots=":@@:expo-modules-core::release,com.facebook.react:react-android:0.76.7:release@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.compose.material:material-android:1.7.8@aar,androidx.compose.foundation:foundation-layout-android:1.7.8@aar,androidx.compose.material:material-ripple-android:1.7.8@aar,androidx.compose.foundation:foundation-android:1.7.8@aar,androidx.compose.animation:animation-core-android:1.7.8@aar,androidx.compose.animation:animation-android:1.7.8@aar,androidx.compose.ui:ui-util-android:1.7.8@aar,androidx.compose.ui:ui-unit-android:1.7.8@aar,androidx.compose.ui:ui-text-android:1.7.8@aar,androidx.compose.ui:ui-graphics-android:1.7.8@aar,androidx.compose.ui:ui-geometry-android:1.7.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar,androidx.compose.material:material-icons-extended:1.3.1@aar,androidx.compose.material:material-icons-core:1.3.1@aar,androidx.compose.ui:ui-android:1.7.8@aar,androidx.autofill:autofill:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,com.facebook.fresco:fresco:3.2.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar,com.facebook.fresco:drawee:3.2.0@aar,com.facebook.fresco:nativeimagefilters:3.2.0@aar,com.facebook.fresco:memory-type-native:3.2.0@aar,com.facebook.fresco:memory-type-java:3.2.0@aar,com.facebook.fresco:imagepipeline-native:3.2.0@aar,com.facebook.fresco:memory-type-ashmem:3.2.0@aar,com.facebook.fresco:imagepipeline:3.2.0@aar,com.facebook.fresco:nativeimagetranscoder:3.2.0@aar,com.facebook.fresco:imagepipeline-base:3.2.0@aar,com.facebook.fresco:middleware:3.2.0@aar,com.facebook.fresco:ui-common:3.2.0@aar,com.facebook.fresco:soloader:3.2.0@aar,com.facebook.fresco:fbcore:3.2.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity-ktx:1.7.0@aar,androidx.activity:activity:1.7.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-process:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.core:core-ktx:1.13.1@aar,org.jetbrains.kotlin:kotlin-reflect:1.9.25@jar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.9.2@jar,com.squareup.okio:okio:2.9.0@jar,androidx.compose.runtime:runtime-saveable-android:1.7.8@aar,androidx.compose.runtime:runtime-android:1.7.8@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.4.4@jar,androidx.collection:collection-jvm:1.4.4@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar,com.facebook.fbjni:fbjni:0.6.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,javax.inject:javax.inject:1@jar,org.jetbrains:annotations:23.0.0@jar,com.facebook.soloader:nativeloader:0.12.1@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.12.1@jar,com.parse.bolts:bolts-tasks:1.4.0@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="artifacts::expo-modules-core"/>
    <dependency
        name="com.facebook.react:react-android:0.76.7:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.8@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended:1.3.1@aar"
        simpleName="androidx.compose.material:material-icons-extended"/>
    <dependency
        name="androidx.compose.material:material-icons-core:1.3.1@aar"
        simpleName="androidx.compose.material:material-icons-core"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="com.facebook.fresco:fresco:3.2.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:drawee:3.2.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.2.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.2.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.2.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.fresco:middleware:3.2.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.2.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:soloader:3.2.0@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.2.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity-ktx:1.7.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.7.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:2.9.0@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.8@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.4@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.4@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.6.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
