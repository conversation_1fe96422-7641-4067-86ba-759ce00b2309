# C/C++ build system timings
generate_cxx_metadata
  [gap of 67ms]
  create-invalidation-state 134ms
  generate-prefab-packages
    [gap of 66ms]
    exec-prefab 937ms
    [gap of 116ms]
  generate-prefab-packages completed in 1119ms
  execute-generate-process
    exec-configure 2278ms
    [gap of 397ms]
  execute-generate-process completed in 2677ms
  [gap of 106ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 4123ms

