import Bell from "../../assets/icons/Bell_Icon.svg";
import MyModule from "@/modules/my-module/src/MyModule";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { useRef, useState } from "react";
import ToggleComponent from "../ToggleComponent/ToggleComponent";
import { router } from "expo-router";
import { Alert, Text, TextInput, TouchableOpacity, View } from "react-native";

const ShopHeader = () => {
  const [Toggle, setToggle] = useState(false);
  const [NotificationsCount, setNotificationsCount] = useState(0);

  return (
    <View className="items-center justify-end mb-4 bg-[#FFFFFF] mt-6">
      <View className="flex-row items-center relative justify-center">
        {/* Toggle Button Area */}
        <View className=" absolute right-[35%] top-[0] items-end justify-center mr-5">
          <ToggleComponent
            setToggle={(val) => {
              setToggle(val);
            }}
          />
        </View>
        <Text className="font-[500] text-[20px] leading-[30px] text-center">Your Orders</Text>
        {/* Bell Icon Area */}

        <View className="absolute left-[50%]">
          <TouchableOpacity
            onPress={() => {
              router.push({
                pathname: "/home/<USER>",
              });
            }}
            className="relative"
          >
            <Bell fill={"#00660A"} />
            {NotificationsCount > 0 ? (
              <>
                <View className="absolute left-3 bottom-3 w-[20px] bg-red-700 items-center justify-center rounded-full">
                  <Text className="font-[500] text-[#FFFFFF]">{NotificationsCount}</Text>
                </View>
              </>
            ) : (
              <></>
            )}
          </TouchableOpacity>
        </View>
      </View>
      <View className="mt-10 relative h-[20px]">
        {!Toggle && (
          <Text className=" font-[400] text-[12px] leading-[18px]">
            You will not receive any orders when the shop is
            <Text className="font-[500]"> closed</Text>
          </Text>
        )}
      </View>
    </View>
  );
};

export default ShopHeader;
