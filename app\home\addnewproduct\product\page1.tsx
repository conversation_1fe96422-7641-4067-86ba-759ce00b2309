import AddproductField from "../../../../component/AddproductField";
import AppleImg from "../../../../assets/images/AppleIcon.png";
import Close from "@/assets/icons/bottemSheetIcon/Close.svg";
import CustomFiled from "../../../../component/CustomFiled";
import CustomFiled2 from "../../../../component/CustomFiled2";
import CustomFiled3 from "../../../../component/CustomFiled3";
import FileUploder from "../../../../component/FileUploder";
import GStarIcon from "../../../../assets/OrderDetails/star.svg";
import OnOffButton from "../../../../component/OnOffButton";
import PriceFieldComponent from "../../../../component/PriceFieldComponent";
import ProductOptionComponent from "../../../../component/ProductOptionComponent";
import React, {useContext, useEffect, useState} from "react";
import RsIcon from "@/assets/icons/₹Icon.svg";
import SmallPressable from "../../../../component/SmallPressable";
import VegIcon from "../../../../assets/OrderDetails/Veg.svg";
import resImage from "../../../../assets/Img/CarouselImg.png";
import useGetApiData from "../../../../hooks/useGetApiData";
import useGetApiDataFun from "../../../../hooks/useGetApiDataFun";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStack from "@/hook/TenStackHook/useTenStack";
import useUpdateWithFormData from "../../../../hooks/useUpdateWithFormData";
import {useRoute} from "@react-navigation/native";
import {router, useLocalSearchParams} from "expo-router";
import {Controller, useForm} from "react-hook-form";
import {Image, Text, TextInput, TouchableOpacity, View} from "react-native";
import {useVariantIDoptionID} from "../../../../store/VariantIDoptionID";
import {TableComponent, TableComponentForPage1} from "../addOptons";
import {GlobalPagenumber} from "./_layout";

const page1 = () => {
  const {prd_id} = useLocalSearchParams();
  const {page, setpage} = useContext(GlobalPagenumber);
  const {VariantID, optionID, removeAllVariant, removeAllOption} = useVariantIDoptionID(
    (state) => state,
  );
  const route = useRoute();

  const {
    data: ProductDetails,
    isLoading,
    refetch: triggerreFresh,
  } = useTenStack({
    endpoint: "/auth/productDetails",
    pram: {product_id: prd_id},
    key: "productDetails",
    id: [prd_id.toString()],
    refetch: true,
  });

  useEffect(() => {
    setpage(route.name);
    return () => {
      removeAllOption();
      removeAllVariant();
    };
  }, []);

  return (
    <>
      {isLoading ? (
        <></>
      ) : (
        <>
          <View className="mt-4">
            <View>
              <Text className="font-[400] text-[18px]">Variants (Optional)</Text>
            </View>
            {ProductDetails?.data?.product_variants?.map((items) => {
              if (items.variant_name === "null" || items.variant_name === null) {
                return null;
              }
              return (
                <>
                  <View className="px-4 mb-4 mt-6" key={items?.id}>
                    <VariantProductCard
                      hide={true}
                      id={items?.id}
                      price={items.price}
                      name={items.variant_name}
                      image={items.image}
                      qty={items.unit}
                      product_id={items.product_id}
                      Refresh={triggerreFresh}
                      prd_tag_name={items.prd_tag_name}
                      description={items.description}
                    />
                  </View>
                </>
              );
            })}

            <View className="mt-4">
              <SmallPressable
                text={"Add Variant"}
                style={{width: 154}}
                pressfun={() => {
                  router.push({
                    pathname: "/home/<USER>/addvariantspage",
                    params: {prodict_id: prd_id},
                  });
                }}
              />
            </View>
            <View>
              <View className="mt-4">
                <Text className="font-[400] text-[18px]">Options (Optional)</Text>
              </View>
            </View>
            {ProductDetails?.data?.product_options?.map((items) => {
              return (
                <View className="px-4 mb-4 mt-6" key={items?.id}>
                  <OptionsProductCard
                    id={items?.id}
                    hide={true}
                    price={items.price}
                    name={items.name}
                    image={items.image}
                    qty={items.qty}
                    product_id={items.product_id}
                    Refresh={triggerreFresh}
                    prd_tag_name={items.prd_tag_name}
                    description={items.description}
                  />
                </View>
              );
            })}

            <View className="mt-4">
              <SmallPressable
                text={"Add Options"}
                style={{width: 154}}
                pressfun={() => {
                  router.push({
                    pathname: "/home/<USER>/addOptons",
                    params: {prodict_id: prd_id},
                  });
                }}
              />
            </View>
            <View className="mt-10">
              <TouchableOpacity
                className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
                onPress={() => {
                  router.push({
                    pathname: "/home/<USER>/product/page2",
                    params: {
                      id: prd_id,
                    },
                  });
                }}
              >
                <Text className="font-[400] text-[#fff] text-[16px]">Next</Text>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )}
    </>
  );
};

export const AddVariantComponents = (id) => {
  const {
    control,
    setValue,
    handleSubmit,
    watch,
    setError,
    formState: {errors},
  } = useForm();
  const {UpdateBankDetails: AddProductVariants} = useUpdateWithFormData({
    endpoint: "productVariants",
  });
  const formdata = new FormData();
  const [IsSlash, setIsSlash] = useState(false);
  const [ActualAmount, setActualAmount] = useState("400");
  const [SlashAmount, setSlashAmount] = useState("350");

  useEffect(() => {
    setValue("IsSlash", IsSlash);
  }, [IsSlash]);

  useEffect(() => {
    return () => {
      handleSubmit((data) => {
        formdata.append("product_id", id.id);
        formdata.append("price", data.price);
        formdata.append("unit", data.unit);
        formdata.append("qty", data.qty);
        formdata.append("is_discounted_prd", Number(data?.IsSlash));
        formdata.append("slash_price", data.slashedamount);
        formdata.append("image", {
          uri: data.productimage,
          name: data.productimage,
          filename: data.productimage,
          type: "image/png",
        });
        AddProductVariants(formdata);
      })();
    };
  }, []);
  useEffect(() => {
    if (Number(watch("slashedamount")) >= Number(watch("actualamount"))) {
      setError("actualamount", {
        message: "Discou|nted amount must be lesser than the actual amount.",
      });
      return;
    }
    setError("actualamount", {
      message: "",
    });
  }, [watch("slashedamount"), watch("actualamount")]);
  return (
    <>
      <View className="mt-2">
        <PriceFieldComponent
          neme={"price"}
          control={control}
          text={"Enter price"}
          placeholder={"Enter price"}
          KeyType={"numeric"}
        />
      </View>

      <CustomFiled3
        text={"Enter qty"}
        name1={"qty"}
        name2={"unit"}
        placeholder={"Enter qty!"}
        KeyType={"numeric"}
        data={[
          {label: "Kg", value: "1"},
          {label: "lb", value: "2"},
          {label: "oz", value: "3"},
          {label: "g", value: "4"},
        ]}
        control={control}
      />

      <View className="flex-row items-center justify-between my-3 mt-4">
        <View className="flex-shrink">
          <Text className="font-[400] text-[16px] leading-[20px]">
            Increase conversion by slash pricing ?
          </Text>
        </View>
        <View className="">
          <OnOffButton setTaxables={setIsSlash} />
        </View>
      </View>
      {IsSlash ? (
        <>
          <View className="flex-row items-center border-[1px] border-[#627164] justify-center p-4 mt-4 w-full space-x-5">
            <View className="flex-1">
              <Text className="font-[400] text-[16px] leading-[20px] text-[#4D4D4D]">
                Actual Amount
              </Text>
              <View className="pl-2 rounded-[4px] flex-row items-center border-[1px] border-[#ACB9D5] h-[40px] mt-2">
                <View>
                  <RsIcon />
                </View>
                <View>
                  <Controller
                    name="actualamount"
                    control={control}
                    defaultValue={ActualAmount}
                    render={({field: {onChange, value}}) => {
                      return (
                        <>
                          <TextInput
                            className="pl-2 font-[500] text-[16px] leading-[24px]"
                            value={value}
                            keyboardType="numeric"
                            onChangeText={onChange}
                            editable={IsSlash}
                          />
                        </>
                      );
                    }}
                  />
                </View>
              </View>
            </View>
            <View className="flex-1">
              <Text className="font-[400] text-[16px] leading-[20px] text-[#4D4D4D]">
                Discounted Amount
              </Text>
              <View className="pl-2 rounded-[4px] flex-row items-center border-[1px] border-[#ACB9D5] h-[40px] mt-2">
                <View>
                  <RsIcon />
                </View>
                <View>
                  <Controller
                    name="slashedamount"
                    control={control}
                    rules={{}}
                    defaultValue={SlashAmount}
                    render={({field: {onChange, value}}) => {
                      return (
                        <>
                          <TextInput
                            className="pl-2 font-[500] text-[16px] leading-[24px] flex-1"
                            value={value}
                            onChangeText={onChange}
                            editable={IsSlash}
                            keyboardType="numeric"
                          />
                        </>
                      );
                    }}
                  />
                </View>
              </View>
            </View>
          </View>
          {errors?.actualamount?.message ? (
            <View className="relative">
              <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                {errors?.actualamount?.message}
              </Text>
            </View>
          ) : (
            <></>
          )}
        </>
      ) : (
        <></>
      )}
      <FileUploder title={"Upload an Image (Optional)"} text={"Browse Files"} setValue={setValue} />
    </>
  );
};

export const VariantProductCard = (props) => {
  const Tags = [
    {
      Tag: "Veg",
      image: VegIcon,
    },
    {
      Tag: "Best Seller",
      image: VegIcon,
    },
    {
      Tag: "Featured Product",
      image: VegIcon,
    },
  ];

  return (
    <>
      <View className="flex-row ">
        <View className="mt-2 space-y-1 flex-1">
          <View>
            <Text className="text-[18px]">{props?.name}</Text>
          </View>
          {/* Tag Area */}
          {props?.prd_tag_name && props?.prd_tag_name != "" && (
            <View className="flex-row flex flex-wrap">
              <TagComponent Tag={props?.prd_tag_name} image={VegIcon} />;
            </View>
          )}

          <View className=""></View>
          <View className="flex-row items-center my-4">
            <RsIcon fill={"#000"} />
            <Text>{props?.price}</Text>
          </View>
          <View className="flex-row items-center space-x-2 my-4">
            <GStarIcon fill={0 > 0 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 1 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 2 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 3 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 4 ? "#DBB900" : "#fff"} height={20} width={20} />
            <Text>{5} ratings</Text>
          </View>
        </View>
        <View className="items-center relative">
          <View className="relative rounded-[10px] mt-8 overflow-hidden">
            <TouchableOpacity>
              <Image
                source={{uri: props?.image}}
                style={{
                  width: 100,
                  height: 100,
                }}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </>
  );
};
export const OptionsProductCard = (props) => {
  const Tags = [
    {
      Tag: "Veg",
      image: VegIcon,
    },
    {
      Tag: "Best Seller",
      image: VegIcon,
    },
    {
      Tag: "Featured Product",
      image: VegIcon,
    },
  ];
  const {removeOptionID} = useVariantIDoptionID((state) => state);
  return (
    <>
      <View className="flex-row ">
        <View className="mt-2 space-y-1 flex-1 ">
          <View>
            <Text className="text-[18px]">{props?.name}</Text>
          </View>
          {/* Tag Area */}
          {props?.prd_tag_name && props?.prd_tag_name != "" && (
            <View className="flex-row flex flex-wrap">
              {/* {Tags.map(({ Tag, image }, index) => { */}
              {/* if (Tag === "Out of Stock") { */}
              {/* return null; */}
              {/* } */}
              {/* return  */}
              <TagComponent Tag={props?.prd_tag_name} image={VegIcon} />;{/* })} */}
            </View>
          )}
          <View className=""></View>
          <View className="flex-row items-center my-4">
            <RsIcon fill={"#000"} />
            <Text>{props?.price}</Text>
          </View>
          <View className="flex-row items-center space-x-2 my-4">
            <GStarIcon fill={0 > 0 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 1 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 2 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 3 ? "#DBB900" : "#fff"} height={20} width={20} />
            <GStarIcon fill={0 > 4 ? "#DBB900" : "#fff"} height={20} width={20} />
            <Text>{5} ratings</Text>
          </View>
        </View>
        <View className="items-center relative">
          <View className="relative rounded-[10px] mt-8 overflow-hidden">
            <TouchableOpacity>
              <Image
                source={{uri: props?.image}}
                style={{
                  width: 100,
                  height: 100,
                }}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </>
  );
};

export const TagComponent = ({Tag, image}) => {
  return (
    <View className="flex-row space-x-1 items-center mr-3 my-1">
      <View className="">
        <VegIcon />
      </View>
      <View className="">
        <Text className="text-[18] font-[500]">{Tag}</Text>
      </View>
    </View>
  );
};
export default page1;
