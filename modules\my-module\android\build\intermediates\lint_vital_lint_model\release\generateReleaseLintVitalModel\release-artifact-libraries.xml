<libraries>
  <library
      name=":@@:expo-modules-core::release"
      project=":expo-modules-core"/>
  <library
      name="com.facebook.react:react-android:0.76.7:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ed95a07aaabdffd864bffdea2e8e32\transformed\react-android-0.76.7-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.76.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ed95a07aaabdffd864bffdea2e8e32\transformed\react-android-0.76.7-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69a44a0644e0c0b6fca06fc8288a055a\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69a44a0644e0c0b6fca06fc8288a055a\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277b49928061acbe92dd621a1b8ff2f4\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277b49928061acbe92dd621a1b8ff2f4\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19ff53384693d0f6beef34db290cc9dc\transformed\activity-1.7.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19ff53384693d0f6beef34db290cc9dc\transformed\activity-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b019702394fcc443b4eceaa0ea78d5c\transformed\appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b019702394fcc443b4eceaa0ea78d5c\transformed\appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e05e892ffdecb0732be449d87380a\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e05e892ffdecb0732be449d87380a\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dcb3ad2c2538f41fcb1b97b90d977d3\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dcb3ad2c2538f41fcb1b97b90d977d3\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6a72d476a7494b99277f23eeedc14ef\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6a72d476a7494b99277f23eeedc14ef\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a10b339848a815b69026484a411d6ca2\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a10b339848a815b69026484a411d6ca2\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2d17da914960c339f5bea3240e52229\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2d17da914960c339f5bea3240e52229\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a05afd0d31c7655980d4b84a3e9757e\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a05afd0d31c7655980d4b84a3e9757e\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3216f2adefe504969e0c8b0a7cdae13c\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3216f2adefe504969e0c8b0a7cdae13c\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73f3b86f835712171f34baf1a782bddb\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73f3b86f835712171f34baf1a782bddb\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e855765b125d9644e5c8409b0d061ab0\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e855765b125d9644e5c8409b0d061ab0\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcc83b6d199baa75966179b6d9876f08\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcc83b6d199baa75966179b6d9876f08\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92b001d3df7e2cf8f19fe0630b26b2a0\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92b001d3df7e2cf8f19fe0630b26b2a0\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a62f16cf1f0c4f8eb16d2af2df20e434\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a62f16cf1f0c4f8eb16d2af2df20e434\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3033776e3c9ca44f96772e8990fa6d5\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3033776e3c9ca44f96772e8990fa6d5\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7845994356070ced85cd0b73971ae46\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7845994356070ced85cd0b73971ae46\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbe86ba9d4b9fb54515b1318ca14e93\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbe86ba9d4b9fb54515b1318ca14e93\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1077de31dd378c052abb416d1270635d\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1077de31dd378c052abb416d1270635d\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a146bbdae329663c4aced4888a0edf6\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a146bbdae329663c4aced4888a0edf6\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8b10df4343c65bf02f6cb9488312ff\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8b10df4343c65bf02f6cb9488312ff\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\319f0058eda9a66ad76ef2dbc84a107d\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\319f0058eda9a66ad76ef2dbc84a107d\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96741bc27aff22eed429c57bbdbaaaf\transformed\material-icons-extended-1.3.1\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96741bc27aff22eed429c57bbdbaaaf\transformed\material-icons-extended-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f102158f81c56012abcc67945711546e\transformed\material-icons-core-1.3.1\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f102158f81c56012abcc67945711546e\transformed\material-icons-core-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45ec1cf61ca13cfcbcc6941817029dcc\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45ec1cf61ca13cfcbcc6941817029dcc\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3fc3dba45a92221c2e78928cfc8facb\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3fc3dba45a92221c2e78928cfc8facb\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6911ef2a324fbed9db31cdfa0b836ea9\transformed\lifecycle-livedata-core-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6911ef2a324fbed9db31cdfa0b836ea9\transformed\lifecycle-livedata-core-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\411cca4a384151624c98e91bcc19b688\transformed\lifecycle-livedata-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\411cca4a384151624c98e91bcc19b688\transformed\lifecycle-livedata-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2a672124dc447ca92bb88d1f4dad9d\transformed\lifecycle-viewmodel-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2a672124dc447ca92bb88d1f4dad9d\transformed\lifecycle-viewmodel-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91344d32faf30e676e4e5f395154711e\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91344d32faf30e676e4e5f395154711e\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.3\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaeebc8edb44ba465dac823abdc05aff\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaeebc8edb44ba465dac823abdc05aff\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf843d7ebb2eea0e13d0e21ef81e5f71\transformed\lifecycle-livedata-core-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf843d7ebb2eea0e13d0e21ef81e5f71\transformed\lifecycle-livedata-core-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b73cca706b8829b4950fb697f5ac7552\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b73cca706b8829b4950fb697f5ac7552\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1127f1bf76d3700b799e542ec25356\transformed\lifecycle-viewmodel-savedstate-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1127f1bf76d3700b799e542ec25356\transformed\lifecycle-viewmodel-savedstate-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2c002668649c83afa304418f73149cb\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2c002668649c83afa304418f73149cb\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad60c46f32b30faee0b3757b8f21857\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad60c46f32b30faee0b3757b8f21857\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f9aeb484852c5450fb5d2c7bd8d610a\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f9aeb484852c5450fb5d2c7bd8d610a\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c49d5c535c7de2a2cf3fa0c02da448\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c49d5c535c7de2a2cf3fa0c02da448\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5cca2ed6726add394dab4eef21cb8c\transformed\imagepipeline-okhttp3-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5cca2ed6726add394dab4eef21cb8c\transformed\imagepipeline-okhttp3-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.9.2\5302714ee9320b64cf65ed865e5f65981ef9ba46\okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\2.9.0\dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a\okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.facebook.fresco:fresco:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9925f9c2ffd4b94044f5bf7d1dff1a5\transformed\fresco-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9925f9c2ffd4b94044f5bf7d1dff1a5\transformed\fresco-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d962002c56e992670ee7d67269620f32\transformed\middleware-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d962002c56e992670ee7d67269620f32\transformed\middleware-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bccb47424fc99b0187e1d197016c6190\transformed\ui-common-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bccb47424fc99b0187e1d197016c6190\transformed\ui-common-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b29a999ef87f766f9f214299fbcb8e4\transformed\fbcore-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b29a999ef87f766f9f214299fbcb8e4\transformed\fbcore-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2367e964e0f1f5ee2a3dc498bfaf46f4\transformed\imagepipeline-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2367e964e0f1f5ee2a3dc498bfaf46f4\transformed\imagepipeline-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e73173aab5f33dc9de471b2e927f30d\transformed\imagepipeline-base-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e73173aab5f33dc9de471b2e927f30d\transformed\imagepipeline-base-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.21\67f57e154437cd9e6e9cf368394b95814836ff88\kotlin-stdlib-jdk8-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.21"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4cd95fc9f54988bfdb95d2ba21bb820\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4cd95fc9f54988bfdb95d2ba21bb820\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a579b0d591648ab94bd0fd96f50696c\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a579b0d591648ab94bd0fd96f50696c\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bfd4e134c9e519eaff7d824602c80c5\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bfd4e134c9e519eaff7d824602c80c5\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bf45d5719b48f9742cee2f29dfce124\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bf45d5719b48f9742cee2f29dfce124\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85079bcc7068b62137b6ad0a4506a99c\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85079bcc7068b62137b6ad0a4506a99c\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.4\da13a7e557c430276b8cb490420effebc1398c0d\collection-jvm-1.4.4.jar"
      resolved="androidx.collection:collection-jvm:1.4.4"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.25\f700a2f2b8f0d6d0fde48f56d894dc722fb029d7\kotlin-stdlib-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.25"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.25\1c166692314a2639e5edfed0d23ed7eee4a5c7a5\kotlin-stdlib-jdk7-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbd1b6b69a7cdda61098539c0d8a7d3b\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbd1b6b69a7cdda61098539c0d8a7d3b\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\837aa0b22b89b14f7150be2e7705724f\transformed\autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\837aa0b22b89b14f7150be2e7705724f\transformed\autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc6538d1dfd58568ef78eef8c5ee9673\transformed\fbjni-0.6.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc6538d1dfd58568ef78eef8c5ee9673\transformed\fbjni-0.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91393d0dba507860921dc26adee0980a\transformed\soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91393d0dba507860921dc26adee0980a\transformed\soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.fresco:drawee:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa67e21413141b19b8d4c6e1aa699226\transformed\drawee-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa67e21413141b19b8d4c6e1aa699226\transformed\drawee-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9117208a6c6a5ba881bd97452e15f22\transformed\imagepipeline-native-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9117208a6c6a5ba881bd97452e15f22\transformed\imagepipeline-native-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c923340b9a032ad3f1ead61a82094596\transformed\memory-type-ashmem-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c923340b9a032ad3f1ead61a82094596\transformed\memory-type-ashmem-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f767419faf9a450875ef24851150bd65\transformed\memory-type-native-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f767419faf9a450875ef24851150bd65\transformed\memory-type-native-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ba63ec9ff701c55cb2a30d68ae9031\transformed\memory-type-java-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ba63ec9ff701c55cb2a30d68ae9031\transformed\memory-type-java-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca65da19d8094a94b8a62ef42792ce3c\transformed\nativeimagefilters-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca65da19d8094a94b8a62ef42792ce3c\transformed\nativeimagefilters-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c161910e6ec05395af87e854d759efe\transformed\nativeimagetranscoder-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c161910e6ec05395af87e854d759efe\transformed\nativeimagetranscoder-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.facebook.fresco:soloader:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef86200a6e1e3034a22465f8e6e9d9\transformed\soloader-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef86200a6e1e3034a22465f8e6e9d9\transformed\soloader-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6d36c3869d27a40f788d9488d6c501\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6d36c3869d27a40f788d9488d6c501\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29705d9049f035062877e1d92f79ef81\transformed\activity-ktx-1.7.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29705d9049f035062877e1d92f79ef81\transformed\activity-ktx-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b550573d4eb3f07dad2ea98a68c4808b\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b550573d4eb3f07dad2ea98a68c4808b\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ad34e2f7d6f6ce1d71ed3d731a13e9\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ad34e2f7d6f6ce1d71ed3d731a13e9\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd9fd70c99fdb05a5033ffc1b08b01fe\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd9fd70c99fdb05a5033ffc1b08b01fe\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f759fd6dc8518586b6e7af61693d3a18\transformed\lifecycle-viewmodel-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f759fd6dc8518586b6e7af61693d3a18\transformed\lifecycle-viewmodel-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943471b0f40b86fe6327d29c4f536927\transformed\lifecycle-process-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943471b0f40b86fe6327d29c4f536927\transformed\lifecycle-process-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.9.25\73023c38b7b20430232893cf9b556dc8486e07a4\kotlin-reflect-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:1.9.25"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4123e3f849b24d389d1cca28d989a749\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4123e3f849b24d389d1cca28d989a749\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13115e4846c6b060c3413b79605781ec\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13115e4846c6b060c3413b79605781ec\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.4\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.4.jar"
      resolved="androidx.collection:collection-ktx:1.4.4"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
