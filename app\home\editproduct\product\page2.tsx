import CaloriesComponent from "../../../../component/Caloriescomponent";
import DropDownComponent from "../../../../component/DropDownComponent";
import PIcon from "@/assets/icons/%.svg";
import React, { useContext, useEffect, useState } from "react";
import SmallPressable from "../../../../component/SmallPressable";
import TextAreaComponent from "../../../../component/TextAreaComponent";
import useGetApiDatawithParam from "../../../../hooks/useGetApiDatawithParam";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStack from "@/hook/TenStackHook/useTenStack";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import useUpdateWithFormData from "../../../../hooks/useUpdateWithFormData";
import { router, useLocalSearchParams } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { ActivityIndicator, Alert, Text, TextInput, TouchableOpacity, View } from "react-native";
import { GlobalPagenumber } from "./_layout";

const page2 = () => {
  const {page, setpage} = useContext(GlobalPagenumber);
  const [Caloriescomp, srtCaloriescomp] = useState([]);
  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors},
  } = useForm();
  const {id} = useLocalSearchParams();

  const {
    data,
    isLoading,
    refetch: triggerreFresh,
  } = useTenStack({
    endpoint: "/auth/productDetails",
    pram: {product_id: id},
    key: "productDetails",
    id: [id.toString()],
    refetch: true,
  });
  const {mutate: productIngredientsEdit} = useTenStackMutateD({
    endpoint: "auth/productIngredients",
    invalidateQueriesKey: ["productDetails"],
  });
  const {mutate: productAllergeninformationEdit} = useTenStackMutateD({
    endpoint: "auth/productInformation",
    invalidateQueriesKey: ["productDetails"],
  });
  useEffect(() => {
    setValue(
      "ingredients",
      data?.data?.product_ingredients[data?.data?.product_ingredients.length - 1]?.description,
    );
    setValue(
      "Allergeninformation",
      data?.data?.product_information[data?.data?.product_information.length - 1]?.description,
    );
  }, [isLoading]);

  const tags = [
    {label: "Vegetarian", value: "1"},
    {label: "Non-Vegetarian", value: "2"},
    {label: "Jaivik Bharat Logo", value: "3"},
    {label: "FSSAI Logo", value: "4"},
    {label: "BIS Certification", value: "5"},
    {label: "Cruelty-Free", value: "6"},
    {label: "Vegan", value: "7"},
    {label: "Food Grade Material", value: "8"},
    {label: "Recyclable Packaging", value: "9"},
  ];

  console.log(JSON.stringify(data?.data?.product_nutritions, null, 2));

  return (
    <>
      {isLoading ? (
        <>
          <ActivityIndicator />
        </>
      ) : (
        <>
          <View className="mt-4">
            <View className="">
              <View className="">
                <Text className="font-[400] text-[18px]">Tag (Optional)</Text>
              </View>
              <View className="mb-4">
                <DropDownComponent
                  control={control}
                  setvaluefun={setValue}
                  name={"Tags"}
                  data={tags}
                  defaul={tags.find((tag) => tag.label === data?.data?.prd_tag_name)?.label}
                  text={"Select Tags"}
                  placeholder={"Select Options"}
                  // defaul={"Seller"}
                />
              </View>
            </View>
            <View>
              <Text className="font-[400] text-[18px]">Nutritional Information (Optional)</Text>
            </View>
            <View className="">
              <>
                <CaloriesComponent
                  key={0}
                  id={{id: id}}
                  control={control}
                  errors={errors}
                  setValue={setValue}
                  handleSubmit={handleSubmit}
                />
              </>
            </View>
            <View className="mt-4">
              <TextAreaComponent
                text={"Ingredients"}
                name={"ingredients"}
                control={control}
                defaul={data?.data?.product_ingredients?.description}
                placeholder={"Name the ingredients present in the product separated by “,”."}
              />
            </View>
            <View className="mt-5">
              <TextAreaComponent
                text={"Allergen Information:"}
                control={control}
                name={"Allergeninformation"}
                defaul={data?.data?.product_information?.description}
                placeholder={"Name the ingredients present in the product separated by “,”."}
              />
            </View>
            <View className="mt-10">
              <TouchableOpacity
                className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
                onPress={handleSubmit((datas) => {
                  let success = true;
                  productIngredientsEdit(
                    {
                      product_id: id,
                      description: datas.ingredients,
                      tag_id: tags.find((tag) => tag.label === datas?.Tags)?.value,
                    },
                    {
                      onError: (val) => {
                        success = false;
                        Alert.alert("Faild", val.message);
                      },
                    },
                  );
                  productAllergeninformationEdit(
                    {product_id: id, description: datas?.Allergeninformation},
                    {
                      onSuccess: () => {},
                      onError: (val) => {
                        success = false;
                        Alert.alert("Faild", val.message);
                      },
                    },
                  );
                  if (success) {
                    setpage(page + 1);
                    router.push({
                      pathname: "/home/<USER>/product/page3",
                      params: {id: id},
                    });
                  }
                })}
              >
                <Text className="font-[400] text-[#fff] text-[16px]">Next</Text>
              </TouchableOpacity>
            </View>
          </View>
        </>
      )}
    </>
  );
};

const EditCaloriesComponent = ({id, product_id, no_kcal, nutrient, quantity}) => {
  const {control, handleSubmit, setValue} = useForm();
  const {SetFunction: UpdateproductNutritions} = useSetApiData({
    endpoint: "productNutritionsEdit",
  });
  useEffect(() => {
    setValue("kcal", no_kcal);
    setValue("nutrient", nutrient);
    setValue("quantity", quantity);
    return () => {
      handleSubmit((datas) => {
        UpdateproductNutritions({
          id: id,
          product_id: product_id,
          no_kcal: datas.kcal,
          nutrient: datas.nutrient,
          quantity: datas.quantity,
        });
      })();
    };
  }, []);
  return (
    <View className="mb-2">
      <View className="flex-row items-center">
        <Controller
          name="kcal"
          control={control}
          render={({field: {onChange, value}}) => {
            return (
              <>
                <TextInput
                  onChangeText={onChange}
                  value={value}
                  placeholder="Enter Number of Calories in the product"
                  placeholderTextColor={"#B3B3B3"}
                  className="border-[1px] border-[#ACB9D5] flex-1 rounded-[4px] pl-2 mt-2 h-[40px]"
                />
              </>
            );
          }}
        />
        <View className="ml-2">
          <Text>Kcal </Text>
        </View>
      </View>
      <View className="flex-row space-x-4 items-center ">
        <View className="w-[130px]">
          <Controller
            name="nutrient"
            control={control}
            render={({field: {onChange, value}}) => {
              return (
                <>
                  <TextInput
                    onChangeText={onChange}
                    value={value}
                    placeholder="Nutrient"
                    placeholderTextColor={"#B3B3B3"}
                    className="border-[1px] border-[#ACB9D5] rounded-[4px] pl-2 h-[40px] mt-2"
                  />
                </>
              );
            }}
          />
        </View>
        <View className="w-[130px]">
          <Controller
            name="quantity"
            control={control}
            render={({field: {onChange, value}}) => {
              return (
                <>
                  <TextInput
                    onChangeText={onChange}
                    value={value}
                    placeholder="Quantity"
                    placeholderTextColor={"#B3B3B3"}
                    className="border-[1px] border-[#ACB9D5]  rounded-[4px] pl-2 h-[40px] mt-2"
                  />
                </>
              );
            }}
          />
        </View>
        <View>
          <Text className="">
            <PIcon />
          </Text>
        </View>
      </View>
    </View>
  );
};

export default page2;
