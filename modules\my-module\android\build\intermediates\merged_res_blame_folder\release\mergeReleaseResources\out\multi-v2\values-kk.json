{"logs": [{"outputFile": "expo.modules.mymodule.my-module-mergeReleaseResources-51:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45ec1cf61ca13cfcbcc6941817029dcc\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,1004,1089,1162,1236,1312,1386,1462,1532", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,999,1084,1157,1231,1307,1381,1457,1527,1645"}, "to": {"startLines": "36,37,39,40,41,44,45,48,49,50,51,53,54,55,57,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3510,3603,3772,3877,3979,4219,4300,4531,4621,4703,4786,4953,5026,5100,5251,5499,5575,5645", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "3598,3681,3872,3974,4061,4295,4388,4616,4698,4781,4866,5021,5095,5171,5320,5570,5640,5758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92b001d3df7e2cf8f19fe0630b26b2a0\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "63,64", "startColumns": "4,4", "startOffsets": "5763,5855", "endColumns": "91,95", "endOffsets": "5850,5946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\69a44a0644e0c0b6fca06fc8288a055a\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,4871", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,4948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46ed95a07aaabdffd864bffdea2e8e32\\transformed\\react-android-0.76.7-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,432,507", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "136,206,289,359,427,502,575"}, "to": {"startLines": "38,42,43,46,47,56,58", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3686,4066,4136,4393,4463,5176,5325", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "3767,4131,4214,4458,4526,5246,5393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac7076343e9dc5d8b75e982d33fd7fa0\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2796,2891,2993,3095,3198,3302,3399,5398", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "2886,2988,3090,3193,3297,3394,3505,5494"}}]}, {"outputFile": "expo.modules.mymodule.my-module-release-53:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45ec1cf61ca13cfcbcc6941817029dcc\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,1004,1089,1162,1236,1312,1386,1462,1532", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,999,1084,1157,1231,1307,1381,1457,1527,1645"}, "to": {"startLines": "36,37,39,40,41,44,45,48,49,50,51,53,54,55,57,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3510,3603,3772,3877,3979,4219,4300,4531,4621,4703,4786,4953,5026,5100,5251,5499,5575,5645", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "3598,3681,3872,3974,4061,4295,4388,4616,4698,4781,4866,5021,5095,5171,5320,5570,5640,5758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92b001d3df7e2cf8f19fe0630b26b2a0\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "63,64", "startColumns": "4,4", "startOffsets": "5763,5855", "endColumns": "91,95", "endOffsets": "5850,5946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\69a44a0644e0c0b6fca06fc8288a055a\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,4871", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,4948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46ed95a07aaabdffd864bffdea2e8e32\\transformed\\react-android-0.76.7-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,432,507", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "136,206,289,359,427,502,575"}, "to": {"startLines": "38,42,43,46,47,56,58", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3686,4066,4136,4393,4463,5176,5325", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "3767,4131,4214,4458,4526,5246,5393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac7076343e9dc5d8b75e982d33fd7fa0\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2796,2891,2993,3095,3198,3302,3399,5398", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "2886,2988,3090,3193,3297,3394,3505,5494"}}]}]}