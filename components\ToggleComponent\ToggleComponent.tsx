import RBSheet from "react-native-raw-bottom-sheet";
import React, { useEffect, useRef, useState } from "react";
import ToggleSwitch from "toggle-switch-react-native";
import useSetApiData from "../../hooks/useSetApiData";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import { Alert, Text, TextInput, TouchableOpacity, View } from "react-native";
import { useDastBoard } from "../../store/Auth/Dashboard/Dashboardstore";

interface Props {
  setToggle: React.Dispatch<React.SetStateAction<boolean>>;
}

const ToggleComponent = (props: Props) => {
  const [Toggle, setToggle] = useState(false);
  const {mutate: SetFunction} = useTenStackMutateD({
    endpoint: "auth/shop_status_change",
    invalidateQueriesKey: ["dashboard"],
  });
  const shopId = useDastBoard((state) => state.shopId);
  const is_shop_open = useDastBoard((state) => state.dashboardData?.is_shop_open);
  useEffect(() => {
    if (is_shop_open !== undefined) {
      setToggle(is_shop_open);
    }
  }, []);
  useEffect(() => {
    props.setToggle(Toggle);
  }, [Toggle]);
  const [PreparationTimeInMin, setPreparationTimeInMin] = useState("");
  const [PreparationTimeInhr, setPreparationTimeInhr] = useState("");

  const refRBSheet = useRef<any>();

  const OpenpopUp = () => {
    refRBSheet.current?.open();
  };

  const handleSavePreparationTime = () => {
    // Validate inputs
    const minutes = parseInt(PreparationTimeInMin) || 0;
    const hours = parseInt(PreparationTimeInhr) || 0;

    if (minutes < 0 || minutes > 59) {
      Alert.alert("Invalid Input", "Minutes should be between 0 and 59");
      return;
    }

    if (hours < 0) {
      Alert.alert("Invalid Input", "Hours should be 0 or greater");
      return;
    }

    if (minutes === 0 && hours === 0) {
      Alert.alert("Invalid Input", "Please enter at least some preparation time");
      return;
    }

    // Here you can save the preparation time or perform any action
    console.log(`Preparation Time: ${hours} hours and ${minutes} minutes`);
    SetFunction(
      {
        shop_id: shopId,
        type: Toggle ? 0 : 1,
      },
      {
        onSuccess: (data) => {
          setToggle(!Toggle);
        },
        onError: (error) => {
          console.log(error);
        },
      },
    );
    refRBSheet.current?.close();
  };

  const Timer = useRef<any>();
  const [countdown, setCountdown] = useState(5);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const refCloseConfirmSheet = useRef<any>();

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (Timer.current) {
        clearInterval(Timer.current);
      }
    };
  }, []);

  const startCountdown = () => {
    setCountdown(5);
    setIsTimerActive(true);

    Timer.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(Timer.current);
          setIsTimerActive(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const stopCountdown = () => {
    if (Timer.current) {
      clearInterval(Timer.current);
    }
    setIsTimerActive(false);
    setCountdown(5);
  };

  const openCloseTimer = () => {
    startCountdown();
    refCloseConfirmSheet.current?.open();
  };

  const handleConfirmClose = () => {
    stopCountdown();
    refCloseConfirmSheet.current?.close();
    // Perform the actual shop close action here
    console.log("Shop closing confirmed");
    SetFunction(
      {
        shop_id: shopId,
        type: Toggle ? 0 : 1,
      },
      {
        onSuccess: (data) => {
          setToggle(!Toggle);
        },
        onError: (error) => {
          console.log(error);
        },
      },
    );
  };

  const handleCancelClose = () => {
    stopCountdown();
    refCloseConfirmSheet.current?.close();
  };
  return (
    <View className="items-start">
      <ToggleSwitch
        isOn={Toggle}
        onColor="#28C979"
        offColor="#D13434"
        size="medium"
        onToggle={() => {
          if (Toggle) {
            openCloseTimer();
          } else if (!Toggle) {
            OpenpopUp();
          }
        }}
      />
      <Text
        className="font-[500] text-[14px] leading-[16px] mt-1"
        style={{
          color: Toggle ? "#28C979" : "#D13434",
        }}
      >
        {Toggle ? "Shop Open" : "Shop Closed"}
      </Text>

      {/* Preparation Time Modal */}
      <RBSheet
        ref={refRBSheet}
        customStyles={{
          container: {
            borderRadius: 20,
            flex: 1,
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="px-6 py-6">
          <Text className="font-[500] text-[20px] leading-[30px] text-center mb-6">
            Enter Order Preparation Time
          </Text>

          <View className="space-y-4">
            {/* Hours Input */}
            <View>
              <Text className="font-[400] text-[16px] text-[#4D4D4D] mb-2">Hours</Text>
              <TextInput
                value={PreparationTimeInhr}
                onChangeText={setPreparationTimeInhr}
                keyboardType="numeric"
                placeholder="Enter hours (e.g., 2)"
                placeholderTextColor="#c3bbbb"
                className="bg-[#fff] h-[48px] px-3 rounded-[4px] border-[1px] border-[#ACB9D5]"
              />
            </View>

            {/* Minutes Input */}
            <View>
              <Text className="font-[400] text-[16px] text-[#4D4D4D] mb-2">Minutes</Text>
              <TextInput
                value={PreparationTimeInMin}
                onChangeText={setPreparationTimeInMin}
                keyboardType="numeric"
                placeholder="Enter minutes (0-59)"
                placeholderTextColor="#c3bbbb"
                className="bg-[#fff] h-[48px] px-3 rounded-[4px] border-[1px] border-[#ACB9D5]"
              />
            </View>
          </View>

          {/* Action Buttons */}
          <View className="flex-row justify-between mt-8 space-x-4">
            <TouchableOpacity
              onPress={() => refRBSheet.current?.close()}
              className="flex-1 h-[48px] bg-[#E5E5E5] items-center justify-center rounded-[4px]"
            >
              <Text className="font-[400] text-[16px] text-[#4D4D4D]">Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleSavePreparationTime}
              className="flex-1 h-[48px] bg-[#00660A] items-center justify-center rounded-[4px]"
            >
              <Text className="font-[400] text-[16px] text-[#FFFFFF]">Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </RBSheet>

      {/* Close Confirmation Modal */}
      <RBSheet
        ref={refCloseConfirmSheet}
        customStyles={{
          container: {
            borderRadius: 20,
            // flex: 1,
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="px-6 py-4 flex-1 justify-center">
          <Text className="font-[500] text-[20px] leading-[30px] text-center mb-6">
            Are you sure you want to close the shop?
          </Text>

          {isTimerActive && (
            <View className="items-center mb-6">
              <Text className="font-[400] text-[16px] text-[#4D4D4D] mb-2">
                Please wait before confirming
              </Text>
              <View className="w-16 h-16 rounded-full border-4 border-[#E5E5E5] items-center justify-center">
                <Text className="font-[600] text-[24px] text-[#D13434]">{countdown}</Text>
              </View>
            </View>
          )}

          {!isTimerActive && countdown === 0 && (
            <View className="items-center mb-6">
              <Text className="font-[400] text-[16px] text-[#28C979] text-center">
                You can now confirm your action
              </Text>
            </View>
          )}

          {/* Action Buttons */}
          <View className="flex-row justify-between space-x-4">
            <TouchableOpacity
              onPress={handleCancelClose}
              className="flex-1 h-[48px] bg-[#E5E5E5] items-center justify-center rounded-[4px]"
            >
              <Text className="font-[400] text-[16px] text-[#4D4D4D]">Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleConfirmClose}
              disabled={isTimerActive}
              className={`flex-1 h-[48px] items-center justify-center rounded-[4px] ${
                isTimerActive ? "bg-[#CCCCCC]" : "bg-[#00660A]"
              }`}
            >
              <Text
                className={`font-[400] text-[16px] ${
                  isTimerActive ? "text-[#888888]" : "text-[#FFFFFF]"
                }`}
              >
                {isTimerActive ? `Wait ${countdown}s` : "Close Shop"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </RBSheet>
    </View>
  );
};

export default ToggleComponent;
