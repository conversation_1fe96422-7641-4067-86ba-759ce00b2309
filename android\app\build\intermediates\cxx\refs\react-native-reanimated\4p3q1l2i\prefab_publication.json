{"installationFolder": "F:\\Apps\\Seller\\my-app\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\release\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "F:\\Apps\\Seller\\my-app\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": []}, {"moduleName": "worklets", "moduleHeaders": "F:\\Apps\\Seller\\my-app\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": []}]}}