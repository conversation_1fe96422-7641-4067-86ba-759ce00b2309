<libraries>
  <library
      name=":@@:expo::release"
      project=":expo"/>
  <library
      name=":@@:react-native-screens::release"
      project=":react-native-screens"/>
  <library
      name="com.facebook.react:react-android:0.76.7:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ed95a07aaabdffd864bffdea2e8e32\transformed\react-android-0.76.7-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.76.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ed95a07aaabdffd864bffdea2e8e32\transformed\react-android-0.76.7-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-av::release"
      project=":expo-av"/>
  <library
      name=":@@:expo-file-system::release"
      project=":expo-file-system"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5cca2ed6726add394dab4eef21cb8c\transformed\imagepipeline-okhttp3-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5cca2ed6726add394dab4eef21cb8c\transformed\imagepipeline-okhttp3-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.google.android.exoplayer:extension-okhttp:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1d83235dd4b8c32a8cfe28c35dd4edf\transformed\extension-okhttp-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:extension-okhttp:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1d83235dd4b8c32a8cfe28c35dd4edf\transformed\extension-okhttp-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.9.2\5302714ee9320b64cf65ed865e5f65981ef9ba46\okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\2.9.0\dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a\okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name=":@@:expo-modules-core::release"
      project=":expo-modules-core"/>
  <library
      name=":@@:expo-camera::release"
      project=":expo-camera"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3249e21276bba18144d9024586d5bf5b\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3249e21276bba18144d9024586d5bf5b\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac0c37e0e76c2a94d1587768fd1f019\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac0c37e0e76c2a94d1587768fd1f019\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-device::release"
      project=":expo-device"/>
  <library
      name=":@@:expo-sharing::release"
      project=":expo-sharing"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54d20c22cb7228030b65f00814a2606\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54d20c22cb7228030b65f00814a2606\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-image-loader::release"
      project=":expo-image-loader"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b2a953813faba7c66f67db252d32507\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b2a953813faba7c66f67db252d32507\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-location::release"
      project=":expo-location"/>
  <library
      name="com.google.android.gms:play-services-location:21.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b725e0d6f4ac46866229f15b32020a3\transformed\play-services-location-21.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-location:21.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b725e0d6f4ac46866229f15b32020a3\transformed\play-services-location-21.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5d2036565c74c4e638e3487ea5ab118\transformed\play-services-base-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5d2036565c74c4e638e3487ea5ab118\transformed\play-services-base-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d41c782fc844f731da63ce419aefdbe7\transformed\play-services-tasks-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d41c782fc844f731da63ce419aefdbe7\transformed\play-services-tasks-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e16d6529afca942d16389a4fdc6ce28\transformed\play-services-basement-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e16d6529afca942d16389a4fdc6ce28\transformed\play-services-basement-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\825c802345e15660dc928e5bfdf00678\transformed\fragment-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\825c802345e15660dc928e5bfdf00678\transformed\fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b377161f2ad38988e02988aab369f2d7\transformed\activity-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b377161f2ad38988e02988aab369f2d7\transformed\activity-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3be749d2b10608eaab1957a43cc02bf7\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3be749d2b10608eaab1957a43cc02bf7\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70afc5874f691a98f4150292ba122a10\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70afc5874f691a98f4150292ba122a10\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c17d730da8c4496472225a8893c060e\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c17d730da8c4496472225a8893c060e\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e05e892ffdecb0732be449d87380a\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c83e05e892ffdecb0732be449d87380a\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-web-browser::release"
      project=":expo-web-browser"/>
  <library
      name="androidx.browser:browser:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad102f9843b748a9314544e5ee34d7be\transformed\browser-1.6.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad102f9843b748a9314544e5ee34d7be\transformed\browser-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d61813dc19c9c76d649cc7be356e572\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d61813dc19c9c76d649cc7be356e572\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6a72d476a7494b99277f23eeedc14ef\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6a72d476a7494b99277f23eeedc14ef\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a10b339848a815b69026484a411d6ca2\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a10b339848a815b69026484a411d6ca2\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08b531a012aa345c4a0f9184486b314c\transformed\exoplayer-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08b531a012aa345c4a0f9184486b314c\transformed\exoplayer-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-ui:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c124de8ddeee39bac6e88250caecc71f\transformed\exoplayer-ui-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-ui:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c124de8ddeee39bac6e88250caecc71f\transformed\exoplayer-ui-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.4.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49eca366ca8549061ae289232fd34643\transformed\media-1.4.3\jars\classes.jar"
      resolved="androidx.media:media:1.4.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49eca366ca8549061ae289232fd34643\transformed\media-1.4.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2d17da914960c339f5bea3240e52229\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2d17da914960c339f5bea3240e52229\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f5ad293588ab52b1d9fe8e71c8cac06\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f5ad293588ab52b1d9fe8e71c8cac06\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f126f1015faaaa25e0806a8149de6358\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f126f1015faaaa25e0806a8149de6358\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e581c8dc36e9938bda2e7d684b8b3b4\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e581c8dc36e9938bda2e7d684b8b3b4\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40d1ff04e0125464a012049d086d2006\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40d1ff04e0125464a012049d086d2006\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac7076343e9dc5d8b75e982d33fd7fa0\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.3\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2a672124dc447ca92bb88d1f4dad9d\transformed\lifecycle-viewmodel-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2a672124dc447ca92bb88d1f4dad9d\transformed\lifecycle-viewmodel-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91344d32faf30e676e4e5f395154711e\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91344d32faf30e676e4e5f395154711e\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b73cca706b8829b4950fb697f5ac7552\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b73cca706b8829b4950fb697f5ac7552\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf843d7ebb2eea0e13d0e21ef81e5f71\transformed\lifecycle-livedata-core-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf843d7ebb2eea0e13d0e21ef81e5f71\transformed\lifecycle-livedata-core-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1127f1bf76d3700b799e542ec25356\transformed\lifecycle-viewmodel-savedstate-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb1127f1bf76d3700b799e542ec25356\transformed\lifecycle-viewmodel-savedstate-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2c002668649c83afa304418f73149cb\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2c002668649c83afa304418f73149cb\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bfd4e134c9e519eaff7d824602c80c5\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bfd4e134c9e519eaff7d824602c80c5\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9925f9c2ffd4b94044f5bf7d1dff1a5\transformed\fresco-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9925f9c2ffd4b94044f5bf7d1dff1a5\transformed\fresco-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d962002c56e992670ee7d67269620f32\transformed\middleware-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d962002c56e992670ee7d67269620f32\transformed\middleware-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bccb47424fc99b0187e1d197016c6190\transformed\ui-common-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bccb47424fc99b0187e1d197016c6190\transformed\ui-common-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.facebook.fresco:fbcore:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b29a999ef87f766f9f214299fbcb8e4\transformed\fbcore-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b29a999ef87f766f9f214299fbcb8e4\transformed\fbcore-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2367e964e0f1f5ee2a3dc498bfaf46f4\transformed\imagepipeline-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2367e964e0f1f5ee2a3dc498bfaf46f4\transformed\imagepipeline-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e73173aab5f33dc9de471b2e927f30d\transformed\imagepipeline-base-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e73173aab5f33dc9de471b2e927f30d\transformed\imagepipeline-base-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name=":@@:expo-constants::release"
      project=":expo-constants"/>
  <library
      name=":@@:expo-document-picker::release"
      project=":expo-document-picker"/>
  <library
      name=":@@:expo-haptics::release"
      project=":expo-haptics"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a579b0d591648ab94bd0fd96f50696c\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a579b0d591648ab94bd0fd96f50696c\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bf45d5719b48f9742cee2f29dfce124\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bf45d5719b48f9742cee2f29dfce124\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95facb40e96d750c90869bd1266e1b18\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95facb40e96d750c90869bd1266e1b18\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4cd95fc9f54988bfdb95d2ba21bb820\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4cd95fc9f54988bfdb95d2ba21bb820\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e6c333d3a6b9c4d199ad4ce919d62ab\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e6c333d3a6b9c4d199ad4ce919d62ab\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6452234df67c79fec1b1a6d2e6d506a6\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6452234df67c79fec1b1a6d2e6d506a6\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2de48a26fc808c6890ae701192e2438\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2de48a26fc808c6890ae701192e2438\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.4\da13a7e557c430276b8cb490420effebc1398c0d\collection-jvm-1.4.4.jar"
      resolved="androidx.collection:collection-jvm:1.4.4"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c49d5c535c7de2a2cf3fa0c02da448\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c49d5c535c7de2a2cf3fa0c02da448\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.25\1c166692314a2639e5edfed0d23ed7eee4a5c7a5\kotlin-stdlib-jdk7-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.25"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.25\f700a2f2b8f0d6d0fde48f56d894dc722fb029d7\kotlin-stdlib-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.25"/>
  <library
      name=":@@:react-native-async-storage_async-storage::release"
      project=":react-native-async-storage_async-storage"/>
  <library
      name=":@@:react-native-community_datetimepicker::release"
      project=":react-native-community_datetimepicker"/>
  <library
      name=":@@:react-native-gesture-handler::release"
      project=":react-native-gesture-handler"/>
  <library
      name=":@@:react-native-get-random-values::release"
      project=":react-native-get-random-values"/>
  <library
      name=":@@:react-native-html-to-pdf::release"
      project=":react-native-html-to-pdf"/>
  <library
      name=":@@:react-native-maps::release"
      project=":react-native-maps"/>
  <library
      name=":@@:react-native-razorpay::release"
      project=":react-native-razorpay"/>
  <library
      name=":@@:react-native-reanimated::release"
      project=":react-native-reanimated"/>
  <library
      name=":@@:react-native-safe-area-context::release"
      project=":react-native-safe-area-context"/>
  <library
      name=":@@:react-native-svg::release"
      project=":react-native-svg"/>
  <library
      name=":@@:react-native-view-shot::release"
      project=":react-native-view-shot"/>
  <library
      name=":@@:react-native-webview::release"
      project=":react-native-webview"/>
  <library
      name="com.facebook.fresco:animated-gif:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ab05c3bc714b1c4bd941d85dfb57754\transformed\animated-gif-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-gif:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ab05c3bc714b1c4bd941d85dfb57754\transformed\animated-gif-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\480c21d1b44d8771234e08c069d215b4\transformed\webpsupport-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:webpsupport:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\480c21d1b44d8771234e08c069d215b4\transformed\webpsupport-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.76.7:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d737b571aa14fa99550749c5c6ccc7b2\transformed\hermes-android-0.76.7-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.76.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d737b571aa14fa99550749c5c6ccc7b2\transformed\hermes-android-0.76.7-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-core:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81cae9d45941aaa456a5ba5cac11b14f\transformed\exoplayer-core-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-core:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81cae9d45941aaa456a5ba5cac11b14f\transformed\exoplayer-core-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-common:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a22716d664535b9074f358b2dcb6ba1d\transformed\exoplayer-common-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-common:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a22716d664535b9074f358b2dcb6ba1d\transformed\exoplayer-common-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:31.0.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.0.1-android\b400db548e748a1a60ba18543ad4fbb44a675a69\guava-31.0.1-android.jar"
      resolved="com.google.guava:guava:31.0.1-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbd1b6b69a7cdda61098539c0d8a7d3b\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbd1b6b69a7cdda61098539c0d8a7d3b\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\837aa0b22b89b14f7150be2e7705724f\transformed\autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\837aa0b22b89b14f7150be2e7705724f\transformed\autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc6538d1dfd58568ef78eef8c5ee9673\transformed\fbjni-0.6.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc6538d1dfd58568ef78eef8c5ee9673\transformed\fbjni-0.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91393d0dba507860921dc26adee0980a\transformed\soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91393d0dba507860921dc26adee0980a\transformed\soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.fresco:drawee:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa67e21413141b19b8d4c6e1aa699226\transformed\drawee-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa67e21413141b19b8d4c6e1aa699226\transformed\drawee-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9117208a6c6a5ba881bd97452e15f22\transformed\imagepipeline-native-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9117208a6c6a5ba881bd97452e15f22\transformed\imagepipeline-native-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c923340b9a032ad3f1ead61a82094596\transformed\memory-type-ashmem-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c923340b9a032ad3f1ead61a82094596\transformed\memory-type-ashmem-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f767419faf9a450875ef24851150bd65\transformed\memory-type-native-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f767419faf9a450875ef24851150bd65\transformed\memory-type-native-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ba63ec9ff701c55cb2a30d68ae9031\transformed\memory-type-java-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9ba63ec9ff701c55cb2a30d68ae9031\transformed\memory-type-java-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca65da19d8094a94b8a62ef42792ce3c\transformed\nativeimagefilters-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca65da19d8094a94b8a62ef42792ce3c\transformed\nativeimagefilters-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c161910e6ec05395af87e854d759efe\transformed\nativeimagetranscoder-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c161910e6ec05395af87e854d759efe\transformed\nativeimagetranscoder-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="com.google.android.exoplayer:exoplayer-database:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f61d8e68ccb6d1e96bfc108a00e2488f\transformed\exoplayer-database-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-database:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f61d8e68ccb6d1e96bfc108a00e2488f\transformed\exoplayer-database-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6caff6be1283f33d860027724ea9acd0\transformed\exoplayer-datasource-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-datasource:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6caff6be1283f33d860027724ea9acd0\transformed\exoplayer-datasource-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\854a2707b0fab935b92086abbae7fce4\transformed\exoplayer-decoder-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-decoder:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\854a2707b0fab935b92086abbae7fce4\transformed\exoplayer-decoder-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca3329c17ce5e69ec06ff92173ebaa28\transformed\exoplayer-extractor-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-extractor:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca3329c17ce5e69ec06ff92173ebaa28\transformed\exoplayer-extractor-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-dash:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7d82f6c2bc4a075714c1793db83a2f4\transformed\exoplayer-dash-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-dash:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7d82f6c2bc4a075714c1793db83a2f4\transformed\exoplayer-dash-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-hls:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf1ac65a3c7646358da374deb32fefa\transformed\exoplayer-hls-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-hls:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bf1ac65a3c7646358da374deb32fefa\transformed\exoplayer-hls-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b251f720a1ee41907be8c6a87df664f\transformed\exoplayer-rtsp-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-rtsp:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b251f720a1ee41907be8c6a87df664f\transformed\exoplayer-rtsp-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\538e4296b15cba1d7e54f2c71195ece9\transformed\exoplayer-smoothstreaming-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\538e4296b15cba1d7e54f2c71195ece9\transformed\exoplayer-smoothstreaming-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-media-library::release"
      project=":expo-media-library"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\380b7cbaccdae466c5e7a4a54e0177d3\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\380b7cbaccdae466c5e7a4a54e0177d3\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.6\815893df5f31da2ece4040fe0a12fd44b577afaf\commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.device.yearclass\yearclass\2.1.0\ef7d013a0140137b4a948dd65b46a08205d21020\yearclass-2.1.0.jar"
      resolved="com.facebook.device.yearclass:yearclass:2.1.0"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.10\4b95f4897fa13f2cd904aee711aeafc0c5295cd8\commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name=":@@:expo-application::release"
      project=":expo-application"/>
  <library
      name=":@@:expo-asset::release"
      project=":expo-asset"/>
  <library
      name=":@@:expo-font::release"
      project=":expo-font"/>
  <library
      name=":@@:expo-image-picker::release"
      project=":expo-image-picker"/>
  <library
      name=":@@:expo-keep-awake::release"
      project=":expo-keep-awake"/>
  <library
      name=":@@:expo-linear-gradient::release"
      project=":expo-linear-gradient"/>
  <library
      name=":@@:expo-linking::release"
      project=":expo-linking"/>
  <library
      name=":@@:expo-notifications::release"
      project=":expo-notifications"/>
  <library
      name=":@@:expo-print::release"
      project=":expo-print"/>
  <library
      name=":@@:expo-splash-screen::release"
      project=":expo-splash-screen"/>
  <library
      name=":@@:expo-system-ui::release"
      project=":expo-system-ui"/>
  <library
      name=":@@:my-module::release"
      project=":my-module"/>
  <library
      name="com.google.maps.android:android-maps-utils:3.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c95cb72c41fea165ef66e03e2027abf\transformed\android-maps-utils-3.8.2\jars\classes.jar"
      resolved="com.google.maps.android:android-maps-utils:3.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c95cb72c41fea165ef66e03e2027abf\transformed\android-maps-utils-3.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.razorpay:checkout:1.6.41@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\274d8f9d8ee911602b305ac8463b3793\transformed\checkout-1.6.41\jars\classes.jar"
      resolved="com.razorpay:checkout:1.6.41"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\274d8f9d8ee911602b305ac8463b3793\transformed\checkout-1.6.41"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.razorpay:standard-core:1.6.49@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49\jars\classes.jar"
      resolved="com.razorpay:standard-core:1.6.49"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba87d701be7e7a49034143393eaff75\transformed\standard-core-1.6.49"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-wallet:18.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b05dea324a5218e6371e6c1efd649a1b\transformed\play-services-wallet-18.1.3\jars\classes.jar"
      resolved="com.google.android.gms:play-services-wallet:18.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b05dea324a5218e6371e6c1efd649a1b\transformed\play-services-wallet-18.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-maps:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\play-services-maps-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-maps:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\play-services-maps-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe169eea04e9eecdd19a2a50d4c8bb94\transformed\material-1.6.1\jars\classes.jar"
      resolved="com.google.android.material:material:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe169eea04e9eecdd19a2a50d4c8bb94\transformed\material-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-mlkit-vision:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\393ff7155273a87842c24498fb4b77c5\transformed\camera-mlkit-vision-1.4.1\jars\classes.jar"
      resolved="androidx.camera:camera-mlkit-vision:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\393ff7155273a87842c24498fb4b77c5\transformed\camera-mlkit-vision-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-extensions:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1\jars\classes.jar"
      resolved="androidx.camera:camera-extensions:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a99fdacb4df11c69f5371858d0fbc76c\transformed\camera-extensions-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-video:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a48aa1a756ad86376ebe5bbf2f11a47\transformed\camera-video-1.4.1\jars\classes.jar"
      resolved="androidx.camera:camera-video:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a48aa1a756ad86376ebe5bbf2f11a47\transformed\camera-video-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-lifecycle:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\441b3dd1027f5a1d20a104e9f0104064\transformed\camera-lifecycle-1.4.1\jars\classes.jar"
      resolved="androidx.camera:camera-lifecycle:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\441b3dd1027f5a1d20a104e9f0104064\transformed\camera-lifecycle-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-camera2:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1\jars\classes.jar"
      resolved="androidx.camera:camera-camera2:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f867356fd7ec3ae7e6875067d3fe224\transformed\camera-camera2-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-core:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36ebfa91bb5a0ab578d79fe0dde0296e\transformed\camera-core-1.4.1\jars\classes.jar"
      resolved="androidx.camera:camera-core:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36ebfa91bb5a0ab578d79fe0dde0296e\transformed\camera-core-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-view:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57aab009e0a18844479b249d065bb566\transformed\camera-view-1.4.1\jars\classes.jar"
      resolved="androidx.camera:camera-view:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57aab009e0a18844479b249d065bb566\transformed\camera-view-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.CanHub:Android-Image-Cropper:4.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1\jars\classes.jar"
      resolved="com.github.CanHub:Android-Image-Cropper:4.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb06600f7bc8e618c09075f4d839848c\transformed\Android-Image-Cropper-4.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d002207a7409532686ef615f215bbf2\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d002207a7409532686ef615f215bbf2\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-splashscreen:1.2.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf94f49d50dde49d9cb78ebe50944264\transformed\core-splashscreen-1.2.0-alpha02\jars\classes.jar"
      resolved="androidx.core:core-splashscreen:1.2.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf94f49d50dde49d9cb78ebe50944264\transformed\core-splashscreen-1.2.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75c953d4572b28c79c89dee75742d390\transformed\play-services-auth-21.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1909dc1a2b5fa43f9adce0c17b47c0\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1909dc1a2b5fa43f9adce0c17b47c0\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4596016ea60ab8e4d665abaa6b25ed\transformed\firebase-messaging-24.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9089b37efe78b8888b4d22a193c94dd9\transformed\play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9089b37efe78b8888b4d22a193c94dd9\transformed\play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:barcode-scanning:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b1241bac09c65bb5974514a9b3d4918\transformed\barcode-scanning-17.2.0\jars\classes.jar"
      resolved="com.google.mlkit:barcode-scanning:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b1241bac09c65bb5974514a9b3d4918\transformed\barcode-scanning-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\104762ce3ae269dd97274d64f94e1d7d\transformed\play-services-mlkit-barcode-scanning-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14cf8d0d3de2c2c3262c11e18f06f616\transformed\barcode-scanning-common-17.0.0\jars\classes.jar"
      resolved="com.google.mlkit:barcode-scanning-common:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14cf8d0d3de2c2c3262c11e18f06f616\transformed\barcode-scanning-common-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-common:17.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67b00141bd7072ce6b29945fe7bd3e1e\transformed\vision-common-17.3.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-common:17.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67b00141bd7072ce6b29945fe7bd3e1e\transformed\vision-common-17.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:common:18.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0\jars\classes.jar"
      resolved="com.google.mlkit:common:18.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aaafe6ec1bdba06812287d612fb584d\transformed\common-18.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1db6e4038a40f1f92cbbdb7bcfc6e\transformed\play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87d1db6e4038a40f1f92cbbdb7bcfc6e\transformed\play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ab6d22b1ca6f240b2f757f7d6d7ff4f\transformed\play-services-fido-20.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ab6d22b1ca6f240b2f757f7d6d7ff4f\transformed\play-services-fido-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-identity:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dc63c54b92554966ff51ba97915cb55\transformed\play-services-identity-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-identity:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dc63c54b92554966ff51ba97915cb55\transformed\play-services-identity-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e25b31738da884cbca8abf8ecc52c5d\transformed\firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a236b19643a27143e8e909f6de03f9a9\transformed\firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a236b19643a27143e8e909f6de03f9a9\transformed\firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\326d766b793ac315390507a38f72afd7\transformed\firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97636e340524f4e2c04edffef996fcd9\transformed\firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97636e340524f4e2c04edffef996fcd9\transformed\firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63ec498444bc29348689c3cc296c888a\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63ec498444bc29348689c3cc296c888a\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab9a98635a3bb10db7c30747f604b656\transformed\play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab9a98635a3bb10db7c30747f604b656\transformed\play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f8e4934800350b6d392bdb832a8948d\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f8e4934800350b6d392bdb832a8948d\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-interfaces:16.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e388d9016b3cd5beb01833a1c6f78fd0\transformed\vision-interfaces-16.2.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-interfaces:16.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e388d9016b3cd5beb01833a1c6f78fd0\transformed\vision-interfaces-16.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73f3b86f835712171f34baf1a782bddb\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73f3b86f835712171f34baf1a782bddb\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e855765b125d9644e5c8409b0d061ab0\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e855765b125d9644e5c8409b0d061ab0\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcc83b6d199baa75966179b6d9876f08\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcc83b6d199baa75966179b6d9876f08\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92b001d3df7e2cf8f19fe0630b26b2a0\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92b001d3df7e2cf8f19fe0630b26b2a0\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a62f16cf1f0c4f8eb16d2af2df20e434\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a62f16cf1f0c4f8eb16d2af2df20e434\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3033776e3c9ca44f96772e8990fa6d5\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3033776e3c9ca44f96772e8990fa6d5\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7845994356070ced85cd0b73971ae46\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7845994356070ced85cd0b73971ae46\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbe86ba9d4b9fb54515b1318ca14e93\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbe86ba9d4b9fb54515b1318ca14e93\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1077de31dd378c052abb416d1270635d\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1077de31dd378c052abb416d1270635d\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a146bbdae329663c4aced4888a0edf6\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a146bbdae329663c4aced4888a0edf6\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8b10df4343c65bf02f6cb9488312ff\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c8b10df4343c65bf02f6cb9488312ff\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\319f0058eda9a66ad76ef2dbc84a107d\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\319f0058eda9a66ad76ef2dbc84a107d\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96741bc27aff22eed429c57bbdbaaaf\transformed\material-icons-extended-1.3.1\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96741bc27aff22eed429c57bbdbaaaf\transformed\material-icons-extended-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f102158f81c56012abcc67945711546e\transformed\material-icons-core-1.3.1\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f102158f81c56012abcc67945711546e\transformed\material-icons-core-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45ec1cf61ca13cfcbcc6941817029dcc\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45ec1cf61ca13cfcbcc6941817029dcc\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad60c46f32b30faee0b3757b8f21857\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad60c46f32b30faee0b3757b8f21857\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96e30cf90977c3d88ec4752e7bbdc4c\transformed\work-runtime-2.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ea63a4fe231653db904bfdd2282eb52\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ea63a4fe231653db904bfdd2282eb52\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6d36c3869d27a40f788d9488d6c501\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6d36c3869d27a40f788d9488d6c501\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fc9eb23cc171c825fb42d4bb336e5d\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57f5fc5bd2d5ba8d211fd7b01fee35be\transformed\activity-ktx-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57f5fc5bd2d5ba8d211fd7b01fee35be\transformed\activity-ktx-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61326fce931a5f4d9a73b27a0ba4198e\transformed\webkit-1.4.0\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61326fce931a5f4d9a73b27a0ba4198e\transformed\webkit-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a122279ce23eedd4f03b2058f4d4299\transformed\animated-base-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-base:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a122279ce23eedd4f03b2058f4d4299\transformed\animated-base-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c92f2516c8cb201558ee63f756b7fd26\transformed\animated-drawable-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-drawable:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c92f2516c8cb201558ee63f756b7fd26\transformed\animated-drawable-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-options:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16da58e56879bdf8ffbb8b90949b7b77\transformed\vito-options-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-options:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16da58e56879bdf8ffbb8b90949b7b77\transformed\vito-options-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef86200a6e1e3034a22465f8e6e9d9\transformed\soloader-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cef86200a6e1e3034a22465f8e6e9d9\transformed\soloader-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ad34e2f7d6f6ce1d71ed3d731a13e9\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18ad34e2f7d6f6ce1d71ed3d731a13e9\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ab7fce078a98c7f6e0e5e9f91e083ad\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ab7fce078a98c7f6e0e5e9f91e083ad\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b78fb9d012039429ae75be8a6a038e6b\transformed\recyclerview-1.2.1\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b78fb9d012039429ae75be8a6a038e6b\transformed\recyclerview-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b550573d4eb3f07dad2ea98a68c4808b\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b550573d4eb3f07dad2ea98a68c4808b\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd9fd70c99fdb05a5033ffc1b08b01fe\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd9fd70c99fdb05a5033ffc1b08b01fe\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3fc3dba45a92221c2e78928cfc8facb\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3fc3dba45a92221c2e78928cfc8facb\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f759fd6dc8518586b6e7af61693d3a18\transformed\lifecycle-viewmodel-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f759fd6dc8518586b6e7af61693d3a18\transformed\lifecycle-viewmodel-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4031dd3b4fdb5cb7294f5b7cc6243ca1\transformed\lifecycle-service-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4031dd3b4fdb5cb7294f5b7cc6243ca1\transformed\lifecycle-service-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943471b0f40b86fe6327d29c4f536927\transformed\lifecycle-process-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\943471b0f40b86fe6327d29c4f536927\transformed\lifecycle-process-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6911ef2a324fbed9db31cdfa0b836ea9\transformed\lifecycle-livedata-core-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6911ef2a324fbed9db31cdfa0b836ea9\transformed\lifecycle-livedata-core-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\411cca4a384151624c98e91bcc19b688\transformed\lifecycle-livedata-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\411cca4a384151624c98e91bcc19b688\transformed\lifecycle-livedata-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.3\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaeebc8edb44ba465dac823abdc05aff\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaeebc8edb44ba465dac823abdc05aff\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f9aeb484852c5450fb5d2c7bd8d610a\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.8"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f9aeb484852c5450fb5d2c7bd8d610a\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e7eac297418dac0faef8d4e8668c68\transformed\firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60e7eac297418dac0faef8d4e8668c68\transformed\firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\811bb25ff3cb6495f8715afba1898ad8\transformed\fragment-ktx-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\811bb25ff3cb6495f8715afba1898ad8\transformed\fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.9.25\73023c38b7b20430232893cf9b556dc8486e07a4\kotlin-reflect-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:1.9.25"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c0adabdde918d4157b4455b7bccb0\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4123e3f849b24d389d1cca28d989a749\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4123e3f849b24d389d1cca28d989a749\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13115e4846c6b060c3413b79605781ec\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13115e4846c6b060c3413b79605781ec\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-renderer:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\403f8995c918811117a20199946b0282\transformed\vito-renderer-3.2.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-renderer:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\403f8995c918811117a20199946b0282\transformed\vito-renderer-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.25\5ddaf36e1f9708ffd4019de9757ba813bd0a1421\kotlin-parcelize-runtime-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.25"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.25\3c69ebd730fc998c00b5d6d69f637cd12231028\kotlin-android-extensions-runtime-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.25"/>
  <library
      name="androidx.room:room-runtime:2.2.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e4932253ff5ff77949c81309e9f693\transformed\room-runtime-2.2.5\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.2.5"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32e4932253ff5ff77949c81309e9f693\transformed\room-runtime-2.2.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cd504c1f12a3b449dca959e8c3f0f0b\transformed\sqlite-framework-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cd504c1f12a3b449dca959e8c3f0f0b\transformed\sqlite-framework-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f64cafd3fe29a431d5847bb1dab41235\transformed\sqlite-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f64cafd3fe29a431d5847bb1dab41235\transformed\sqlite-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6117cf78197bf4e70a17520ef71c961\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6117cf78197bf4e70a17520ef71c961\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.databinding:viewbinding:7.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6b2bff44ed549a12837f956289ecc0e\transformed\viewbinding-7.2.1\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:7.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6b2bff44ed549a12837f956289ecc0e\transformed\viewbinding-7.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bea1edc8413749ce938c0ae2d144c741\transformed\firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bea1edc8413749ce938c0ae2d144c741\transformed\firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d67bec726af004a41316ce88006c28\transformed\firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d67bec726af004a41316ce88006c28\transformed\firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90653907aa95d9639c07215ccdfe5dbf\transformed\transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af96830dfd953b5fbeb693135fed8dbc\transformed\firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af96830dfd953b5fbeb693135fed8dbc\transformed\firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\543e83001df4f5cc4949fe4615ddca63\transformed\transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cdc481b776b8db96659e6751c4dd3c7\transformed\transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cdc481b776b8db96659e6751c4dd3c7\transformed\transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.room:room-common:2.2.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.2.5\f5e3b73a0c2ab5e276e26868e4ce3542baede207\room-common-2.2.5.jar"
      resolved="androidx.room:room-common:2.2.5"/>
  <library
      name="androidx.collection:collection-ktx:1.4.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.4\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.4.jar"
      resolved="androidx.collection:collection-ktx:1.4.4"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85079bcc7068b62137b6ad0a4506a99c\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85079bcc7068b62137b6ad0a4506a99c\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.tom-roush:pdfbox-android:********@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f1a7712a00f9e40f058acae5183cd3d\transformed\pdfbox-android-********\jars\classes.jar"
      resolved="com.tom-roush:pdfbox-android:********"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f1a7712a00f9e40f058acae5183cd3d\transformed\pdfbox-android-********"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.android.installreferrer:installreferrer:2.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61dfe7560f9e3a6572f85e66e02165eb\transformed\installreferrer-2.2\jars\classes.jar"
      resolved="com.android.installreferrer:installreferrer:2.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61dfe7560f9e3a6572f85e66e02165eb\transformed\installreferrer-2.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-location$io.nlopez.smartlocation-jetified-aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified\jars\classes.jar"
      resolved="artifacts:expo-location$io.nlopez.smartlocation-jetified-aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2275990e69a3af3c6d74fc1a8da23aa\transformed\io.nlopez.smartlocation-3.3.3-jetified"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="me.leolin:ShortcutBadger:1.1.22@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22\jars\classes.jar"
      resolved="me.leolin:ShortcutBadger:1.1.22"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb6fd450b01d5f84a32c39d5f93c10c5\transformed\ShortcutBadger-1.1.22"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.bouncycastle:bcpkix-jdk15to18:1.68@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.bouncycastle\bcpkix-jdk15to18\1.68\f8a3b0a62642ceaca476845241952e559fc8965c\bcpkix-jdk15to18-1.68.jar"
      resolved="org.bouncycastle:bcpkix-jdk15to18:1.68"/>
  <library
      name="org.bouncycastle:bcprov-jdk15to18:1.68@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.bouncycastle\bcprov-jdk15to18\1.68\a569e1ca1fc09ccd2dad64dae1830ea81a309989\bcprov-jdk15to18-1.68.jar"
      resolved="org.bouncycastle:bcprov-jdk15to18:1.68"/>
  <library
      name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.auto.value\auto-value-annotations\1.6.3\b88c1bb7f149f6d2cc03898359283e57b08f39cc\auto-value-annotations-1.6.3.jar"
      resolved="com.google.auto.value:auto-value-annotations:1.6.3"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="com.google.android.odml:image:1.0.0-beta1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae282b31f2848b50c6e83f7a94d7c427\transformed\image-1.0.0-beta1\jars\classes.jar"
      resolved="com.google.android.odml:image:1.0.0-beta1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae282b31f2848b50c6e83f7a94d7c427\transformed\image-1.0.0-beta1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
