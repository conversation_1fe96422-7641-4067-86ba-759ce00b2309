{"logs": [{"outputFile": "expo.modules.mymodule.my-module-release-53:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45ec1cf61ca13cfcbcc6941817029dcc\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,248,249,250,254,256,299,368,369,371,372,373,377,378,383,384,387,388,392,396,399,401,406,407,409,1525,1528,1531", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13883,13942,14001,14061,14121,14181,14241,14301,14361,14421,14481,14541,14601,14660,14720,14780,14840,14900,14960,15020,15080,15140,15200,15260,15319,15379,15439,15498,15557,15616,15675,15734,16348,16422,16480,16694,16779,18927,25077,25142,25366,25432,25533,26045,26097,26663,26725,27021,27071,27414,27894,28235,28376,28962,29009,29144,100493,100605,100716", "endLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,248,249,250,254,256,299,368,369,371,372,373,377,378,383,384,387,388,392,396,399,401,406,407,409,1527,1530,1534", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "13937,13996,14056,14116,14176,14236,14296,14356,14416,14476,14536,14596,14655,14715,14775,14835,14895,14955,15015,15075,15135,15195,15255,15314,15374,15434,15493,15552,15611,15670,15729,15788,16417,16475,16530,16740,16829,18975,25137,25191,25427,25528,25586,26092,26152,26720,26774,27066,27120,27455,27935,28272,28411,29004,29040,29229,100600,100711,100906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19ff53384693d0f6beef34db290cc9dc\\transformed\\activity-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "268,295", "startColumns": "4,4", "startOffsets": "17343,18699", "endColumns": "41,59", "endOffsets": "17380,18754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\277b49928061acbe92dd621a1b8ff2f4\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "252,270,298,2878,2883", "startColumns": "4,4,4,4,4", "startOffsets": "16571,17419,18863,168140,168310", "endLines": "252,270,298,2882,2886", "endColumns": "56,64,63,24,24", "endOffsets": "16623,17479,18922,168305,168454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a146bbdae329663c4aced4888a0edf6\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "253", "startColumns": "4", "startOffsets": "16628", "endColumns": "65", "endOffsets": "16689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8bfd4e134c9e519eaff7d824602c80c5\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "18759", "endColumns": "53", "endOffsets": "18808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46ed95a07aaabdffd864bffdea2e8e32\\transformed\\react-android-0.76.7-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,82,86,90,93,97,101,105,108,111,112,113,122,129,136,139,142,145,151,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,163,226,277,331,390,438,487,536,584,633,691,740,776,826,867,911,955,998,1032,1071,1117,1165,1219,1267,1331,1448,1571,1667,1787,1907,2009,2149,2271,2381,2488,2591,2702,2871,3039,3156,3275,3388,3574,3682,3795,3886,3997,4166,4264,4389,4484,4591,4761,4859,5042,5215,5327,5428,5587,5721,5861,5963,6068,6199,6368,6485,6633,6778,6928,7027,7123,7319,7502,7601,7785,7952,8200,8448,8716,8901,9103,9309,9506,9707,9896,9922,9957,10495,10913,11291,11468,11647,11830,12195,12392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,81,85,89,92,96,100,104,107,110,111,112,121,128,135,138,141,144,150,153,163", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,35,49,40,43,43,42,33,38,45,47,53,47,63,116,122,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "158,221,272,326,385,433,482,531,579,628,686,735,771,821,862,906,950,993,1027,1066,1112,1160,1214,1262,1326,1443,1566,1662,1782,1902,2004,2144,2266,2376,2483,2586,2697,2866,3034,3151,3270,3383,3569,3677,3790,3881,3992,4161,4259,4384,4479,4586,4756,4854,5037,5210,5322,5423,5582,5716,5856,5958,6063,6194,6363,6480,6628,6773,6923,7022,7118,7314,7497,7596,7780,7947,8195,8443,8711,8896,9098,9304,9501,9702,9891,9917,9952,10490,10908,11286,11463,11642,11825,12190,12387,12828"}, "to": {"startLines": "32,33,202,203,204,237,238,239,240,241,242,243,251,257,259,262,263,267,269,288,289,291,292,293,304,333,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,370,374,375,376,379,380,381,382,385,386,389,390,393,394,395,397,398,400,402,403,405,408,410,411,419,423,1510,1514,1517,1521,1535,1754,1757,1833,1880,1881,1890,1897,1904,1907,1910,1913,1919,2065", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2269,2332,13719,13770,13824,15793,15841,15890,15939,15987,16036,16094,16535,16834,16938,17049,17093,17300,17385,18389,18428,18506,18554,18608,19223,21223,21934,22057,22153,22273,22393,22495,22635,22757,22867,22974,23077,23188,23357,23525,23642,23761,23874,24060,24168,24281,24372,24483,24652,24750,24875,24970,25196,25591,25689,25872,26157,26269,26370,26529,26779,26919,27125,27230,27460,27629,27746,27940,28085,28277,28416,28512,28779,29045,29234,29418,30138,30386,99632,99900,100085,100287,100911,116280,116481,123570,126615,126650,127188,127606,127984,128161,128340,128523,128888,139769", "endLines": "32,33,202,203,204,237,238,239,240,241,242,243,251,257,259,262,263,267,269,288,289,291,292,293,304,333,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,370,374,375,376,379,380,381,382,385,386,389,390,393,394,395,397,398,400,402,403,405,408,410,411,422,426,1513,1516,1520,1524,1538,1756,1759,1833,1880,1889,1896,1903,1906,1909,1912,1918,1921,2074", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,35,49,40,43,43,42,33,38,45,47,53,47,63,116,122,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "2327,2390,13765,13819,13878,15836,15885,15934,15982,16031,16089,16138,16566,16879,16974,17088,17132,17338,17414,18423,18469,18549,18603,18651,19282,21335,22052,22148,22268,22388,22490,22630,22752,22862,22969,23072,23183,23352,23520,23637,23756,23869,24055,24163,24276,24367,24478,24647,24745,24870,24965,25072,25361,25684,25867,26040,26264,26365,26524,26658,26914,27016,27225,27356,27624,27741,27889,28080,28230,28371,28507,28703,28957,29139,29413,29580,30381,30629,99895,100080,100282,100488,101103,116476,116665,123591,126645,127183,127601,127979,128156,128335,128518,128883,129080,140205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\837aa0b22b89b14f7150be2e7705724f\\transformed\\autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "159,1864,2029,2030,2037,2042,2047,2054,2716", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10735,125804,137744,137804,138186,138466,138748,139132,161204", "endLines": "159,1879,2029,2036,2041,2046,2053,2062,2729", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "10798,126610,137799,138181,138461,138743,139127,139625,161785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c83e05e892ffdecb0732be449d87380a\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3427", "startColumns": "4", "startOffsets": "183608", "endLines": "3430", "endColumns": "24", "endOffsets": "183774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\91344d32faf30e676e4e5f395154711e\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "297", "startColumns": "4", "startOffsets": "18813", "endColumns": "49", "endOffsets": "18858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\69a44a0644e0c0b6fca06fc8288a055a\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,244,245,246,247,255,265,266,271,290,300,301,302,303,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,391,414,415,416,417,418,427,435,436,440,444,448,453,459,466,470,474,479,483,487,491,495,499,503,509,513,519,523,529,533,538,542,545,549,555,559,565,569,575,578,582,586,590,594,598,599,600,601,604,607,610,613,617,618,619,620,621,624,626,628,630,635,636,640,646,650,651,653,665,666,670,676,680,681,682,686,713,717,718,722,750,922,948,1119,1145,1176,1184,1190,1206,1228,1233,1238,1248,1257,1266,1270,1277,1296,1303,1304,1313,1316,1319,1323,1327,1331,1334,1335,1340,1345,1355,1360,1367,1373,1374,1377,1381,1386,1388,1390,1393,1396,1398,1402,1405,1412,1415,1418,1422,1424,1428,1430,1432,1434,1438,1446,1454,1466,1472,1481,1484,1495,1498,1499,1504,1505,1539,1608,1678,1679,1689,1698,1699,1701,1705,1708,1711,1714,1717,1720,1723,1726,1730,1733,1736,1739,1743,1746,1750,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1782,1784,1785,1786,1787,1788,1789,1790,1791,1793,1794,1796,1797,1799,1801,1802,1804,1805,1806,1807,1808,1809,1811,1812,1813,1814,1815,1827,1829,1831,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1848,1849,1850,1851,1852,1853,1854,1856,1860,1922,1923,1924,1925,1926,1927,1931,1932,1933,1934,1936,1938,1940,1942,1944,1945,1946,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1967,1968,1969,1970,1972,1974,1975,1977,1978,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1997,1998,1999,2000,2002,2003,2004,2005,2006,2008,2010,2012,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2075,2150,2153,2156,2159,2173,2179,2221,2224,2253,2280,2289,2353,2730,2740,2778,2806,3060,3084,3090,3096,3117,3241,3261,3267,3271,3277,3395,3431,3497,3517,3572,3584,3610", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,517,581,651,712,787,863,940,1178,1263,1345,1421,1497,1574,1652,1758,1864,1943,2023,2080,2395,2469,2544,2609,2675,2735,2796,2868,2941,3008,3076,3135,3194,3253,3312,3371,3425,3479,3532,3586,3640,3694,3880,3954,4033,4106,4180,4251,4323,4395,4468,4525,4583,4656,4730,4804,4879,4951,5024,5094,5165,5225,5286,5355,5424,5494,5568,5644,5708,5785,5861,5938,6003,6072,6149,6224,6293,6361,6438,6504,6565,6662,6727,6796,6895,6966,7025,7083,7140,7199,7263,7334,7406,7478,7550,7622,7689,7757,7825,7884,7947,8011,8101,8192,8252,8318,8385,8451,8521,8585,8638,8705,8766,8833,8946,9004,9067,9132,9197,9272,9345,9417,9461,9508,9554,9603,9664,9725,9786,9848,9912,9976,10040,10105,10168,10228,10289,10355,10414,10474,10536,10607,10667,11291,11377,11464,11554,11641,11729,11811,11894,11984,13053,13105,13163,13208,13274,13338,13395,13452,16143,16200,16248,16297,16745,17204,17251,17484,18474,18980,19044,19106,19166,19357,19431,19501,19579,19633,19703,19788,19836,19882,19943,20006,20072,20136,20207,20270,20335,20399,20460,20521,20573,20646,20720,20789,20864,20938,21012,21153,27361,29696,29774,29864,29952,30048,30634,31216,31305,31552,31833,32085,32370,32763,33240,33462,33684,33960,34187,34417,34647,34877,35107,35334,35753,35979,36404,36634,37062,37281,37564,37772,37903,38130,38556,38781,39208,39429,39854,39974,40250,40551,40875,41166,41480,41617,41748,41853,42095,42262,42466,42674,42945,43057,43169,43274,43391,43605,43751,43891,43977,44325,44413,44659,45077,45326,45408,45506,46163,46263,46515,46939,47194,47288,47377,47614,49638,49880,49982,50235,52391,63072,64588,75283,76811,78568,79194,79614,80875,82140,82396,82632,83179,83673,84278,84476,85056,86424,86799,86917,87455,87612,87808,88081,88337,88507,88648,88712,89077,89444,90120,90384,90722,91075,91169,91355,91661,91923,92048,92175,92414,92625,92744,92937,93114,93569,93750,93872,94131,94244,94431,94533,94640,94769,95044,95552,96048,96925,97219,97789,97938,98670,98842,98926,99262,99354,101108,106339,111710,111772,112350,112934,113025,113138,113367,113527,113679,113850,114016,114185,114352,114515,114758,114928,115101,115272,115546,115745,115950,116670,116754,116850,116946,117044,117144,117246,117348,117450,117552,117654,117754,117850,117962,118091,118214,118345,118476,118574,118688,118782,118922,119056,119152,119264,119364,119480,119576,119688,119788,119928,120064,120228,120358,120516,120666,120807,120951,121086,121198,121348,121476,121604,121740,121872,122002,122132,122244,123142,123288,123432,123596,123662,123752,123828,123932,124022,124124,124232,124340,124440,124520,124612,124710,124820,124872,124950,125056,125148,125252,125362,125484,125647,129085,129165,129265,129355,129465,129555,129796,129890,129996,130088,130188,130300,130414,130530,130646,130740,130854,130966,131068,131188,131310,131392,131496,131616,131742,131840,131934,132022,132134,132250,132372,132484,132659,132775,132861,132953,133065,133189,133256,133382,133450,133578,133722,133850,133919,134014,134129,134242,134341,134450,134561,134672,134773,134878,134978,135108,135199,135322,135416,135528,135614,135718,135814,135902,136020,136124,136228,136354,136442,136550,136650,136740,136850,136934,137036,137120,137174,137238,137344,137430,137540,137624,140210,142826,142944,143059,143139,143500,143733,145137,145215,146559,147920,148308,151151,161790,162128,163799,165156,173159,173910,174172,174372,174751,179029,179635,179864,180015,180230,182758,183779,186805,187549,189680,190020,191331", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,244,245,246,247,255,265,266,271,290,300,301,302,303,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,391,414,415,416,417,418,434,435,439,443,447,452,458,465,469,473,478,482,486,490,494,498,502,508,512,518,522,528,532,537,541,544,548,554,558,564,568,574,577,581,585,589,593,597,598,599,600,603,606,609,612,616,617,618,619,620,623,625,627,629,634,635,639,645,649,650,652,664,665,669,675,679,680,681,685,712,716,717,721,749,921,947,1118,1144,1175,1183,1189,1205,1227,1232,1237,1247,1256,1265,1269,1276,1295,1302,1303,1312,1315,1318,1322,1326,1330,1333,1334,1339,1344,1354,1359,1366,1372,1373,1376,1380,1385,1387,1389,1392,1395,1397,1401,1404,1411,1414,1417,1421,1423,1427,1429,1431,1433,1437,1445,1453,1465,1471,1480,1483,1494,1497,1498,1503,1504,1509,1607,1677,1678,1688,1697,1698,1700,1704,1707,1710,1713,1716,1719,1722,1725,1729,1732,1735,1738,1742,1745,1749,1753,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1783,1784,1785,1786,1787,1788,1789,1790,1792,1793,1795,1796,1798,1800,1801,1803,1804,1805,1806,1807,1808,1810,1811,1812,1813,1814,1815,1828,1830,1832,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1847,1848,1849,1850,1851,1852,1853,1855,1859,1863,1922,1923,1924,1925,1926,1930,1931,1932,1933,1935,1937,1939,1941,1943,1944,1945,1946,1948,1950,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1966,1967,1968,1969,1971,1973,1974,1976,1977,1979,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1998,1999,2001,2002,2003,2004,2005,2007,2009,2011,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2149,2152,2155,2158,2172,2178,2188,2223,2252,2279,2288,2352,2715,2733,2767,2805,2823,3083,3089,3095,3116,3240,3260,3266,3270,3276,3311,3406,3496,3516,3571,3583,3609,3616", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,395,450,512,576,646,707,782,858,935,1013,1258,1340,1416,1492,1569,1647,1753,1859,1938,2018,2075,2133,2464,2539,2604,2670,2730,2791,2863,2936,3003,3071,3130,3189,3248,3307,3366,3420,3474,3527,3581,3635,3689,3743,3949,4028,4101,4175,4246,4318,4390,4463,4520,4578,4651,4725,4799,4874,4946,5019,5089,5160,5220,5281,5350,5419,5489,5563,5639,5703,5780,5856,5933,5998,6067,6144,6219,6288,6356,6433,6499,6560,6657,6722,6791,6890,6961,7020,7078,7135,7194,7258,7329,7401,7473,7545,7617,7684,7752,7820,7879,7942,8006,8096,8187,8247,8313,8380,8446,8516,8580,8633,8700,8761,8828,8941,8999,9062,9127,9192,9267,9340,9412,9456,9503,9549,9598,9659,9720,9781,9843,9907,9971,10035,10100,10163,10223,10284,10350,10409,10469,10531,10602,10662,10730,11372,11459,11549,11636,11724,11806,11889,11979,12070,13100,13158,13203,13269,13333,13390,13447,13501,16195,16243,16292,16343,16774,17246,17295,17525,18501,19039,19101,19161,19218,19426,19496,19574,19628,19698,19783,19831,19877,19938,20001,20067,20131,20202,20265,20330,20394,20455,20516,20568,20641,20715,20784,20859,20933,21007,21148,21218,27409,29769,29859,29947,30043,30133,31211,31300,31547,31828,32080,32365,32758,33235,33457,33679,33955,34182,34412,34642,34872,35102,35329,35748,35974,36399,36629,37057,37276,37559,37767,37898,38125,38551,38776,39203,39424,39849,39969,40245,40546,40870,41161,41475,41612,41743,41848,42090,42257,42461,42669,42940,43052,43164,43269,43386,43600,43746,43886,43972,44320,44408,44654,45072,45321,45403,45501,46158,46258,46510,46934,47189,47283,47372,47609,49633,49875,49977,50230,52386,63067,64583,75278,76806,78563,79189,79609,80870,82135,82391,82627,83174,83668,84273,84471,85051,86419,86794,86912,87450,87607,87803,88076,88332,88502,88643,88707,89072,89439,90115,90379,90717,91070,91164,91350,91656,91918,92043,92170,92409,92620,92739,92932,93109,93564,93745,93867,94126,94239,94426,94528,94635,94764,95039,95547,96043,96920,97214,97784,97933,98665,98837,98921,99257,99349,99627,106334,111705,111767,112345,112929,113020,113133,113362,113522,113674,113845,114011,114180,114347,114510,114753,114923,115096,115267,115541,115740,115945,116275,116749,116845,116941,117039,117139,117241,117343,117445,117547,117649,117749,117845,117957,118086,118209,118340,118471,118569,118683,118777,118917,119051,119147,119259,119359,119475,119571,119683,119783,119923,120059,120223,120353,120511,120661,120802,120946,121081,121193,121343,121471,121599,121735,121867,121997,122127,122239,122379,123283,123427,123565,123657,123747,123823,123927,124017,124119,124227,124335,124435,124515,124607,124705,124815,124867,124945,125051,125143,125247,125357,125479,125642,125799,129160,129260,129350,129460,129550,129791,129885,129991,130083,130183,130295,130409,130525,130641,130735,130849,130961,131063,131183,131305,131387,131491,131611,131737,131835,131929,132017,132129,132245,132367,132479,132654,132770,132856,132948,133060,133184,133251,133377,133445,133573,133717,133845,133914,134009,134124,134237,134336,134445,134556,134667,134768,134873,134973,135103,135194,135317,135411,135523,135609,135713,135809,135897,136015,136119,136223,136349,136437,136545,136645,136735,136845,136929,137031,137115,137169,137233,137339,137425,137535,137619,137739,142821,142939,143054,143134,143495,143728,144245,145210,146554,147915,148303,151146,161199,161920,163493,165151,165723,173905,174167,174367,174746,179024,179630,179859,180010,180225,181308,183065,186800,187544,189675,190015,191326,191529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8b019702394fcc443b4eceaa0ea78d5c\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2189,2205,2211,3407,3423", "startColumns": "4,4,4,4,4", "startOffsets": "144250,144675,144853,183070,183481", "endLines": "2204,2210,2220,3422,3426", "endColumns": "24,24,24,24,24", "endOffsets": "144670,144848,145132,183476,183603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4123e3f849b24d389d1cca28d989a749\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "334", "startColumns": "4", "startOffsets": "21340", "endColumns": "82", "endOffsets": "21418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b73cca706b8829b4950fb697f5ac7552\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "294", "startColumns": "4", "startOffsets": "18656", "endColumns": "42", "endOffsets": "18694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92b001d3df7e2cf8f19fe0630b26b2a0\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "412,413", "startColumns": "4,4", "startOffsets": "29585,29641", "endColumns": "55,54", "endOffsets": "29636,29691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac7076343e9dc5d8b75e982d33fd7fa0\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,260,261,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,305,335,336,337,338,339,340,341,404,1816,1817,1821,1822,1826,2063,2064,2734,2768,2824,2857,3021,3054", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1018,1090,2138,2203,3748,3817,10803,10873,10941,11013,11083,11144,11218,12075,12136,12197,12259,12323,12385,12446,12514,12614,12674,12740,12813,12882,12939,12991,13506,13578,13654,16979,17014,17530,17585,17648,17703,17761,17819,17880,17943,18000,18051,18101,18162,18219,18285,18319,18354,19287,21423,21490,21562,21631,21700,21774,21846,28708,122384,122501,122702,122812,123013,139630,139702,161925,163498,165728,167459,172310,172992", "endLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,260,261,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,305,335,336,337,338,339,340,341,404,1816,1820,1821,1825,1826,2063,2064,2739,2777,2856,2877,3053,3059", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1085,1173,2198,2264,3812,3875,10868,10936,11008,11078,11139,11213,11286,12131,12192,12254,12318,12380,12441,12509,12609,12669,12735,12808,12877,12934,12986,13048,13573,13649,13714,17009,17044,17580,17643,17698,17756,17814,17875,17938,17995,18046,18096,18157,18214,18280,18314,18349,18384,19352,21485,21557,21626,21695,21769,21841,21929,28774,122496,122697,122807,123008,123137,139697,139764,162123,163794,167454,168135,172987,173154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\18ad34e2f7d6f6ce1d71ed3d731a13e9\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "258,264", "startColumns": "4,4", "startOffsets": "16884,17137", "endColumns": "53,66", "endOffsets": "16933,17199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa67e21413141b19b8d4c6e1aa699226\\transformed\\drawee-3.2.0\\res\\values\\values.xml", "from": {"startLines": "2,136", "startColumns": "4,4", "startOffsets": "55,3906", "endLines": "135,218", "endColumns": "22,22", "endOffsets": "3901,5346"}, "to": {"startLines": "2887,3312", "startColumns": "4,4", "startOffsets": "168459,181313", "endLines": "3020,3394", "endColumns": "22,22", "endOffsets": "172305,182753"}}]}, {"outputFile": "expo.modules.mymodule.my-module-mergeReleaseResources-51:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45ec1cf61ca13cfcbcc6941817029dcc\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,248,249,250,254,256,299,368,369,371,372,373,377,378,383,384,387,388,392,396,399,401,406,407,409,1525,1528,1531", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13883,13942,14001,14061,14121,14181,14241,14301,14361,14421,14481,14541,14601,14660,14720,14780,14840,14900,14960,15020,15080,15140,15200,15260,15319,15379,15439,15498,15557,15616,15675,15734,16348,16422,16480,16694,16779,18927,25077,25142,25366,25432,25533,26045,26097,26663,26725,27021,27071,27414,27894,28235,28376,28962,29009,29144,100493,100605,100716", "endLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,248,249,250,254,256,299,368,369,371,372,373,377,378,383,384,387,388,392,396,399,401,406,407,409,1527,1530,1534", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "13937,13996,14056,14116,14176,14236,14296,14356,14416,14476,14536,14596,14655,14715,14775,14835,14895,14955,15015,15075,15135,15195,15255,15314,15374,15434,15493,15552,15611,15670,15729,15788,16417,16475,16530,16740,16829,18975,25137,25191,25427,25528,25586,26092,26152,26720,26774,27066,27120,27455,27935,28272,28411,29004,29040,29229,100600,100711,100906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19ff53384693d0f6beef34db290cc9dc\\transformed\\activity-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "268,295", "startColumns": "4,4", "startOffsets": "17343,18699", "endColumns": "41,59", "endOffsets": "17380,18754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\277b49928061acbe92dd621a1b8ff2f4\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "252,270,298,2878,2883", "startColumns": "4,4,4,4,4", "startOffsets": "16571,17419,18863,168140,168310", "endLines": "252,270,298,2882,2886", "endColumns": "56,64,63,24,24", "endOffsets": "16623,17479,18922,168305,168454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a146bbdae329663c4aced4888a0edf6\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "253", "startColumns": "4", "startOffsets": "16628", "endColumns": "65", "endOffsets": "16689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8bfd4e134c9e519eaff7d824602c80c5\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "18759", "endColumns": "53", "endOffsets": "18808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46ed95a07aaabdffd864bffdea2e8e32\\transformed\\react-android-0.76.7-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,82,86,90,93,97,101,105,108,111,112,113,122,129,136,139,142,145,151,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,163,226,277,331,390,438,487,536,584,633,691,740,776,826,867,911,955,998,1032,1071,1117,1165,1219,1267,1331,1448,1571,1667,1787,1907,2009,2149,2271,2381,2488,2591,2702,2871,3039,3156,3275,3388,3574,3682,3795,3886,3997,4166,4264,4389,4484,4591,4761,4859,5042,5215,5327,5428,5587,5721,5861,5963,6068,6199,6368,6485,6633,6778,6928,7027,7123,7319,7502,7601,7785,7952,8200,8448,8716,8901,9103,9309,9506,9707,9896,9922,9957,10495,10913,11291,11468,11647,11830,12195,12392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,81,85,89,92,96,100,104,107,110,111,112,121,128,135,138,141,144,150,153,163", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,35,49,40,43,43,42,33,38,45,47,53,47,63,116,122,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "158,221,272,326,385,433,482,531,579,628,686,735,771,821,862,906,950,993,1027,1066,1112,1160,1214,1262,1326,1443,1566,1662,1782,1902,2004,2144,2266,2376,2483,2586,2697,2866,3034,3151,3270,3383,3569,3677,3790,3881,3992,4161,4259,4384,4479,4586,4756,4854,5037,5210,5322,5423,5582,5716,5856,5958,6063,6194,6363,6480,6628,6773,6923,7022,7118,7314,7497,7596,7780,7947,8195,8443,8711,8896,9098,9304,9501,9702,9891,9917,9952,10490,10908,11286,11463,11642,11825,12190,12387,12828"}, "to": {"startLines": "32,33,202,203,204,237,238,239,240,241,242,243,251,257,259,262,263,267,269,288,289,291,292,293,304,333,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,370,374,375,376,379,380,381,382,385,386,389,390,393,394,395,397,398,400,402,403,405,408,410,411,419,423,1510,1514,1517,1521,1535,1754,1757,1833,1880,1881,1890,1897,1904,1907,1910,1913,1919,2065", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2269,2332,13719,13770,13824,15793,15841,15890,15939,15987,16036,16094,16535,16834,16938,17049,17093,17300,17385,18389,18428,18506,18554,18608,19223,21223,21934,22057,22153,22273,22393,22495,22635,22757,22867,22974,23077,23188,23357,23525,23642,23761,23874,24060,24168,24281,24372,24483,24652,24750,24875,24970,25196,25591,25689,25872,26157,26269,26370,26529,26779,26919,27125,27230,27460,27629,27746,27940,28085,28277,28416,28512,28779,29045,29234,29418,30138,30386,99632,99900,100085,100287,100911,116280,116481,123570,126615,126650,127188,127606,127984,128161,128340,128523,128888,139769", "endLines": "32,33,202,203,204,237,238,239,240,241,242,243,251,257,259,262,263,267,269,288,289,291,292,293,304,333,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,370,374,375,376,379,380,381,382,385,386,389,390,393,394,395,397,398,400,402,403,405,408,410,411,422,426,1513,1516,1520,1524,1538,1756,1759,1833,1880,1889,1896,1903,1906,1909,1912,1918,1921,2074", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,35,49,40,43,43,42,33,38,45,47,53,47,63,116,122,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "2327,2390,13765,13819,13878,15836,15885,15934,15982,16031,16089,16138,16566,16879,16974,17088,17132,17338,17414,18423,18469,18549,18603,18651,19282,21335,22052,22148,22268,22388,22490,22630,22752,22862,22969,23072,23183,23352,23520,23637,23756,23869,24055,24163,24276,24367,24478,24647,24745,24870,24965,25072,25361,25684,25867,26040,26264,26365,26524,26658,26914,27016,27225,27356,27624,27741,27889,28080,28230,28371,28507,28703,28957,29139,29413,29580,30381,30629,99895,100080,100282,100488,101103,116476,116665,123591,126645,127183,127601,127979,128156,128335,128518,128883,129080,140205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\837aa0b22b89b14f7150be2e7705724f\\transformed\\autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "159,1864,2029,2030,2037,2042,2047,2054,2716", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10735,125804,137744,137804,138186,138466,138748,139132,161204", "endLines": "159,1879,2029,2036,2041,2046,2053,2062,2729", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "10798,126610,137799,138181,138461,138743,139127,139625,161785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c83e05e892ffdecb0732be449d87380a\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3427", "startColumns": "4", "startOffsets": "183608", "endLines": "3430", "endColumns": "24", "endOffsets": "183774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\91344d32faf30e676e4e5f395154711e\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "297", "startColumns": "4", "startOffsets": "18813", "endColumns": "49", "endOffsets": "18858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\69a44a0644e0c0b6fca06fc8288a055a\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,244,245,246,247,255,265,266,271,290,300,301,302,303,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,391,414,415,416,417,418,427,435,436,440,444,448,453,459,466,470,474,479,483,487,491,495,499,503,509,513,519,523,529,533,538,542,545,549,555,559,565,569,575,578,582,586,590,594,598,599,600,601,604,607,610,613,617,618,619,620,621,624,626,628,630,635,636,640,646,650,651,653,665,666,670,676,680,681,682,686,713,717,718,722,750,922,948,1119,1145,1176,1184,1190,1206,1228,1233,1238,1248,1257,1266,1270,1277,1296,1303,1304,1313,1316,1319,1323,1327,1331,1334,1335,1340,1345,1355,1360,1367,1373,1374,1377,1381,1386,1388,1390,1393,1396,1398,1402,1405,1412,1415,1418,1422,1424,1428,1430,1432,1434,1438,1446,1454,1466,1472,1481,1484,1495,1498,1499,1504,1505,1539,1608,1678,1679,1689,1698,1699,1701,1705,1708,1711,1714,1717,1720,1723,1726,1730,1733,1736,1739,1743,1746,1750,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1782,1784,1785,1786,1787,1788,1789,1790,1791,1793,1794,1796,1797,1799,1801,1802,1804,1805,1806,1807,1808,1809,1811,1812,1813,1814,1815,1827,1829,1831,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1848,1849,1850,1851,1852,1853,1854,1856,1860,1922,1923,1924,1925,1926,1927,1931,1932,1933,1934,1936,1938,1940,1942,1944,1945,1946,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1967,1968,1969,1970,1972,1974,1975,1977,1978,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1997,1998,1999,2000,2002,2003,2004,2005,2006,2008,2010,2012,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2075,2150,2153,2156,2159,2173,2179,2221,2224,2253,2280,2289,2353,2730,2740,2778,2806,3060,3084,3090,3096,3117,3241,3261,3267,3271,3277,3395,3431,3497,3517,3572,3584,3610", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,517,581,651,712,787,863,940,1178,1263,1345,1421,1497,1574,1652,1758,1864,1943,2023,2080,2395,2469,2544,2609,2675,2735,2796,2868,2941,3008,3076,3135,3194,3253,3312,3371,3425,3479,3532,3586,3640,3694,3880,3954,4033,4106,4180,4251,4323,4395,4468,4525,4583,4656,4730,4804,4879,4951,5024,5094,5165,5225,5286,5355,5424,5494,5568,5644,5708,5785,5861,5938,6003,6072,6149,6224,6293,6361,6438,6504,6565,6662,6727,6796,6895,6966,7025,7083,7140,7199,7263,7334,7406,7478,7550,7622,7689,7757,7825,7884,7947,8011,8101,8192,8252,8318,8385,8451,8521,8585,8638,8705,8766,8833,8946,9004,9067,9132,9197,9272,9345,9417,9461,9508,9554,9603,9664,9725,9786,9848,9912,9976,10040,10105,10168,10228,10289,10355,10414,10474,10536,10607,10667,11291,11377,11464,11554,11641,11729,11811,11894,11984,13053,13105,13163,13208,13274,13338,13395,13452,16143,16200,16248,16297,16745,17204,17251,17484,18474,18980,19044,19106,19166,19357,19431,19501,19579,19633,19703,19788,19836,19882,19943,20006,20072,20136,20207,20270,20335,20399,20460,20521,20573,20646,20720,20789,20864,20938,21012,21153,27361,29696,29774,29864,29952,30048,30634,31216,31305,31552,31833,32085,32370,32763,33240,33462,33684,33960,34187,34417,34647,34877,35107,35334,35753,35979,36404,36634,37062,37281,37564,37772,37903,38130,38556,38781,39208,39429,39854,39974,40250,40551,40875,41166,41480,41617,41748,41853,42095,42262,42466,42674,42945,43057,43169,43274,43391,43605,43751,43891,43977,44325,44413,44659,45077,45326,45408,45506,46163,46263,46515,46939,47194,47288,47377,47614,49638,49880,49982,50235,52391,63072,64588,75283,76811,78568,79194,79614,80875,82140,82396,82632,83179,83673,84278,84476,85056,86424,86799,86917,87455,87612,87808,88081,88337,88507,88648,88712,89077,89444,90120,90384,90722,91075,91169,91355,91661,91923,92048,92175,92414,92625,92744,92937,93114,93569,93750,93872,94131,94244,94431,94533,94640,94769,95044,95552,96048,96925,97219,97789,97938,98670,98842,98926,99262,99354,101108,106339,111710,111772,112350,112934,113025,113138,113367,113527,113679,113850,114016,114185,114352,114515,114758,114928,115101,115272,115546,115745,115950,116670,116754,116850,116946,117044,117144,117246,117348,117450,117552,117654,117754,117850,117962,118091,118214,118345,118476,118574,118688,118782,118922,119056,119152,119264,119364,119480,119576,119688,119788,119928,120064,120228,120358,120516,120666,120807,120951,121086,121198,121348,121476,121604,121740,121872,122002,122132,122244,123142,123288,123432,123596,123662,123752,123828,123932,124022,124124,124232,124340,124440,124520,124612,124710,124820,124872,124950,125056,125148,125252,125362,125484,125647,129085,129165,129265,129355,129465,129555,129796,129890,129996,130088,130188,130300,130414,130530,130646,130740,130854,130966,131068,131188,131310,131392,131496,131616,131742,131840,131934,132022,132134,132250,132372,132484,132659,132775,132861,132953,133065,133189,133256,133382,133450,133578,133722,133850,133919,134014,134129,134242,134341,134450,134561,134672,134773,134878,134978,135108,135199,135322,135416,135528,135614,135718,135814,135902,136020,136124,136228,136354,136442,136550,136650,136740,136850,136934,137036,137120,137174,137238,137344,137430,137540,137624,140210,142826,142944,143059,143139,143500,143733,145137,145215,146559,147920,148308,151151,161790,162128,163799,165156,173159,173910,174172,174372,174751,179029,179635,179864,180015,180230,182758,183779,186805,187549,189680,190020,191331", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,244,245,246,247,255,265,266,271,290,300,301,302,303,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,391,414,415,416,417,418,434,435,439,443,447,452,458,465,469,473,478,482,486,490,494,498,502,508,512,518,522,528,532,537,541,544,548,554,558,564,568,574,577,581,585,589,593,597,598,599,600,603,606,609,612,616,617,618,619,620,623,625,627,629,634,635,639,645,649,650,652,664,665,669,675,679,680,681,685,712,716,717,721,749,921,947,1118,1144,1175,1183,1189,1205,1227,1232,1237,1247,1256,1265,1269,1276,1295,1302,1303,1312,1315,1318,1322,1326,1330,1333,1334,1339,1344,1354,1359,1366,1372,1373,1376,1380,1385,1387,1389,1392,1395,1397,1401,1404,1411,1414,1417,1421,1423,1427,1429,1431,1433,1437,1445,1453,1465,1471,1480,1483,1494,1497,1498,1503,1504,1509,1607,1677,1678,1688,1697,1698,1700,1704,1707,1710,1713,1716,1719,1722,1725,1729,1732,1735,1738,1742,1745,1749,1753,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1783,1784,1785,1786,1787,1788,1789,1790,1792,1793,1795,1796,1798,1800,1801,1803,1804,1805,1806,1807,1808,1810,1811,1812,1813,1814,1815,1828,1830,1832,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1847,1848,1849,1850,1851,1852,1853,1855,1859,1863,1922,1923,1924,1925,1926,1930,1931,1932,1933,1935,1937,1939,1941,1943,1944,1945,1946,1948,1950,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1966,1967,1968,1969,1971,1973,1974,1976,1977,1979,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1998,1999,2001,2002,2003,2004,2005,2007,2009,2011,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2149,2152,2155,2158,2172,2178,2188,2223,2252,2279,2288,2352,2715,2733,2767,2805,2823,3083,3089,3095,3116,3240,3260,3266,3270,3276,3311,3406,3496,3516,3571,3583,3609,3616", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,395,450,512,576,646,707,782,858,935,1013,1258,1340,1416,1492,1569,1647,1753,1859,1938,2018,2075,2133,2464,2539,2604,2670,2730,2791,2863,2936,3003,3071,3130,3189,3248,3307,3366,3420,3474,3527,3581,3635,3689,3743,3949,4028,4101,4175,4246,4318,4390,4463,4520,4578,4651,4725,4799,4874,4946,5019,5089,5160,5220,5281,5350,5419,5489,5563,5639,5703,5780,5856,5933,5998,6067,6144,6219,6288,6356,6433,6499,6560,6657,6722,6791,6890,6961,7020,7078,7135,7194,7258,7329,7401,7473,7545,7617,7684,7752,7820,7879,7942,8006,8096,8187,8247,8313,8380,8446,8516,8580,8633,8700,8761,8828,8941,8999,9062,9127,9192,9267,9340,9412,9456,9503,9549,9598,9659,9720,9781,9843,9907,9971,10035,10100,10163,10223,10284,10350,10409,10469,10531,10602,10662,10730,11372,11459,11549,11636,11724,11806,11889,11979,12070,13100,13158,13203,13269,13333,13390,13447,13501,16195,16243,16292,16343,16774,17246,17295,17525,18501,19039,19101,19161,19218,19426,19496,19574,19628,19698,19783,19831,19877,19938,20001,20067,20131,20202,20265,20330,20394,20455,20516,20568,20641,20715,20784,20859,20933,21007,21148,21218,27409,29769,29859,29947,30043,30133,31211,31300,31547,31828,32080,32365,32758,33235,33457,33679,33955,34182,34412,34642,34872,35102,35329,35748,35974,36399,36629,37057,37276,37559,37767,37898,38125,38551,38776,39203,39424,39849,39969,40245,40546,40870,41161,41475,41612,41743,41848,42090,42257,42461,42669,42940,43052,43164,43269,43386,43600,43746,43886,43972,44320,44408,44654,45072,45321,45403,45501,46158,46258,46510,46934,47189,47283,47372,47609,49633,49875,49977,50230,52386,63067,64583,75278,76806,78563,79189,79609,80870,82135,82391,82627,83174,83668,84273,84471,85051,86419,86794,86912,87450,87607,87803,88076,88332,88502,88643,88707,89072,89439,90115,90379,90717,91070,91164,91350,91656,91918,92043,92170,92409,92620,92739,92932,93109,93564,93745,93867,94126,94239,94426,94528,94635,94764,95039,95547,96043,96920,97214,97784,97933,98665,98837,98921,99257,99349,99627,106334,111705,111767,112345,112929,113020,113133,113362,113522,113674,113845,114011,114180,114347,114510,114753,114923,115096,115267,115541,115740,115945,116275,116749,116845,116941,117039,117139,117241,117343,117445,117547,117649,117749,117845,117957,118086,118209,118340,118471,118569,118683,118777,118917,119051,119147,119259,119359,119475,119571,119683,119783,119923,120059,120223,120353,120511,120661,120802,120946,121081,121193,121343,121471,121599,121735,121867,121997,122127,122239,122379,123283,123427,123565,123657,123747,123823,123927,124017,124119,124227,124335,124435,124515,124607,124705,124815,124867,124945,125051,125143,125247,125357,125479,125642,125799,129160,129260,129350,129460,129550,129791,129885,129991,130083,130183,130295,130409,130525,130641,130735,130849,130961,131063,131183,131305,131387,131491,131611,131737,131835,131929,132017,132129,132245,132367,132479,132654,132770,132856,132948,133060,133184,133251,133377,133445,133573,133717,133845,133914,134009,134124,134237,134336,134445,134556,134667,134768,134873,134973,135103,135194,135317,135411,135523,135609,135713,135809,135897,136015,136119,136223,136349,136437,136545,136645,136735,136845,136929,137031,137115,137169,137233,137339,137425,137535,137619,137739,142821,142939,143054,143134,143495,143728,144245,145210,146554,147915,148303,151146,161199,161920,163493,165151,165723,173905,174167,174367,174746,179024,179630,179859,180010,180225,181308,183065,186800,187544,189675,190015,191326,191529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8b019702394fcc443b4eceaa0ea78d5c\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2189,2205,2211,3407,3423", "startColumns": "4,4,4,4,4", "startOffsets": "144250,144675,144853,183070,183481", "endLines": "2204,2210,2220,3422,3426", "endColumns": "24,24,24,24,24", "endOffsets": "144670,144848,145132,183476,183603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4123e3f849b24d389d1cca28d989a749\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "334", "startColumns": "4", "startOffsets": "21340", "endColumns": "82", "endOffsets": "21418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b73cca706b8829b4950fb697f5ac7552\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "294", "startColumns": "4", "startOffsets": "18656", "endColumns": "42", "endOffsets": "18694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92b001d3df7e2cf8f19fe0630b26b2a0\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "412,413", "startColumns": "4,4", "startOffsets": "29585,29641", "endColumns": "55,54", "endOffsets": "29636,29691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac7076343e9dc5d8b75e982d33fd7fa0\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,260,261,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,305,335,336,337,338,339,340,341,404,1816,1817,1821,1822,1826,2063,2064,2734,2768,2824,2857,3021,3054", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1018,1090,2138,2203,3748,3817,10803,10873,10941,11013,11083,11144,11218,12075,12136,12197,12259,12323,12385,12446,12514,12614,12674,12740,12813,12882,12939,12991,13506,13578,13654,16979,17014,17530,17585,17648,17703,17761,17819,17880,17943,18000,18051,18101,18162,18219,18285,18319,18354,19287,21423,21490,21562,21631,21700,21774,21846,28708,122384,122501,122702,122812,123013,139630,139702,161925,163498,165728,167459,172310,172992", "endLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,260,261,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,305,335,336,337,338,339,340,341,404,1816,1820,1821,1825,1826,2063,2064,2739,2777,2856,2877,3053,3059", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1085,1173,2198,2264,3812,3875,10868,10936,11008,11078,11139,11213,11286,12131,12192,12254,12318,12380,12441,12509,12609,12669,12735,12808,12877,12934,12986,13048,13573,13649,13714,17009,17044,17580,17643,17698,17756,17814,17875,17938,17995,18046,18096,18157,18214,18280,18314,18349,18384,19352,21485,21557,21626,21695,21769,21841,21929,28774,122496,122697,122807,123008,123137,139697,139764,162123,163794,167454,168135,172987,173154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\18ad34e2f7d6f6ce1d71ed3d731a13e9\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "258,264", "startColumns": "4,4", "startOffsets": "16884,17137", "endColumns": "53,66", "endOffsets": "16933,17199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa67e21413141b19b8d4c6e1aa699226\\transformed\\drawee-3.2.0\\res\\values\\values.xml", "from": {"startLines": "2,136", "startColumns": "4,4", "startOffsets": "55,3906", "endLines": "135,218", "endColumns": "22,22", "endOffsets": "3901,5346"}, "to": {"startLines": "2887,3312", "startColumns": "4,4", "startOffsets": "168459,181313", "endLines": "3020,3394", "endColumns": "22,22", "endOffsets": "172305,182753"}}]}]}