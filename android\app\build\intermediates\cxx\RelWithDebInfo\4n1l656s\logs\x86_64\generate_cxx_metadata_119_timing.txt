# C/C++ build system timings
generate_cxx_metadata
  [gap of 42ms]
  create-invalidation-state 41ms
generate_cxx_metadata completed in 90ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 295ms]
  create-invalidation-state 171ms
  generate-prefab-packages
    exec-prefab 528ms
    [gap of 124ms]
  generate-prefab-packages completed in 655ms
  execute-generate-process
    exec-configure 1141ms
    [gap of 638ms]
  execute-generate-process completed in 1781ms
  remove-unexpected-so-files 54ms
  [gap of 133ms]
generate_cxx_metadata completed in 3117ms

